import { NextConfig } from "next";
import { Configuration } from "webpack";

const nextConfig: NextConfig = {
  webpack: (
    config: Configuration,
    options: { isServer: boolean; dev: boolean }
  ) => {
    // const { isServer } = options;
    config.plugins?.push(
      new (require("@module-federation/nextjs-mf").NextFederationPlugin)({
        name: "authApp",
        filename: "static/chunks/remoteEntry.js",
        exposes: {
          "./Login": "./pages/login.tsx",
        },
        shared: {
          react: {
            singleton: true,
            eager: true,
            requiredVersion: false,
          },
          "react-dom": {
            singleton: true,
            eager: true,
            requiredVersion: false,
          },
          "react/jsx-runtime": {
            singleton: true,
            eager: true,
            requiredVersion: false,
          },
          "react/jsx-dev-runtime": {
            singleton: true,
            eager: true,
            requiredVersion: false,
          },
          "next/head": {
            singleton: true,
            eager: true,
            requiredVersion: false,
          },
        },
      })
    );
    return config;
  },
};

export default nextConfig;
