(()=>{var e={325:(e,r,t)=>{var n=t(781);var o=["write","end","destroy"];var i=["resume","pause"];var a=["data","close"];var s=Array.prototype.slice;e.exports=duplex;function forEach(e,r){if(e.forEach){return e.forEach(r)}for(var t=0;t<e.length;t++){r(e[t],t)}}function duplex(e,r){var t=new n;var p=false;forEach(o,proxyWriter);forEach(i,proxyReader);forEach(a,proxyStream);r.on("end",handleEnd);e.on("drain",(function(){t.emit("drain")}));e.on("error",reemit);r.on("error",reemit);t.writable=e.writable;t.readable=r.readable;return t;function proxyWriter(r){t[r]=method;function method(){return e[r].apply(e,arguments)}}function proxyReader(e){t[e]=method;function method(){t.emit(e);var n=r[e];if(n){return n.apply(r,arguments)}r.emit(e)}}function proxyStream(e){r.on(e,reemit);function reemit(){var r=s.call(arguments);r.unshift(e);t.emit.apply(t,r)}}function handleEnd(){if(p){return}p=true;var e=s.call(arguments);e.unshift("end");t.emit.apply(t,e)}function reemit(e){t.emit("error",e)}}},423:(e,r,t)=>{"use strict";const n=t(147);const o=t(781);const i=t(796);const{promisify:a}=t(837);const s=t(325);const getOptions=e=>({level:9,...e});const p=a(i.gzip);e.exports=async(e,r)=>{if(!e){return 0}const t=await p(e,getOptions(r));return t.length};e.exports.sync=(e,r)=>i.gzipSync(e,getOptions(r)).length;e.exports.stream=e=>{const r=new o.PassThrough;const t=new o.PassThrough;const n=s(r,t);let a=0;const p=i.createGzip(getOptions(e)).on("data",(e=>{a+=e.length})).on("error",(()=>{n.gzipSize=0})).on("end",(()=>{n.gzipSize=a;n.emit("gzip-size",a);t.end()}));r.pipe(p);r.pipe(t,{end:false});return n};e.exports.file=(r,t)=>new Promise(((o,i)=>{const a=n.createReadStream(r);a.on("error",i);const s=a.pipe(e.exports.stream(t));s.on("error",i);s.on("gzip-size",o)}));e.exports.fileSync=(r,t)=>e.exports.sync(n.readFileSync(r),t)},147:e=>{"use strict";e.exports=require("fs")},781:e=>{"use strict";e.exports=require("stream")},837:e=>{"use strict";e.exports=require("util")},796:e=>{"use strict";e.exports=require("zlib")}};var r={};function __nccwpck_require__(t){var n=r[t];if(n!==undefined){return n.exports}var o=r[t]={exports:{}};var i=true;try{e[t](o,o.exports,__nccwpck_require__);i=false}finally{if(i)delete r[t]}return o.exports}if(typeof __nccwpck_require__!=="undefined")__nccwpck_require__.ab=__dirname+"/";var t=__nccwpck_require__(423);module.exports=t})();