(()=>{var e={44:(e,t,n)=>{"use strict";e=n.nmd(e);const wrapAnsi16=(e,t)=>(...n)=>{const r=e(...n);return`[${r+t}m`};const wrapAnsi256=(e,t)=>(...n)=>{const r=e(...n);return`[${38+t};5;${r}m`};const wrapAnsi16m=(e,t)=>(...n)=>{const r=e(...n);return`[${38+t};2;${r[0]};${r[1]};${r[2]}m`};const ansi2ansi=e=>e;const rgb2rgb=(e,t,n)=>[e,t,n];const setLazyProperty=(e,t,n)=>{Object.defineProperty(e,t,{get:()=>{const r=n();Object.defineProperty(e,t,{value:r,enumerable:true,configurable:true});return r},enumerable:true,configurable:true})};let r;const makeDynamicStyles=(e,t,s,o)=>{if(r===undefined){r=n(767)}const l=o?10:0;const c={};for(const[n,o]of Object.entries(r)){const r=n==="ansi16"?"ansi":n;if(n===t){c[r]=e(s,l)}else if(typeof o==="object"){c[r]=e(o[t],l)}}return c};function assembleStyles(){const e=new Map;const t={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}};t.color.gray=t.color.blackBright;t.bgColor.bgGray=t.bgColor.bgBlackBright;t.color.grey=t.color.blackBright;t.bgColor.bgGrey=t.bgColor.bgBlackBright;for(const[n,r]of Object.entries(t)){for(const[n,s]of Object.entries(r)){t[n]={open:`[${s[0]}m`,close:`[${s[1]}m`};r[n]=t[n];e.set(s[0],s[1])}Object.defineProperty(t,n,{value:r,enumerable:false})}Object.defineProperty(t,"codes",{value:e,enumerable:false});t.color.close="[39m";t.bgColor.close="[49m";setLazyProperty(t.color,"ansi",(()=>makeDynamicStyles(wrapAnsi16,"ansi16",ansi2ansi,false)));setLazyProperty(t.color,"ansi256",(()=>makeDynamicStyles(wrapAnsi256,"ansi256",ansi2ansi,false)));setLazyProperty(t.color,"ansi16m",(()=>makeDynamicStyles(wrapAnsi16m,"rgb",rgb2rgb,false)));setLazyProperty(t.bgColor,"ansi",(()=>makeDynamicStyles(wrapAnsi16,"ansi16",ansi2ansi,true)));setLazyProperty(t.bgColor,"ansi256",(()=>makeDynamicStyles(wrapAnsi256,"ansi256",ansi2ansi,true)));setLazyProperty(t.bgColor,"ansi16m",(()=>makeDynamicStyles(wrapAnsi16m,"rgb",rgb2rgb,true)));return t}Object.defineProperty(e,"exports",{enumerable:true,get:assembleStyles})},584:(e,t,n)=>{"use strict";const r=n(44);const{stdout:s,stderr:o}=n(242);const{stringReplaceAll:l,stringEncaseCRLFWithFirstIndex:c}=n(905);const{isArray:i}=Array;const a=["ansi","ansi","ansi256","ansi16m"];const u=Object.create(null);const applyOptions=(e,t={})=>{if(t.level&&!(Number.isInteger(t.level)&&t.level>=0&&t.level<=3)){throw new Error("The `level` option should be an integer from 0 to 3")}const n=s?s.level:0;e.level=t.level===undefined?n:t.level};class ChalkClass{constructor(e){return chalkFactory(e)}}const chalkFactory=e=>{const t={};applyOptions(t,e);t.template=(...e)=>chalkTag(t.template,...e);Object.setPrototypeOf(t,Chalk.prototype);Object.setPrototypeOf(t.template,t);t.template.constructor=()=>{throw new Error("`chalk.constructor()` is deprecated. Use `new chalk.Instance()` instead.")};t.template.Instance=ChalkClass;return t.template};function Chalk(e){return chalkFactory(e)}for(const[e,t]of Object.entries(r)){u[e]={get(){const n=createBuilder(this,createStyler(t.open,t.close,this._styler),this._isEmpty);Object.defineProperty(this,e,{value:n});return n}}}u.visible={get(){const e=createBuilder(this,this._styler,true);Object.defineProperty(this,"visible",{value:e});return e}};const h=["rgb","hex","keyword","hsl","hsv","hwb","ansi","ansi256"];for(const e of h){u[e]={get(){const{level:t}=this;return function(...n){const s=createStyler(r.color[a[t]][e](...n),r.color.close,this._styler);return createBuilder(this,s,this._isEmpty)}}}}for(const e of h){const t="bg"+e[0].toUpperCase()+e.slice(1);u[t]={get(){const{level:t}=this;return function(...n){const s=createStyler(r.bgColor[a[t]][e](...n),r.bgColor.close,this._styler);return createBuilder(this,s,this._isEmpty)}}}}const f=Object.defineProperties((()=>{}),{...u,level:{enumerable:true,get(){return this._generator.level},set(e){this._generator.level=e}}});const createStyler=(e,t,n)=>{let r;let s;if(n===undefined){r=e;s=t}else{r=n.openAll+e;s=t+n.closeAll}return{open:e,close:t,openAll:r,closeAll:s,parent:n}};const createBuilder=(e,t,n)=>{const builder=(...e)=>{if(i(e[0])&&i(e[0].raw)){return applyStyle(builder,chalkTag(builder,...e))}return applyStyle(builder,e.length===1?""+e[0]:e.join(" "))};Object.setPrototypeOf(builder,f);builder._generator=e;builder._styler=t;builder._isEmpty=n;return builder};const applyStyle=(e,t)=>{if(e.level<=0||!t){return e._isEmpty?"":t}let n=e._styler;if(n===undefined){return t}const{openAll:r,closeAll:s}=n;if(t.indexOf("")!==-1){while(n!==undefined){t=l(t,n.close,n.open);n=n.parent}}const o=t.indexOf("\n");if(o!==-1){t=c(t,s,r,o)}return r+t+s};let g;const chalkTag=(e,...t)=>{const[r]=t;if(!i(r)||!i(r.raw)){return t.join(" ")}const s=t.slice(1);const o=[r.raw[0]];for(let e=1;e<r.length;e++){o.push(String(s[e-1]).replace(/[{}\\]/g,"\\$&"),String(r.raw[e]))}if(g===undefined){g=n(654)}return g(e,o.join(""))};Object.defineProperties(Chalk.prototype,u);const b=Chalk();b.supportsColor=s;b.stderr=Chalk({level:o?o.level:0});b.stderr.supportsColor=o;e.exports=b},654:e=>{"use strict";const t=/(?:\\(u(?:[a-f\d]{4}|\{[a-f\d]{1,6}\})|x[a-f\d]{2}|.))|(?:\{(~)?(\w+(?:\([^)]*\))?(?:\.\w+(?:\([^)]*\))?)*)(?:[ \t]|(?=\r?\n)))|(\})|((?:.|[\r\n\f])+?)/gi;const n=/(?:^|\.)(\w+)(?:\(([^)]*)\))?/g;const r=/^(['"])((?:\\.|(?!\1)[^\\])*)\1$/;const s=/\\(u(?:[a-f\d]{4}|{[a-f\d]{1,6}})|x[a-f\d]{2}|.)|([^\\])/gi;const o=new Map([["n","\n"],["r","\r"],["t","\t"],["b","\b"],["f","\f"],["v","\v"],["0","\0"],["\\","\\"],["e",""],["a",""]]);function unescape(e){const t=e[0]==="u";const n=e[1]==="{";if(t&&!n&&e.length===5||e[0]==="x"&&e.length===3){return String.fromCharCode(parseInt(e.slice(1),16))}if(t&&n){return String.fromCodePoint(parseInt(e.slice(2,-1),16))}return o.get(e)||e}function parseArguments(e,t){const n=[];const o=t.trim().split(/\s*,\s*/g);let l;for(const t of o){const o=Number(t);if(!Number.isNaN(o)){n.push(o)}else if(l=t.match(r)){n.push(l[2].replace(s,((e,t,n)=>t?unescape(t):n)))}else{throw new Error(`Invalid Chalk template style argument: ${t} (in style '${e}')`)}}return n}function parseStyle(e){n.lastIndex=0;const t=[];let r;while((r=n.exec(e))!==null){const e=r[1];if(r[2]){const n=parseArguments(e,r[2]);t.push([e].concat(n))}else{t.push([e])}}return t}function buildStyle(e,t){const n={};for(const e of t){for(const t of e.styles){n[t[0]]=e.inverse?null:t.slice(1)}}let r=e;for(const[e,t]of Object.entries(n)){if(!Array.isArray(t)){continue}if(!(e in r)){throw new Error(`Unknown Chalk style: ${e}`)}r=t.length>0?r[e](...t):r[e]}return r}e.exports=(e,n)=>{const r=[];const s=[];let o=[];n.replace(t,((t,n,l,c,i,a)=>{if(n){o.push(unescape(n))}else if(c){const t=o.join("");o=[];s.push(r.length===0?t:buildStyle(e,r)(t));r.push({inverse:l,styles:parseStyle(c)})}else if(i){if(r.length===0){throw new Error("Found extraneous } in Chalk template literal")}s.push(buildStyle(e,r)(o.join("")));o=[];r.pop()}else{o.push(a)}}));s.push(o.join(""));if(r.length>0){const e=`Chalk template literal is missing ${r.length} closing bracket${r.length===1?"":"s"} (\`}\`)`;throw new Error(e)}return s.join("")}},905:e=>{"use strict";const stringReplaceAll=(e,t,n)=>{let r=e.indexOf(t);if(r===-1){return e}const s=t.length;let o=0;let l="";do{l+=e.substr(o,r-o)+t+n;o=r+s;r=e.indexOf(t,o)}while(r!==-1);l+=e.substr(o);return l};const stringEncaseCRLFWithFirstIndex=(e,t,n,r)=>{let s=0;let o="";do{const l=e[r-1]==="\r";o+=e.substr(s,(l?r-1:r)-s)+t+(l?"\r\n":"\n")+n;s=r+1;r=e.indexOf("\n",s)}while(r!==-1);o+=e.substr(s);return o};e.exports={stringReplaceAll:stringReplaceAll,stringEncaseCRLFWithFirstIndex:stringEncaseCRLFWithFirstIndex}},226:(e,t,n)=>{const r=n(866);const s={};for(const e of Object.keys(r)){s[r[e]]=e}const o={rgb:{channels:3,labels:"rgb"},hsl:{channels:3,labels:"hsl"},hsv:{channels:3,labels:"hsv"},hwb:{channels:3,labels:"hwb"},cmyk:{channels:4,labels:"cmyk"},xyz:{channels:3,labels:"xyz"},lab:{channels:3,labels:"lab"},lch:{channels:3,labels:"lch"},hex:{channels:1,labels:["hex"]},keyword:{channels:1,labels:["keyword"]},ansi16:{channels:1,labels:["ansi16"]},ansi256:{channels:1,labels:["ansi256"]},hcg:{channels:3,labels:["h","c","g"]},apple:{channels:3,labels:["r16","g16","b16"]},gray:{channels:1,labels:["gray"]}};e.exports=o;for(const e of Object.keys(o)){if(!("channels"in o[e])){throw new Error("missing channels property: "+e)}if(!("labels"in o[e])){throw new Error("missing channel labels property: "+e)}if(o[e].labels.length!==o[e].channels){throw new Error("channel and label counts mismatch: "+e)}const{channels:t,labels:n}=o[e];delete o[e].channels;delete o[e].labels;Object.defineProperty(o[e],"channels",{value:t});Object.defineProperty(o[e],"labels",{value:n})}o.rgb.hsl=function(e){const t=e[0]/255;const n=e[1]/255;const r=e[2]/255;const s=Math.min(t,n,r);const o=Math.max(t,n,r);const l=o-s;let c;let i;if(o===s){c=0}else if(t===o){c=(n-r)/l}else if(n===o){c=2+(r-t)/l}else if(r===o){c=4+(t-n)/l}c=Math.min(c*60,360);if(c<0){c+=360}const a=(s+o)/2;if(o===s){i=0}else if(a<=.5){i=l/(o+s)}else{i=l/(2-o-s)}return[c,i*100,a*100]};o.rgb.hsv=function(e){let t;let n;let r;let s;let o;const l=e[0]/255;const c=e[1]/255;const i=e[2]/255;const a=Math.max(l,c,i);const u=a-Math.min(l,c,i);const diffc=function(e){return(a-e)/6/u+1/2};if(u===0){s=0;o=0}else{o=u/a;t=diffc(l);n=diffc(c);r=diffc(i);if(l===a){s=r-n}else if(c===a){s=1/3+t-r}else if(i===a){s=2/3+n-t}if(s<0){s+=1}else if(s>1){s-=1}}return[s*360,o*100,a*100]};o.rgb.hwb=function(e){const t=e[0];const n=e[1];let r=e[2];const s=o.rgb.hsl(e)[0];const l=1/255*Math.min(t,Math.min(n,r));r=1-1/255*Math.max(t,Math.max(n,r));return[s,l*100,r*100]};o.rgb.cmyk=function(e){const t=e[0]/255;const n=e[1]/255;const r=e[2]/255;const s=Math.min(1-t,1-n,1-r);const o=(1-t-s)/(1-s)||0;const l=(1-n-s)/(1-s)||0;const c=(1-r-s)/(1-s)||0;return[o*100,l*100,c*100,s*100]};function comparativeDistance(e,t){return(e[0]-t[0])**2+(e[1]-t[1])**2+(e[2]-t[2])**2}o.rgb.keyword=function(e){const t=s[e];if(t){return t}let n=Infinity;let o;for(const t of Object.keys(r)){const s=r[t];const l=comparativeDistance(e,s);if(l<n){n=l;o=t}}return o};o.keyword.rgb=function(e){return r[e]};o.rgb.xyz=function(e){let t=e[0]/255;let n=e[1]/255;let r=e[2]/255;t=t>.04045?((t+.055)/1.055)**2.4:t/12.92;n=n>.04045?((n+.055)/1.055)**2.4:n/12.92;r=r>.04045?((r+.055)/1.055)**2.4:r/12.92;const s=t*.4124+n*.3576+r*.1805;const o=t*.2126+n*.7152+r*.0722;const l=t*.0193+n*.1192+r*.9505;return[s*100,o*100,l*100]};o.rgb.lab=function(e){const t=o.rgb.xyz(e);let n=t[0];let r=t[1];let s=t[2];n/=95.047;r/=100;s/=108.883;n=n>.008856?n**(1/3):7.787*n+16/116;r=r>.008856?r**(1/3):7.787*r+16/116;s=s>.008856?s**(1/3):7.787*s+16/116;const l=116*r-16;const c=500*(n-r);const i=200*(r-s);return[l,c,i]};o.hsl.rgb=function(e){const t=e[0]/360;const n=e[1]/100;const r=e[2]/100;let s;let o;let l;if(n===0){l=r*255;return[l,l,l]}if(r<.5){s=r*(1+n)}else{s=r+n-r*n}const c=2*r-s;const i=[0,0,0];for(let e=0;e<3;e++){o=t+1/3*-(e-1);if(o<0){o++}if(o>1){o--}if(6*o<1){l=c+(s-c)*6*o}else if(2*o<1){l=s}else if(3*o<2){l=c+(s-c)*(2/3-o)*6}else{l=c}i[e]=l*255}return i};o.hsl.hsv=function(e){const t=e[0];let n=e[1]/100;let r=e[2]/100;let s=n;const o=Math.max(r,.01);r*=2;n*=r<=1?r:2-r;s*=o<=1?o:2-o;const l=(r+n)/2;const c=r===0?2*s/(o+s):2*n/(r+n);return[t,c*100,l*100]};o.hsv.rgb=function(e){const t=e[0]/60;const n=e[1]/100;let r=e[2]/100;const s=Math.floor(t)%6;const o=t-Math.floor(t);const l=255*r*(1-n);const c=255*r*(1-n*o);const i=255*r*(1-n*(1-o));r*=255;switch(s){case 0:return[r,i,l];case 1:return[c,r,l];case 2:return[l,r,i];case 3:return[l,c,r];case 4:return[i,l,r];case 5:return[r,l,c]}};o.hsv.hsl=function(e){const t=e[0];const n=e[1]/100;const r=e[2]/100;const s=Math.max(r,.01);let o;let l;l=(2-n)*r;const c=(2-n)*s;o=n*s;o/=c<=1?c:2-c;o=o||0;l/=2;return[t,o*100,l*100]};o.hwb.rgb=function(e){const t=e[0]/360;let n=e[1]/100;let r=e[2]/100;const s=n+r;let o;if(s>1){n/=s;r/=s}const l=Math.floor(6*t);const c=1-r;o=6*t-l;if((l&1)!==0){o=1-o}const i=n+o*(c-n);let a;let u;let h;switch(l){default:case 6:case 0:a=c;u=i;h=n;break;case 1:a=i;u=c;h=n;break;case 2:a=n;u=c;h=i;break;case 3:a=n;u=i;h=c;break;case 4:a=i;u=n;h=c;break;case 5:a=c;u=n;h=i;break}return[a*255,u*255,h*255]};o.cmyk.rgb=function(e){const t=e[0]/100;const n=e[1]/100;const r=e[2]/100;const s=e[3]/100;const o=1-Math.min(1,t*(1-s)+s);const l=1-Math.min(1,n*(1-s)+s);const c=1-Math.min(1,r*(1-s)+s);return[o*255,l*255,c*255]};o.xyz.rgb=function(e){const t=e[0]/100;const n=e[1]/100;const r=e[2]/100;let s;let o;let l;s=t*3.2406+n*-1.5372+r*-.4986;o=t*-.9689+n*1.8758+r*.0415;l=t*.0557+n*-.204+r*1.057;s=s>.0031308?1.055*s**(1/2.4)-.055:s*12.92;o=o>.0031308?1.055*o**(1/2.4)-.055:o*12.92;l=l>.0031308?1.055*l**(1/2.4)-.055:l*12.92;s=Math.min(Math.max(0,s),1);o=Math.min(Math.max(0,o),1);l=Math.min(Math.max(0,l),1);return[s*255,o*255,l*255]};o.xyz.lab=function(e){let t=e[0];let n=e[1];let r=e[2];t/=95.047;n/=100;r/=108.883;t=t>.008856?t**(1/3):7.787*t+16/116;n=n>.008856?n**(1/3):7.787*n+16/116;r=r>.008856?r**(1/3):7.787*r+16/116;const s=116*n-16;const o=500*(t-n);const l=200*(n-r);return[s,o,l]};o.lab.xyz=function(e){const t=e[0];const n=e[1];const r=e[2];let s;let o;let l;o=(t+16)/116;s=n/500+o;l=o-r/200;const c=o**3;const i=s**3;const a=l**3;o=c>.008856?c:(o-16/116)/7.787;s=i>.008856?i:(s-16/116)/7.787;l=a>.008856?a:(l-16/116)/7.787;s*=95.047;o*=100;l*=108.883;return[s,o,l]};o.lab.lch=function(e){const t=e[0];const n=e[1];const r=e[2];let s;const o=Math.atan2(r,n);s=o*360/2/Math.PI;if(s<0){s+=360}const l=Math.sqrt(n*n+r*r);return[t,l,s]};o.lch.lab=function(e){const t=e[0];const n=e[1];const r=e[2];const s=r/360*2*Math.PI;const o=n*Math.cos(s);const l=n*Math.sin(s);return[t,o,l]};o.rgb.ansi16=function(e,t=null){const[n,r,s]=e;let l=t===null?o.rgb.hsv(e)[2]:t;l=Math.round(l/50);if(l===0){return 30}let c=30+(Math.round(s/255)<<2|Math.round(r/255)<<1|Math.round(n/255));if(l===2){c+=60}return c};o.hsv.ansi16=function(e){return o.rgb.ansi16(o.hsv.rgb(e),e[2])};o.rgb.ansi256=function(e){const t=e[0];const n=e[1];const r=e[2];if(t===n&&n===r){if(t<8){return 16}if(t>248){return 231}return Math.round((t-8)/247*24)+232}const s=16+36*Math.round(t/255*5)+6*Math.round(n/255*5)+Math.round(r/255*5);return s};o.ansi16.rgb=function(e){let t=e%10;if(t===0||t===7){if(e>50){t+=3.5}t=t/10.5*255;return[t,t,t]}const n=(~~(e>50)+1)*.5;const r=(t&1)*n*255;const s=(t>>1&1)*n*255;const o=(t>>2&1)*n*255;return[r,s,o]};o.ansi256.rgb=function(e){if(e>=232){const t=(e-232)*10+8;return[t,t,t]}e-=16;let t;const n=Math.floor(e/36)/5*255;const r=Math.floor((t=e%36)/6)/5*255;const s=t%6/5*255;return[n,r,s]};o.rgb.hex=function(e){const t=((Math.round(e[0])&255)<<16)+((Math.round(e[1])&255)<<8)+(Math.round(e[2])&255);const n=t.toString(16).toUpperCase();return"000000".substring(n.length)+n};o.hex.rgb=function(e){const t=e.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);if(!t){return[0,0,0]}let n=t[0];if(t[0].length===3){n=n.split("").map((e=>e+e)).join("")}const r=parseInt(n,16);const s=r>>16&255;const o=r>>8&255;const l=r&255;return[s,o,l]};o.rgb.hcg=function(e){const t=e[0]/255;const n=e[1]/255;const r=e[2]/255;const s=Math.max(Math.max(t,n),r);const o=Math.min(Math.min(t,n),r);const l=s-o;let c;let i;if(l<1){c=o/(1-l)}else{c=0}if(l<=0){i=0}else if(s===t){i=(n-r)/l%6}else if(s===n){i=2+(r-t)/l}else{i=4+(t-n)/l}i/=6;i%=1;return[i*360,l*100,c*100]};o.hsl.hcg=function(e){const t=e[1]/100;const n=e[2]/100;const r=n<.5?2*t*n:2*t*(1-n);let s=0;if(r<1){s=(n-.5*r)/(1-r)}return[e[0],r*100,s*100]};o.hsv.hcg=function(e){const t=e[1]/100;const n=e[2]/100;const r=t*n;let s=0;if(r<1){s=(n-r)/(1-r)}return[e[0],r*100,s*100]};o.hcg.rgb=function(e){const t=e[0]/360;const n=e[1]/100;const r=e[2]/100;if(n===0){return[r*255,r*255,r*255]}const s=[0,0,0];const o=t%1*6;const l=o%1;const c=1-l;let i=0;switch(Math.floor(o)){case 0:s[0]=1;s[1]=l;s[2]=0;break;case 1:s[0]=c;s[1]=1;s[2]=0;break;case 2:s[0]=0;s[1]=1;s[2]=l;break;case 3:s[0]=0;s[1]=c;s[2]=1;break;case 4:s[0]=l;s[1]=0;s[2]=1;break;default:s[0]=1;s[1]=0;s[2]=c}i=(1-n)*r;return[(n*s[0]+i)*255,(n*s[1]+i)*255,(n*s[2]+i)*255]};o.hcg.hsv=function(e){const t=e[1]/100;const n=e[2]/100;const r=t+n*(1-t);let s=0;if(r>0){s=t/r}return[e[0],s*100,r*100]};o.hcg.hsl=function(e){const t=e[1]/100;const n=e[2]/100;const r=n*(1-t)+.5*t;let s=0;if(r>0&&r<.5){s=t/(2*r)}else if(r>=.5&&r<1){s=t/(2*(1-r))}return[e[0],s*100,r*100]};o.hcg.hwb=function(e){const t=e[1]/100;const n=e[2]/100;const r=t+n*(1-t);return[e[0],(r-t)*100,(1-r)*100]};o.hwb.hcg=function(e){const t=e[1]/100;const n=e[2]/100;const r=1-n;const s=r-t;let o=0;if(s<1){o=(r-s)/(1-s)}return[e[0],s*100,o*100]};o.apple.rgb=function(e){return[e[0]/65535*255,e[1]/65535*255,e[2]/65535*255]};o.rgb.apple=function(e){return[e[0]/255*65535,e[1]/255*65535,e[2]/255*65535]};o.gray.rgb=function(e){return[e[0]/100*255,e[0]/100*255,e[0]/100*255]};o.gray.hsl=function(e){return[0,0,e[0]]};o.gray.hsv=o.gray.hsl;o.gray.hwb=function(e){return[0,100,e[0]]};o.gray.cmyk=function(e){return[0,0,0,e[0]]};o.gray.lab=function(e){return[e[0],0,0]};o.gray.hex=function(e){const t=Math.round(e[0]/100*255)&255;const n=(t<<16)+(t<<8)+t;const r=n.toString(16).toUpperCase();return"000000".substring(r.length)+r};o.rgb.gray=function(e){const t=(e[0]+e[1]+e[2])/3;return[t/255*100]}},767:(e,t,n)=>{const r=n(226);const s=n(392);const o={};const l=Object.keys(r);function wrapRaw(e){const wrappedFn=function(...t){const n=t[0];if(n===undefined||n===null){return n}if(n.length>1){t=n}return e(t)};if("conversion"in e){wrappedFn.conversion=e.conversion}return wrappedFn}function wrapRounded(e){const wrappedFn=function(...t){const n=t[0];if(n===undefined||n===null){return n}if(n.length>1){t=n}const r=e(t);if(typeof r==="object"){for(let e=r.length,t=0;t<e;t++){r[t]=Math.round(r[t])}}return r};if("conversion"in e){wrappedFn.conversion=e.conversion}return wrappedFn}l.forEach((e=>{o[e]={};Object.defineProperty(o[e],"channels",{value:r[e].channels});Object.defineProperty(o[e],"labels",{value:r[e].labels});const t=s(e);const n=Object.keys(t);n.forEach((n=>{const r=t[n];o[e][n]=wrapRounded(r);o[e][n].raw=wrapRaw(r)}))}));e.exports=o},392:(e,t,n)=>{const r=n(226);function buildGraph(){const e={};const t=Object.keys(r);for(let n=t.length,r=0;r<n;r++){e[t[r]]={distance:-1,parent:null}}return e}function deriveBFS(e){const t=buildGraph();const n=[e];t[e].distance=0;while(n.length){const e=n.pop();const s=Object.keys(r[e]);for(let r=s.length,o=0;o<r;o++){const r=s[o];const l=t[r];if(l.distance===-1){l.distance=t[e].distance+1;l.parent=e;n.unshift(r)}}}return t}function link(e,t){return function(n){return t(e(n))}}function wrapConversion(e,t){const n=[t[e].parent,e];let s=r[t[e].parent][e];let o=t[e].parent;while(t[o].parent){n.unshift(t[o].parent);s=link(r[t[o].parent][o],s);o=t[o].parent}s.conversion=n;return s}e.exports=function(e){const t=deriveBFS(e);const n={};const r=Object.keys(t);for(let e=r.length,s=0;s<e;s++){const e=r[s];const o=t[e];if(o.parent===null){continue}n[e]=wrapConversion(e,t)}return n}},866:e=>{"use strict";e.exports={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]}},975:e=>{"use strict";e.exports=(e,t=process.argv)=>{const n=e.startsWith("-")?"":e.length===1?"-":"--";const r=t.indexOf(n+e);const s=t.indexOf("--");return r!==-1&&(s===-1||r<s)}},242:(e,t,n)=>{"use strict";const r=n(37);const s=n(224);const o=n(975);const{env:l}=process;let c;if(o("no-color")||o("no-colors")||o("color=false")||o("color=never")){c=0}else if(o("color")||o("colors")||o("color=true")||o("color=always")){c=1}if("FORCE_COLOR"in l){if(l.FORCE_COLOR==="true"){c=1}else if(l.FORCE_COLOR==="false"){c=0}else{c=l.FORCE_COLOR.length===0?1:Math.min(parseInt(l.FORCE_COLOR,10),3)}}function translateLevel(e){if(e===0){return false}return{level:e,hasBasic:true,has256:e>=2,has16m:e>=3}}function supportsColor(e,t){if(c===0){return 0}if(o("color=16m")||o("color=full")||o("color=truecolor")){return 3}if(o("color=256")){return 2}if(e&&!t&&c===undefined){return 0}const n=c||0;if(l.TERM==="dumb"){return n}if(process.platform==="win32"){const e=r.release().split(".");if(Number(e[0])>=10&&Number(e[2])>=10586){return Number(e[2])>=14931?3:2}return 1}if("CI"in l){if(["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some((e=>e in l))||l.CI_NAME==="codeship"){return 1}return n}if("TEAMCITY_VERSION"in l){return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(l.TEAMCITY_VERSION)?1:0}if(l.COLORTERM==="truecolor"){return 3}if("TERM_PROGRAM"in l){const e=parseInt((l.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(l.TERM_PROGRAM){case"iTerm.app":return e>=3?3:2;case"Apple_Terminal":return 2}}if(/-256(color)?$/i.test(l.TERM)){return 2}if(/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(l.TERM)){return 1}if("COLORTERM"in l){return 1}return n}function getSupportLevel(e){const t=supportsColor(e,e&&e.isTTY);return translateLevel(t)}e.exports={supportsColor:getSupportLevel,stdout:translateLevel(supportsColor(true,s.isatty(1))),stderr:translateLevel(supportsColor(true,s.isatty(2)))}},37:e=>{"use strict";e.exports=require("os")},224:e=>{"use strict";e.exports=require("tty")}};var t={};function __nccwpck_require__(n){var r=t[n];if(r!==undefined){return r.exports}var s=t[n]={id:n,loaded:false,exports:{}};var o=true;try{e[n](s,s.exports,__nccwpck_require__);o=false}finally{if(o)delete t[n]}s.loaded=true;return s.exports}(()=>{__nccwpck_require__.nmd=e=>{e.paths=[];if(!e.children)e.children=[];return e}})();if(typeof __nccwpck_require__!=="undefined")__nccwpck_require__.ab=__dirname+"/";var n=__nccwpck_require__(584);module.exports=n})();