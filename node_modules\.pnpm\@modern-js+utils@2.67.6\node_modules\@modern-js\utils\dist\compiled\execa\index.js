(()=>{var e={328:(e,t,n)=>{"use strict";const r=n(81);const o=n(529);const s=n(170);function spawn(e,t,n){const i=o(e,t,n);const a=r.spawn(i.command,i.args,i.options);s.hookChildProcess(a,i);return a}function spawnSync(e,t,n){const i=o(e,t,n);const a=r.spawnSync(i.command,i.args,i.options);a.error=a.error||s.verifyENOENTSync(a.status,i);return a}e.exports=spawn;e.exports.spawn=spawn;e.exports.sync=spawnSync;e.exports._parse=o;e.exports._enoent=s},170:e=>{"use strict";const t=process.platform==="win32";function notFoundError(e,t){return Object.assign(new Error(`${t} ${e.command} ENOENT`),{code:"ENOENT",errno:"ENOENT",syscall:`${t} ${e.command}`,path:e.command,spawnargs:e.args})}function hookChildProcess(e,n){if(!t){return}const r=e.emit;e.emit=function(t,o){if(t==="exit"){const t=verifyENOENT(o,n,"spawn");if(t){return r.call(e,"error",t)}}return r.apply(e,arguments)}}function verifyENOENT(e,n){if(t&&e===1&&!n.file){return notFoundError(n.original,"spawn")}return null}function verifyENOENTSync(e,n){if(t&&e===1&&!n.file){return notFoundError(n.original,"spawnSync")}return null}e.exports={hookChildProcess:hookChildProcess,verifyENOENT:verifyENOENT,verifyENOENTSync:verifyENOENTSync,notFoundError:notFoundError}},529:(e,t,n)=>{"use strict";const r=n(17);const o=n(499);const s=n(24);const i=n(91);const a=process.platform==="win32";const c=/\.(?:com|exe)$/i;const d=/node_modules[\\/].bin[\\/][^\\/]+\.cmd$/i;function detectShebang(e){e.file=o(e);const t=e.file&&i(e.file);if(t){e.args.unshift(e.file);e.command=t;return o(e)}return e.file}function parseNonShell(e){if(!a){return e}const t=detectShebang(e);const n=!c.test(t);if(e.options.forceShell||n){const n=d.test(t);e.command=r.normalize(e.command);e.command=s.command(e.command);e.args=e.args.map((e=>s.argument(e,n)));const o=[e.command].concat(e.args).join(" ");e.args=["/d","/s","/c",`"${o}"`];e.command=process.env.comspec||"cmd.exe";e.options.windowsVerbatimArguments=true}return e}function parse(e,t,n){if(t&&!Array.isArray(t)){n=t;t=null}t=t?t.slice(0):[];n=Object.assign({},n);const r={command:e,args:t,options:n,file:undefined,original:{command:e,args:t}};return n.shell?r:parseNonShell(r)}e.exports=parse},24:e=>{"use strict";const t=/([()\][%!^"`<>&|;, *?])/g;function escapeCommand(e){e=e.replace(t,"^$1");return e}function escapeArgument(e,n){e=`${e}`;e=e.replace(/(\\*)"/g,'$1$1\\"');e=e.replace(/(\\*)$/,"$1$1");e=`"${e}"`;e=e.replace(t,"^$1");if(n){e=e.replace(t,"^$1")}return e}e.exports.command=escapeCommand;e.exports.argument=escapeArgument},91:(e,t,n)=>{"use strict";const r=n(147);const o=n(256);function readShebang(e){const t=150;const n=Buffer.alloc(t);let s;try{s=r.openSync(e,"r");r.readSync(s,n,0,t,0);r.closeSync(s)}catch(e){}return o(n.toString())}e.exports=readShebang},499:(e,t,n)=>{"use strict";const r=n(17);const o=n(85);const s=n(460);function resolveCommandAttempt(e,t){const n=e.options.env||process.env;const i=process.cwd();const a=e.options.cwd!=null;const c=a&&process.chdir!==undefined&&!process.chdir.disabled;if(c){try{process.chdir(e.options.cwd)}catch(e){}}let d;try{d=o.sync(e.command,{path:n[s({env:n})],pathExt:t?r.delimiter:undefined})}catch(e){}finally{if(c){process.chdir(i)}}if(d){d=r.resolve(a?e.options.cwd:"",d)}return d}function resolveCommand(e){return resolveCommandAttempt(e)||resolveCommandAttempt(e,true)}e.exports=resolveCommand},632:(e,t,n)=>{"use strict";const r=n(17);const o=n(81);const s=n(328);const i=n(133);const a=n(136);const c=n(591);const d=n(324);const u=n(166);const{spawnedKill:l,spawnedCancel:f,setupTimeout:p,validateTimeout:m,setExitHandler:g}=n(878);const{handleInput:x,getSpawnedResult:h,makeAllStream:y,validateInputSync:b}=n(533);const{mergePromise:S,getSpawnedPromise:v}=n(210);const{joinCommand:w,parseCommand:E,getEscapedCommand:I}=n(192);const T=1e3*1e3*100;const getEnv=({env:e,extendEnv:t,preferLocal:n,localDir:r,execPath:o})=>{const s=t?{...process.env,...e}:e;if(n){return a.env({env:s,cwd:r,execPath:o})}return s};const handleArguments=(e,t,n={})=>{const o=s._parse(e,t,n);e=o.command;t=o.args;n=o.options;n={maxBuffer:T,buffer:true,stripFinalNewline:true,extendEnv:true,preferLocal:false,localDir:n.cwd||process.cwd(),execPath:process.execPath,encoding:"utf8",reject:true,cleanup:true,all:false,windowsHide:true,...n};n.env=getEnv(n);n.stdio=u(n);if(process.platform==="win32"&&r.basename(e,".exe")==="cmd"){t.unshift("/q")}return{file:e,args:t,options:n,parsed:o}};const handleOutput=(e,t,n)=>{if(typeof t!=="string"&&!Buffer.isBuffer(t)){return n===undefined?undefined:""}if(e.stripFinalNewline){return i(t)}return t};const execa=(e,t,n)=>{const r=handleArguments(e,t,n);const s=w(e,t);const i=I(e,t);m(r.options);let a;try{a=o.spawn(r.file,r.args,r.options)}catch(e){const t=new o.ChildProcess;const n=Promise.reject(d({error:e,stdout:"",stderr:"",all:"",command:s,escapedCommand:i,parsed:r,timedOut:false,isCanceled:false,killed:false}));return S(t,n)}const u=v(a);const b=p(a,r.options,u);const E=g(a,r.options,b);const T={isCanceled:false};a.kill=l.bind(null,a.kill.bind(a));a.cancel=f.bind(null,a,T);const handlePromise=async()=>{const[{error:e,exitCode:t,signal:n,timedOut:o},c,u,l]=await h(a,r.options,E);const f=handleOutput(r.options,c);const p=handleOutput(r.options,u);const m=handleOutput(r.options,l);if(e||t!==0||n!==null){const c=d({error:e,exitCode:t,signal:n,stdout:f,stderr:p,all:m,command:s,escapedCommand:i,parsed:r,timedOut:o,isCanceled:T.isCanceled,killed:a.killed});if(!r.options.reject){return c}throw c}return{command:s,escapedCommand:i,exitCode:0,stdout:f,stderr:p,all:m,failed:false,timedOut:false,isCanceled:false,killed:false}};const C=c(handlePromise);x(a,r.options.input);a.all=y(a,r.options);return S(a,C)};e.exports=execa;e.exports.sync=(e,t,n)=>{const r=handleArguments(e,t,n);const s=w(e,t);const i=I(e,t);b(r.options);let a;try{a=o.spawnSync(r.file,r.args,r.options)}catch(e){throw d({error:e,stdout:"",stderr:"",all:"",command:s,escapedCommand:i,parsed:r,timedOut:false,isCanceled:false,killed:false})}const c=handleOutput(r.options,a.stdout,a.error);const u=handleOutput(r.options,a.stderr,a.error);if(a.error||a.status!==0||a.signal!==null){const e=d({stdout:c,stderr:u,error:a.error,signal:a.signal,exitCode:a.status,command:s,escapedCommand:i,parsed:r,timedOut:a.error&&a.error.code==="ETIMEDOUT",isCanceled:false,killed:a.signal!==null});if(!r.options.reject){return e}throw e}return{command:s,escapedCommand:i,exitCode:0,stdout:c,stderr:u,failed:false,timedOut:false,isCanceled:false,killed:false}};e.exports.command=(e,t)=>{const[n,...r]=E(e);return execa(n,r,t)};e.exports.commandSync=(e,t)=>{const[n,...r]=E(e);return execa.sync(n,r,t)};e.exports.node=(e,t,n={})=>{if(t&&!Array.isArray(t)&&typeof t==="object"){n=t;t=[]}const r=u.node(n);const o=process.execArgv.filter((e=>!e.startsWith("--inspect")));const{nodePath:s=process.execPath,nodeOptions:i=o}=n;return execa(s,[...i,e,...Array.isArray(t)?t:[]],{...n,stdin:undefined,stdout:undefined,stderr:undefined,stdio:r,shell:false})}},192:e=>{"use strict";const normalizeArgs=(e,t=[])=>{if(!Array.isArray(t)){return[e]}return[e,...t]};const t=/^[\w.-]+$/;const n=/"/g;const escapeArg=e=>{if(typeof e!=="string"||t.test(e)){return e}return`"${e.replace(n,'\\"')}"`};const joinCommand=(e,t)=>normalizeArgs(e,t).join(" ");const getEscapedCommand=(e,t)=>normalizeArgs(e,t).map((e=>escapeArg(e))).join(" ");const r=/ +/g;const parseCommand=e=>{const t=[];for(const n of e.trim().split(r)){const e=t[t.length-1];if(e&&e.endsWith("\\")){t[t.length-1]=`${e.slice(0,-1)} ${n}`}else{t.push(n)}}return t};e.exports={joinCommand:joinCommand,getEscapedCommand:getEscapedCommand,parseCommand:parseCommand}},324:(e,t,n)=>{"use strict";const{signalsByName:r}=n(882);const getErrorPrefix=({timedOut:e,timeout:t,errorCode:n,signal:r,signalDescription:o,exitCode:s,isCanceled:i})=>{if(e){return`timed out after ${t} milliseconds`}if(i){return"was canceled"}if(n!==undefined){return`failed with ${n}`}if(r!==undefined){return`was killed with ${r} (${o})`}if(s!==undefined){return`failed with exit code ${s}`}return"failed"};const makeError=({stdout:e,stderr:t,all:n,error:o,signal:s,exitCode:i,command:a,escapedCommand:c,timedOut:d,isCanceled:u,killed:l,parsed:{options:{timeout:f}}})=>{i=i===null?undefined:i;s=s===null?undefined:s;const p=s===undefined?undefined:r[s].description;const m=o&&o.code;const g=getErrorPrefix({timedOut:d,timeout:f,errorCode:m,signal:s,signalDescription:p,exitCode:i,isCanceled:u});const x=`Command ${g}: ${a}`;const h=Object.prototype.toString.call(o)==="[object Error]";const y=h?`${x}\n${o.message}`:x;const b=[y,t,e].filter(Boolean).join("\n");if(h){o.originalMessage=o.message;o.message=b}else{o=new Error(b)}o.shortMessage=y;o.command=a;o.escapedCommand=c;o.exitCode=i;o.signal=s;o.signalDescription=p;o.stdout=e;o.stderr=t;if(n!==undefined){o.all=n}if("bufferedData"in o){delete o.bufferedData}o.failed=true;o.timedOut=Boolean(d);o.isCanceled=u;o.killed=l&&!d;return o};e.exports=makeError},878:(e,t,n)=>{"use strict";const r=n(37);const o=n(580);const s=1e3*5;const spawnedKill=(e,t="SIGTERM",n={})=>{const r=e(t);setKillTimeout(e,t,n,r);return r};const setKillTimeout=(e,t,n,r)=>{if(!shouldForceKill(t,n,r)){return}const o=getForceKillAfterTimeout(n);const s=setTimeout((()=>{e("SIGKILL")}),o);if(s.unref){s.unref()}};const shouldForceKill=(e,{forceKillAfterTimeout:t},n)=>isSigterm(e)&&t!==false&&n;const isSigterm=e=>e===r.constants.signals.SIGTERM||typeof e==="string"&&e.toUpperCase()==="SIGTERM";const getForceKillAfterTimeout=({forceKillAfterTimeout:e=true})=>{if(e===true){return s}if(!Number.isFinite(e)||e<0){throw new TypeError(`Expected the \`forceKillAfterTimeout\` option to be a non-negative integer, got \`${e}\` (${typeof e})`)}return e};const spawnedCancel=(e,t)=>{const n=e.kill();if(n){t.isCanceled=true}};const timeoutKill=(e,t,n)=>{e.kill(t);n(Object.assign(new Error("Timed out"),{timedOut:true,signal:t}))};const setupTimeout=(e,{timeout:t,killSignal:n="SIGTERM"},r)=>{if(t===0||t===undefined){return r}let o;const s=new Promise(((r,s)=>{o=setTimeout((()=>{timeoutKill(e,n,s)}),t)}));const i=r.finally((()=>{clearTimeout(o)}));return Promise.race([s,i])};const validateTimeout=({timeout:e})=>{if(e!==undefined&&(!Number.isFinite(e)||e<0)){throw new TypeError(`Expected the \`timeout\` option to be a non-negative integer, got \`${e}\` (${typeof e})`)}};const setExitHandler=async(e,{cleanup:t,detached:n},r)=>{if(!t||n){return r}const s=o((()=>{e.kill()}));return r.finally((()=>{s()}))};e.exports={spawnedKill:spawnedKill,spawnedCancel:spawnedCancel,setupTimeout:setupTimeout,validateTimeout:validateTimeout,setExitHandler:setExitHandler}},210:e=>{"use strict";const t=(async()=>{})().constructor.prototype;const n=["then","catch","finally"].map((e=>[e,Reflect.getOwnPropertyDescriptor(t,e)]));const mergePromise=(e,t)=>{for(const[r,o]of n){const n=typeof t==="function"?(...e)=>Reflect.apply(o.value,t(),e):o.value.bind(t);Reflect.defineProperty(e,r,{...o,value:n})}return e};const getSpawnedPromise=e=>new Promise(((t,n)=>{e.on("exit",((e,n)=>{t({exitCode:e,signal:n})}));e.on("error",(e=>{n(e)}));if(e.stdin){e.stdin.on("error",(e=>{n(e)}))}}));e.exports={mergePromise:mergePromise,getSpawnedPromise:getSpawnedPromise}},166:e=>{"use strict";const t=["stdin","stdout","stderr"];const hasAlias=e=>t.some((t=>e[t]!==undefined));const normalizeStdio=e=>{if(!e){return}const{stdio:n}=e;if(n===undefined){return t.map((t=>e[t]))}if(hasAlias(e)){throw new Error(`It's not possible to provide \`stdio\` in combination with one of ${t.map((e=>`\`${e}\``)).join(", ")}`)}if(typeof n==="string"){return n}if(!Array.isArray(n)){throw new TypeError(`Expected \`stdio\` to be of type \`string\` or \`Array\`, got \`${typeof n}\``)}const r=Math.max(n.length,t.length);return Array.from({length:r},((e,t)=>n[t]))};e.exports=normalizeStdio;e.exports.node=e=>{const t=normalizeStdio(e);if(t==="ipc"){return"ipc"}if(t===undefined||typeof t==="string"){return[t,t,t,"ipc"]}if(t.includes("ipc")){return t}return[...t,"ipc"]}},533:(e,t,n)=>{"use strict";const r=n(434);const o=n(981);const s=n(853);const handleInput=(e,t)=>{if(t===undefined||e.stdin===undefined){return}if(r(t)){t.pipe(e.stdin)}else{e.stdin.end(t)}};const makeAllStream=(e,{all:t})=>{if(!t||!e.stdout&&!e.stderr){return}const n=s();if(e.stdout){n.add(e.stdout)}if(e.stderr){n.add(e.stderr)}return n};const getBufferedData=async(e,t)=>{if(!e){return}e.destroy();try{return await t}catch(e){return e.bufferedData}};const getStreamPromise=(e,{encoding:t,buffer:n,maxBuffer:r})=>{if(!e||!n){return}if(t){return o(e,{encoding:t,maxBuffer:r})}return o.buffer(e,{maxBuffer:r})};const getSpawnedResult=async({stdout:e,stderr:t,all:n},{encoding:r,buffer:o,maxBuffer:s},i)=>{const a=getStreamPromise(e,{encoding:r,buffer:o,maxBuffer:s});const c=getStreamPromise(t,{encoding:r,buffer:o,maxBuffer:s});const d=getStreamPromise(n,{encoding:r,buffer:o,maxBuffer:s*2});try{return await Promise.all([i,a,c,d])}catch(r){return Promise.all([{error:r,signal:r.signal,timedOut:r.timedOut},getBufferedData(e,a),getBufferedData(t,c),getBufferedData(n,d)])}};const validateInputSync=({input:e})=>{if(r(e)){throw new TypeError("The `input` option cannot be a stream in sync mode")}};e.exports={handleInput:handleInput,makeAllStream:makeAllStream,getSpawnedResult:getSpawnedResult,validateInputSync:validateInputSync}},782:(e,t,n)=>{"use strict";const{PassThrough:r}=n(781);e.exports=e=>{e={...e};const{array:t}=e;let{encoding:n}=e;const o=n==="buffer";let s=false;if(t){s=!(n||o)}else{n=n||"utf8"}if(o){n=null}const i=new r({objectMode:s});if(n){i.setEncoding(n)}let a=0;const c=[];i.on("data",(e=>{c.push(e);if(s){a=c.length}else{a+=e.length}}));i.getBufferedValue=()=>{if(t){return c}return o?Buffer.concat(c,a):c.join("")};i.getBufferedLength=()=>a;return i}},981:(e,t,n)=>{"use strict";const{constants:r}=n(300);const o=n(781);const{promisify:s}=n(837);const i=n(782);const a=s(o.pipeline);class MaxBufferError extends Error{constructor(){super("maxBuffer exceeded");this.name="MaxBufferError"}}async function getStream(e,t){if(!e){throw new Error("Expected a stream")}t={maxBuffer:Infinity,...t};const{maxBuffer:n}=t;const o=i(t);await new Promise(((t,s)=>{const rejectPromise=e=>{if(e&&o.getBufferedLength()<=r.MAX_LENGTH){e.bufferedData=o.getBufferedValue()}s(e)};(async()=>{try{await a(e,o);t()}catch(e){rejectPromise(e)}})();o.on("data",(()=>{if(o.getBufferedLength()>n){rejectPromise(new MaxBufferError)}}))}));return o.getBufferedValue()}e.exports=getStream;e.exports.buffer=(e,t)=>getStream(e,{...t,encoding:"buffer"});e.exports.array=(e,t)=>getStream(e,{...t,array:true});e.exports.MaxBufferError=MaxBufferError},221:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:true});t.SIGNALS=void 0;const n=[{name:"SIGHUP",number:1,action:"terminate",description:"Terminal closed",standard:"posix"},{name:"SIGINT",number:2,action:"terminate",description:"User interruption with CTRL-C",standard:"ansi"},{name:"SIGQUIT",number:3,action:"core",description:"User interruption with CTRL-\\",standard:"posix"},{name:"SIGILL",number:4,action:"core",description:"Invalid machine instruction",standard:"ansi"},{name:"SIGTRAP",number:5,action:"core",description:"Debugger breakpoint",standard:"posix"},{name:"SIGABRT",number:6,action:"core",description:"Aborted",standard:"ansi"},{name:"SIGIOT",number:6,action:"core",description:"Aborted",standard:"bsd"},{name:"SIGBUS",number:7,action:"core",description:"Bus error due to misaligned, non-existing address or paging error",standard:"bsd"},{name:"SIGEMT",number:7,action:"terminate",description:"Command should be emulated but is not implemented",standard:"other"},{name:"SIGFPE",number:8,action:"core",description:"Floating point arithmetic error",standard:"ansi"},{name:"SIGKILL",number:9,action:"terminate",description:"Forced termination",standard:"posix",forced:true},{name:"SIGUSR1",number:10,action:"terminate",description:"Application-specific signal",standard:"posix"},{name:"SIGSEGV",number:11,action:"core",description:"Segmentation fault",standard:"ansi"},{name:"SIGUSR2",number:12,action:"terminate",description:"Application-specific signal",standard:"posix"},{name:"SIGPIPE",number:13,action:"terminate",description:"Broken pipe or socket",standard:"posix"},{name:"SIGALRM",number:14,action:"terminate",description:"Timeout or timer",standard:"posix"},{name:"SIGTERM",number:15,action:"terminate",description:"Termination",standard:"ansi"},{name:"SIGSTKFLT",number:16,action:"terminate",description:"Stack is empty or overflowed",standard:"other"},{name:"SIGCHLD",number:17,action:"ignore",description:"Child process terminated, paused or unpaused",standard:"posix"},{name:"SIGCLD",number:17,action:"ignore",description:"Child process terminated, paused or unpaused",standard:"other"},{name:"SIGCONT",number:18,action:"unpause",description:"Unpaused",standard:"posix",forced:true},{name:"SIGSTOP",number:19,action:"pause",description:"Paused",standard:"posix",forced:true},{name:"SIGTSTP",number:20,action:"pause",description:'Paused using CTRL-Z or "suspend"',standard:"posix"},{name:"SIGTTIN",number:21,action:"pause",description:"Background process cannot read terminal input",standard:"posix"},{name:"SIGBREAK",number:21,action:"terminate",description:"User interruption with CTRL-BREAK",standard:"other"},{name:"SIGTTOU",number:22,action:"pause",description:"Background process cannot write to terminal output",standard:"posix"},{name:"SIGURG",number:23,action:"ignore",description:"Socket received out-of-band data",standard:"bsd"},{name:"SIGXCPU",number:24,action:"core",description:"Process timed out",standard:"bsd"},{name:"SIGXFSZ",number:25,action:"core",description:"File too big",standard:"bsd"},{name:"SIGVTALRM",number:26,action:"terminate",description:"Timeout or timer",standard:"bsd"},{name:"SIGPROF",number:27,action:"terminate",description:"Timeout or timer",standard:"bsd"},{name:"SIGWINCH",number:28,action:"ignore",description:"Terminal window size changed",standard:"bsd"},{name:"SIGIO",number:29,action:"terminate",description:"I/O is available",standard:"other"},{name:"SIGPOLL",number:29,action:"terminate",description:"Watched event",standard:"other"},{name:"SIGINFO",number:29,action:"ignore",description:"Request for process information",standard:"other"},{name:"SIGPWR",number:30,action:"terminate",description:"Device running out of power",standard:"systemv"},{name:"SIGSYS",number:31,action:"core",description:"Invalid system call",standard:"other"},{name:"SIGUNUSED",number:31,action:"terminate",description:"Invalid system call",standard:"other"}];t.SIGNALS=n},882:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:true});t.signalsByNumber=t.signalsByName=void 0;var r=n(37);var o=n(922);var s=n(840);const getSignalsByName=function(){const e=(0,o.getSignals)();return e.reduce(getSignalByName,{})};const getSignalByName=function(e,{name:t,number:n,description:r,supported:o,action:s,forced:i,standard:a}){return{...e,[t]:{name:t,number:n,description:r,supported:o,action:s,forced:i,standard:a}}};const i=getSignalsByName();t.signalsByName=i;const getSignalsByNumber=function(){const e=(0,o.getSignals)();const t=s.SIGRTMAX+1;const n=Array.from({length:t},((t,n)=>getSignalByNumber(n,e)));return Object.assign({},...n)};const getSignalByNumber=function(e,t){const n=findSignalByNumber(e,t);if(n===undefined){return{}}const{name:r,description:o,supported:s,action:i,forced:a,standard:c}=n;return{[e]:{name:r,number:e,description:o,supported:s,action:i,forced:a,standard:c}}};const findSignalByNumber=function(e,t){const n=t.find((({name:t})=>r.constants.signals[t]===e));if(n!==undefined){return n}return t.find((t=>t.number===e))};const a=getSignalsByNumber();t.signalsByNumber=a},840:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:true});t.SIGRTMAX=t.getRealtimeSignals=void 0;const getRealtimeSignals=function(){const e=r-n+1;return Array.from({length:e},getRealtimeSignal)};t.getRealtimeSignals=getRealtimeSignals;const getRealtimeSignal=function(e,t){return{name:`SIGRT${t+1}`,number:n+t,action:"terminate",description:"Application-specific signal (realtime)",standard:"posix"}};const n=34;const r=64;t.SIGRTMAX=r},922:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:true});t.getSignals=void 0;var r=n(37);var o=n(221);var s=n(840);const getSignals=function(){const e=(0,s.getRealtimeSignals)();const t=[...o.SIGNALS,...e].map(normalizeSignal);return t};t.getSignals=getSignals;const normalizeSignal=function({name:e,number:t,description:n,action:o,forced:s=false,standard:i}){const{signals:{[e]:a}}=r.constants;const c=a!==undefined;const d=c?a:t;return{name:e,number:d,description:n,supported:c,action:o,forced:s,standard:i}}},434:e=>{"use strict";const isStream=e=>e!==null&&typeof e==="object"&&typeof e.pipe==="function";isStream.writable=e=>isStream(e)&&e.writable!==false&&typeof e._write==="function"&&typeof e._writableState==="object";isStream.readable=e=>isStream(e)&&e.readable!==false&&typeof e._read==="function"&&typeof e._readableState==="object";isStream.duplex=e=>isStream.writable(e)&&isStream.readable(e);isStream.transform=e=>isStream.duplex(e)&&typeof e._transform==="function";e.exports=isStream},212:(e,t,n)=>{var r=n(147);var o;if(process.platform==="win32"||global.TESTING_WINDOWS){o=n(476)}else{o=n(836)}e.exports=isexe;isexe.sync=sync;function isexe(e,t,n){if(typeof t==="function"){n=t;t={}}if(!n){if(typeof Promise!=="function"){throw new TypeError("callback not provided")}return new Promise((function(n,r){isexe(e,t||{},(function(e,t){if(e){r(e)}else{n(t)}}))}))}o(e,t||{},(function(e,r){if(e){if(e.code==="EACCES"||t&&t.ignoreErrors){e=null;r=false}}n(e,r)}))}function sync(e,t){try{return o.sync(e,t||{})}catch(e){if(t&&t.ignoreErrors||e.code==="EACCES"){return false}else{throw e}}}},836:(e,t,n)=>{e.exports=isexe;isexe.sync=sync;var r=n(147);function isexe(e,t,n){r.stat(e,(function(e,r){n(e,e?false:checkStat(r,t))}))}function sync(e,t){return checkStat(r.statSync(e),t)}function checkStat(e,t){return e.isFile()&&checkMode(e,t)}function checkMode(e,t){var n=e.mode;var r=e.uid;var o=e.gid;var s=t.uid!==undefined?t.uid:process.getuid&&process.getuid();var i=t.gid!==undefined?t.gid:process.getgid&&process.getgid();var a=parseInt("100",8);var c=parseInt("010",8);var d=parseInt("001",8);var u=a|c;var l=n&d||n&c&&o===i||n&a&&r===s||n&u&&s===0;return l}},476:(e,t,n)=>{e.exports=isexe;isexe.sync=sync;var r=n(147);function checkPathExt(e,t){var n=t.pathExt!==undefined?t.pathExt:process.env.PATHEXT;if(!n){return true}n=n.split(";");if(n.indexOf("")!==-1){return true}for(var r=0;r<n.length;r++){var o=n[r].toLowerCase();if(o&&e.substr(-o.length).toLowerCase()===o){return true}}return false}function checkStat(e,t,n){if(!e.isSymbolicLink()&&!e.isFile()){return false}return checkPathExt(t,n)}function isexe(e,t,n){r.stat(e,(function(r,o){n(r,r?false:checkStat(o,e,t))}))}function sync(e,t){return checkStat(r.statSync(e),e,t)}},853:(e,t,n)=>{"use strict";const{PassThrough:r}=n(781);e.exports=function(){var e=[];var t=new r({objectMode:true});t.setMaxListeners(0);t.add=add;t.isEmpty=isEmpty;t.on("unpipe",remove);Array.prototype.slice.call(arguments).forEach(add);return t;function add(n){if(Array.isArray(n)){n.forEach(add);return this}e.push(n);n.once("end",remove.bind(null,n));n.once("error",t.emit.bind(t,"error"));n.pipe(t,{end:false});return this}function isEmpty(){return e.length==0}function remove(n){e=e.filter((function(e){return e!==n}));if(!e.length&&t.readable){t.end()}}}},693:e=>{"use strict";const mimicFn=(e,t)=>{for(const n of Reflect.ownKeys(t)){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}return e};e.exports=mimicFn;e.exports["default"]=mimicFn},136:(e,t,n)=>{"use strict";const r=n(17);const o=n(460);const npmRunPath=e=>{e={cwd:process.cwd(),path:process.env[o()],execPath:process.execPath,...e};let t;let n=r.resolve(e.cwd);const s=[];while(t!==n){s.push(r.join(n,"node_modules/.bin"));t=n;n=r.resolve(n,"..")}const i=r.resolve(e.cwd,e.execPath,"..");s.push(i);return s.concat(e.path).join(r.delimiter)};e.exports=npmRunPath;e.exports["default"]=npmRunPath;e.exports.env=t=>{t={env:process.env,...t};const n={...t.env};const r=o({env:n});t.path=n[r];n[r]=e.exports(t);return n}},591:(e,t,n)=>{"use strict";const r=n(693);const o=new WeakMap;const onetime=(e,t={})=>{if(typeof e!=="function"){throw new TypeError("Expected a function")}let n;let s=0;const i=e.displayName||e.name||"<anonymous>";const onetime=function(...r){o.set(onetime,++s);if(s===1){n=e.apply(this,r);e=null}else if(t.throw===true){throw new Error(`Function \`${i}\` can only be called once`)}return n};r(onetime,e);o.set(onetime,s);return onetime};e.exports=onetime;e.exports["default"]=onetime;e.exports.callCount=e=>{if(!o.has(e)){throw new Error(`The given function \`${e.name}\` is not wrapped by the \`onetime\` package`)}return o.get(e)}},460:e=>{"use strict";const pathKey=(e={})=>{const t=e.env||process.env;const n=e.platform||process.platform;if(n!=="win32"){return"PATH"}return Object.keys(t).reverse().find((e=>e.toUpperCase()==="PATH"))||"Path"};e.exports=pathKey;e.exports["default"]=pathKey},256:(e,t,n)=>{"use strict";const r=n(609);e.exports=(e="")=>{const t=e.match(r);if(!t){return null}const[n,o]=t[0].replace(/#! ?/,"").split(" ");const s=n.split("/").pop();if(s==="env"){return o}return o?`${s} ${o}`:s}},609:e=>{"use strict";e.exports=/^#!(.*)/},580:(e,t,n)=>{var r=global.process;const processOk=function(e){return e&&typeof e==="object"&&typeof e.removeListener==="function"&&typeof e.emit==="function"&&typeof e.reallyExit==="function"&&typeof e.listeners==="function"&&typeof e.kill==="function"&&typeof e.pid==="number"&&typeof e.on==="function"};if(!processOk(r)){e.exports=function(){return function(){}}}else{var o=n(491);var s=n(357);var i=/^win/i.test(r.platform);var a=n(361);if(typeof a!=="function"){a=a.EventEmitter}var c;if(r.__signal_exit_emitter__){c=r.__signal_exit_emitter__}else{c=r.__signal_exit_emitter__=new a;c.count=0;c.emitted={}}if(!c.infinite){c.setMaxListeners(Infinity);c.infinite=true}e.exports=function(e,t){if(!processOk(global.process)){return function(){}}o.equal(typeof e,"function","a callback must be provided for exit handler");if(f===false){p()}var n="exit";if(t&&t.alwaysLast){n="afterexit"}var remove=function(){c.removeListener(n,e);if(c.listeners("exit").length===0&&c.listeners("afterexit").length===0){d()}};c.on(n,e);return remove};var d=function unload(){if(!f||!processOk(global.process)){return}f=false;s.forEach((function(e){try{r.removeListener(e,l[e])}catch(e){}}));r.emit=x;r.reallyExit=m;c.count-=1};e.exports.unload=d;var u=function emit(e,t,n){if(c.emitted[e]){return}c.emitted[e]=true;c.emit(e,t,n)};var l={};s.forEach((function(e){l[e]=function listener(){if(!processOk(global.process)){return}var t=r.listeners(e);if(t.length===c.count){d();u("exit",null,e);u("afterexit",null,e);if(i&&e==="SIGHUP"){e="SIGINT"}r.kill(r.pid,e)}}}));e.exports.signals=function(){return s};var f=false;var p=function load(){if(f||!processOk(global.process)){return}f=true;c.count+=1;s=s.filter((function(e){try{r.on(e,l[e]);return true}catch(e){return false}}));r.emit=h;r.reallyExit=g};e.exports.load=p;var m=r.reallyExit;var g=function processReallyExit(e){if(!processOk(global.process)){return}r.exitCode=e||0;u("exit",r.exitCode,null);u("afterexit",r.exitCode,null);m.call(r,r.exitCode)};var x=r.emit;var h=function processEmit(e,t){if(e==="exit"&&processOk(global.process)){if(t!==undefined){r.exitCode=t}var n=x.apply(this,arguments);u("exit",r.exitCode,null);u("afterexit",r.exitCode,null);return n}else{return x.apply(this,arguments)}}}},357:e=>{e.exports=["SIGABRT","SIGALRM","SIGHUP","SIGINT","SIGTERM"];if(process.platform!=="win32"){e.exports.push("SIGVTALRM","SIGXCPU","SIGXFSZ","SIGUSR2","SIGTRAP","SIGSYS","SIGQUIT","SIGIOT")}if(process.platform==="linux"){e.exports.push("SIGIO","SIGPOLL","SIGPWR","SIGSTKFLT","SIGUNUSED")}},133:e=>{"use strict";e.exports=e=>{const t=typeof e==="string"?"\n":"\n".charCodeAt();const n=typeof e==="string"?"\r":"\r".charCodeAt();if(e[e.length-1]===t){e=e.slice(0,e.length-1)}if(e[e.length-1]===n){e=e.slice(0,e.length-1)}return e}},85:(e,t,n)=>{const r=process.platform==="win32"||process.env.OSTYPE==="cygwin"||process.env.OSTYPE==="msys";const o=n(17);const s=r?";":":";const i=n(212);const getNotFoundError=e=>Object.assign(new Error(`not found: ${e}`),{code:"ENOENT"});const getPathInfo=(e,t)=>{const n=t.colon||s;const o=e.match(/\//)||r&&e.match(/\\/)?[""]:[...r?[process.cwd()]:[],...(t.path||process.env.PATH||"").split(n)];const i=r?t.pathExt||process.env.PATHEXT||".EXE;.CMD;.BAT;.COM":"";const a=r?i.split(n):[""];if(r){if(e.indexOf(".")!==-1&&a[0]!=="")a.unshift("")}return{pathEnv:o,pathExt:a,pathExtExe:i}};const which=(e,t,n)=>{if(typeof t==="function"){n=t;t={}}if(!t)t={};const{pathEnv:r,pathExt:s,pathExtExe:a}=getPathInfo(e,t);const c=[];const step=n=>new Promise(((s,i)=>{if(n===r.length)return t.all&&c.length?s(c):i(getNotFoundError(e));const a=r[n];const d=/^".*"$/.test(a)?a.slice(1,-1):a;const u=o.join(d,e);const l=!d&&/^\.[\\\/]/.test(e)?e.slice(0,2)+u:u;s(subStep(l,n,0))}));const subStep=(e,n,r)=>new Promise(((o,d)=>{if(r===s.length)return o(step(n+1));const u=s[r];i(e+u,{pathExt:a},((s,i)=>{if(!s&&i){if(t.all)c.push(e+u);else return o(e+u)}return o(subStep(e,n,r+1))}))}));return n?step(0).then((e=>n(null,e)),n):step(0)};const whichSync=(e,t)=>{t=t||{};const{pathEnv:n,pathExt:r,pathExtExe:s}=getPathInfo(e,t);const a=[];for(let c=0;c<n.length;c++){const d=n[c];const u=/^".*"$/.test(d)?d.slice(1,-1):d;const l=o.join(u,e);const f=!u&&/^\.[\\\/]/.test(e)?e.slice(0,2)+l:l;for(let e=0;e<r.length;e++){const n=f+r[e];try{const e=i.sync(n,{pathExt:s});if(e){if(t.all)a.push(n);else return n}}catch(e){}}}if(t.all&&a.length)return a;if(t.nothrow)return null;throw getNotFoundError(e)};e.exports=which;which.sync=whichSync},491:e=>{"use strict";e.exports=require("assert")},300:e=>{"use strict";e.exports=require("buffer")},81:e=>{"use strict";e.exports=require("child_process")},361:e=>{"use strict";e.exports=require("events")},147:e=>{"use strict";e.exports=require("fs")},37:e=>{"use strict";e.exports=require("os")},17:e=>{"use strict";e.exports=require("path")},781:e=>{"use strict";e.exports=require("stream")},837:e=>{"use strict";e.exports=require("util")}};var t={};function __nccwpck_require__(n){var r=t[n];if(r!==undefined){return r.exports}var o=t[n]={exports:{}};var s=true;try{e[n](o,o.exports,__nccwpck_require__);s=false}finally{if(s)delete t[n]}return o.exports}if(typeof __nccwpck_require__!=="undefined")__nccwpck_require__.ab=__dirname+"/";var n=__nccwpck_require__(632);module.exports=n})();