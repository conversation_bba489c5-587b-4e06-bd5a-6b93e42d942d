(()=>{"use strict";var e={485:(e,r,n)=>{const t=n(17);const s=n(197);e.exports=(e,r={})=>{const n=t.resolve(r.cwd||"");const{root:c}=t.parse(n);const o=[].concat(e);return new Promise((e=>{(function find(r){s(o,{cwd:r}).then((n=>{if(n){e(t.join(r,n))}else if(r===c){e(null)}else{find(t.dirname(r))}}))})(n)}))};e.exports.sync=(e,r={})=>{let n=t.resolve(r.cwd||"");const{root:c}=t.parse(n);const o=[].concat(e);while(true){const e=s.sync(o,{cwd:n});if(e){return t.join(n,e)}if(n===c){return null}n=t.dirname(n)}}},197:(e,r,n)=>{const t=n(17);const s=n(383);const c=n(22);e.exports=(e,r)=>{r=Object.assign({cwd:process.cwd()},r);return c(e,(e=>s(t.resolve(r.cwd,e))),r)};e.exports.sync=(e,r)=>{r=Object.assign({cwd:process.cwd()},r);for(const n of e){if(s.sync(t.resolve(r.cwd,n))){return n}}}},848:(e,r,n)=>{const t=n(242);const pLimit=e=>{if(!((Number.isInteger(e)||e===Infinity)&&e>0)){return Promise.reject(new TypeError("Expected `concurrency` to be a number from 1 and up"))}const r=[];let n=0;const next=()=>{n--;if(r.length>0){r.shift()()}};const run=(e,r,...s)=>{n++;const c=t(e,...s);r(c);c.then(next,next)};const enqueue=(t,s,...c)=>{if(n<e){run(t,s,...c)}else{r.push(run.bind(null,t,s,...c))}};const generator=(e,...r)=>new Promise((n=>enqueue(e,n,...r)));Object.defineProperties(generator,{activeCount:{get:()=>n},pendingCount:{get:()=>r.length},clearQueue:{value:()=>{r.length=0}}});return generator};e.exports=pLimit;e.exports["default"]=pLimit},22:(e,r,n)=>{const t=n(848);class EndError extends Error{constructor(e){super();this.value=e}}const testElement=(e,r)=>Promise.resolve(e).then(r);const finder=e=>Promise.all(e).then((e=>e[1]===true&&Promise.reject(new EndError(e[0]))));e.exports=(e,r,n)=>{n=Object.assign({concurrency:Infinity,preserveOrder:true},n);const s=t(n.concurrency);const c=[...e].map((e=>[e,s(testElement,e,r)]));const o=t(n.preserveOrder?1:Infinity);return Promise.all(c.map((e=>o(finder,e)))).then((()=>{})).catch((e=>e instanceof EndError?e.value:Promise.reject(e)))}},242:e=>{const pTry=(e,...r)=>new Promise((n=>{n(e(...r))}));e.exports=pTry;e.exports["default"]=pTry},383:(e,r,n)=>{const t=n(147);e.exports=e=>new Promise((r=>{t.access(e,(e=>{r(!e)}))}));e.exports.sync=e=>{try{t.accessSync(e);return true}catch(e){return false}}},800:(e,r,n)=>{const t=n(485);e.exports=async({cwd:e}={})=>t("package.json",{cwd:e});e.exports.sync=({cwd:e}={})=>t.sync("package.json",{cwd:e})},147:e=>{e.exports=require("fs")},17:e=>{e.exports=require("path")}};var r={};function __nccwpck_require__(n){var t=r[n];if(t!==undefined){return t.exports}var s=r[n]={exports:{}};var c=true;try{e[n](s,s.exports,__nccwpck_require__);c=false}finally{if(c)delete r[n]}return s.exports}if(typeof __nccwpck_require__!=="undefined")__nccwpck_require__.ab=__dirname+"/";var n=__nccwpck_require__(800);module.exports=n})();