(()=>{var e={319:e=>{e.exports=function(e,t){if(!t)t={};var r={bools:{},strings:{},unknownFn:null};if(typeof t["unknown"]==="function"){r.unknownFn=t["unknown"]}if(typeof t["boolean"]==="boolean"&&t["boolean"]){r.allBools=true}else{[].concat(t["boolean"]).filter(Boolean).forEach((function(e){r.bools[e]=true}))}var n={};Object.keys(t.alias||{}).forEach((function(e){n[e]=[].concat(t.alias[e]);n[e].forEach((function(t){n[t]=[e].concat(n[e].filter((function(e){return t!==e})))}))}));[].concat(t.string).filter(Boolean).forEach((function(e){r.strings[e]=true;if(n[e]){r.strings[n[e]]=true}}));var o=t["default"]||{};var s={_:[]};Object.keys(r.bools).forEach((function(e){setArg(e,o[e]===undefined?false:o[e])}));var i=[];if(e.indexOf("--")!==-1){i=e.slice(e.indexOf("--")+1);e=e.slice(0,e.indexOf("--"))}function argDefined(e,t){return r.allBools&&/^--[^=]+$/.test(t)||r.strings[e]||r.bools[e]||n[e]}function setArg(e,t,o){if(o&&r.unknownFn&&!argDefined(e,o)){if(r.unknownFn(o)===false)return}var i=!r.strings[e]&&isNumber(t)?Number(t):t;setKey(s,e.split("."),i);(n[e]||[]).forEach((function(e){setKey(s,e.split("."),i)}))}function setKey(e,t,n){var o=e;for(var s=0;s<t.length-1;s++){var i=t[s];if(isConstructorOrProto(o,i))return;if(o[i]===undefined)o[i]={};if(o[i]===Object.prototype||o[i]===Number.prototype||o[i]===String.prototype)o[i]={};if(o[i]===Array.prototype)o[i]=[];o=o[i]}var i=t[t.length-1];if(isConstructorOrProto(o,i))return;if(o===Object.prototype||o===Number.prototype||o===String.prototype)o={};if(o===Array.prototype)o=[];if(o[i]===undefined||r.bools[i]||typeof o[i]==="boolean"){o[i]=n}else if(Array.isArray(o[i])){o[i].push(n)}else{o[i]=[o[i],n]}}function aliasIsBoolean(e){return n[e].some((function(e){return r.bools[e]}))}for(var a=0;a<e.length;a++){var f=e[a];if(/^--.+=/.test(f)){var u=f.match(/^--([^=]+)=([\s\S]*)$/);var l=u[1];var c=u[2];if(r.bools[l]){c=c!=="false"}setArg(l,c,f)}else if(/^--no-.+/.test(f)){var l=f.match(/^--no-(.+)/)[1];setArg(l,false,f)}else if(/^--.+/.test(f)){var l=f.match(/^--(.+)/)[1];var p=e[a+1];if(p!==undefined&&!/^-/.test(p)&&!r.bools[l]&&!r.allBools&&(n[l]?!aliasIsBoolean(l):true)){setArg(l,p,f);a++}else if(/^(true|false)$/.test(p)){setArg(l,p==="true",f);a++}else{setArg(l,r.strings[l]?"":true,f)}}else if(/^-[^-]+/.test(f)){var b=f.slice(1,-1).split("");var _=false;for(var g=0;g<b.length;g++){var p=f.slice(g+2);if(p==="-"){setArg(b[g],p,f);continue}if(/[A-Za-z]/.test(b[g])&&/=/.test(p)){setArg(b[g],p.split("=")[1],f);_=true;break}if(/[A-Za-z]/.test(b[g])&&/-?\d+(\.\d*)?(e-?\d+)?$/.test(p)){setArg(b[g],p,f);_=true;break}if(b[g+1]&&b[g+1].match(/\W/)){setArg(b[g],f.slice(g+2),f);_=true;break}else{setArg(b[g],r.strings[b[g]]?"":true,f)}}var l=f.slice(-1)[0];if(!_&&l!=="-"){if(e[a+1]&&!/^(-|--)[^-]/.test(e[a+1])&&!r.bools[l]&&(n[l]?!aliasIsBoolean(l):true)){setArg(l,e[a+1],f);a++}else if(e[a+1]&&/^(true|false)$/.test(e[a+1])){setArg(l,e[a+1]==="true",f);a++}else{setArg(l,r.strings[l]?"":true,f)}}}else{if(!r.unknownFn||r.unknownFn(f)!==false){s._.push(r.strings["_"]||!isNumber(f)?f:Number(f))}if(t.stopEarly){s._.push.apply(s._,e.slice(a+1));break}}}Object.keys(o).forEach((function(e){if(!hasKey(s,e.split("."))){setKey(s,e.split("."),o[e]);(n[e]||[]).forEach((function(t){setKey(s,t.split("."),o[e])}))}}));if(t["--"]){s["--"]=new Array;i.forEach((function(e){s["--"].push(e)}))}else{i.forEach((function(e){s._.push(e)}))}return s};function hasKey(e,t){var r=e;t.slice(0,-1).forEach((function(e){r=r[e]||{}}));var n=t[t.length-1];return n in r}function isNumber(e){if(typeof e==="number")return true;if(/^0x[0-9a-f]+$/i.test(e))return true;return/^[-+]?(?:\d+(?:\.\d*)?|\.\d+)(e[-+]?\d+)?$/.test(e)}function isConstructorOrProto(e,t){return t==="constructor"&&typeof e[t]==="function"||t==="__proto__"}}};var t={};function __nccwpck_require__(r){var n=t[r];if(n!==undefined){return n.exports}var o=t[r]={exports:{}};var s=true;try{e[r](o,o.exports,__nccwpck_require__);s=false}finally{if(s)delete t[r]}return o.exports}if(typeof __nccwpck_require__!=="undefined")__nccwpck_require__.ab=__dirname+"/";var r=__nccwpck_require__(319);module.exports=r})();