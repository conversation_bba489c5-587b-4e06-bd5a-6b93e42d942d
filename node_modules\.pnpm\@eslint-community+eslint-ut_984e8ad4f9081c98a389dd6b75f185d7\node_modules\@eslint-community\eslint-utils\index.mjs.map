{"version": 3, "file": "index.mjs", "sources": ["src/get-innermost-scope.mjs", "src/find-variable.mjs", "src/token-predicate.mjs", "src/get-function-head-location.mjs", "src/get-static-value.mjs", "src/get-string-if-constant.mjs", "src/get-property-name.mjs", "src/get-function-name-with-kind.mjs", "src/has-side-effect.mjs", "src/is-parenthesized.mjs", "src/pattern-matcher.mjs", "src/reference-tracker.mjs", "src/index.mjs"], "sourcesContent": ["/** @typedef {import(\"eslint\").Scope.Scope} Scope */\n/** @typedef {import(\"estree\").Node} Node */\n\n/**\n * Get the innermost scope which contains a given location.\n * @param {Scope} initialScope The initial scope to search.\n * @param {Node} node The location to search.\n * @returns {Scope} The innermost scope.\n */\nexport function getInnermostScope(initialScope, node) {\n    const location = /** @type {[number, number]} */ (node.range)[0]\n\n    let scope = initialScope\n    let found = false\n    do {\n        found = false\n        for (const childScope of scope.childScopes) {\n            const range = /** @type {[number, number]} */ (\n                childScope.block.range\n            )\n\n            if (range[0] <= location && location < range[1]) {\n                scope = childScope\n                found = true\n                break\n            }\n        }\n    } while (found)\n\n    return scope\n}\n", "import { getInnermostScope } from \"./get-innermost-scope.mjs\"\n/** @typedef {import(\"eslint\").Scope.Scope} Scope */\n/** @typedef {import(\"eslint\").Scope.Variable} Variable */\n/** @typedef {import(\"estree\").Identifier} Identifier */\n\n/**\n * Find the variable of a given name.\n * @param {Scope} initialScope The scope to start finding.\n * @param {string|Identifier} nameOrNode The variable name to find. If this is a Node object then it should be an Identifier node.\n * @returns {Variable|null} The found variable or null.\n */\nexport function findVariable(initialScope, nameOrNode) {\n    let name = \"\"\n    /** @type {Scope|null} */\n    let scope = initialScope\n\n    if (typeof nameOrNode === \"string\") {\n        name = nameOrNode\n    } else {\n        name = nameOrNode.name\n        scope = getInnermostScope(scope, nameOrNode)\n    }\n\n    while (scope != null) {\n        const variable = scope.set.get(name)\n        if (variable != null) {\n            return variable\n        }\n        scope = scope.upper\n    }\n\n    return null\n}\n", "/** @typedef {import(\"eslint\").AST.Token} Token */\n/** @typedef {import(\"estree\").Comment} Comment */\n/** @typedef {import(\"./types.mjs\").ArrowToken} ArrowToken */\n/** @typedef {import(\"./types.mjs\").CommaToken} CommaToken */\n/** @typedef {import(\"./types.mjs\").SemicolonToken} SemicolonToken */\n/** @typedef {import(\"./types.mjs\").ColonToken} ColonToken */\n/** @typedef {import(\"./types.mjs\").OpeningParenToken} OpeningParenToken */\n/** @typedef {import(\"./types.mjs\").ClosingParenToken} ClosingParenToken */\n/** @typedef {import(\"./types.mjs\").OpeningBracketToken} OpeningBracketToken */\n/** @typedef {import(\"./types.mjs\").ClosingBracketToken} ClosingBracketToken */\n/** @typedef {import(\"./types.mjs\").OpeningBraceToken} OpeningBraceToken */\n/** @typedef {import(\"./types.mjs\").ClosingBraceToken} ClosingBraceToken */\n/**\n * @template {string} Value\n * @typedef {import(\"./types.mjs\").PunctuatorToken<Value>} PunctuatorToken\n */\n\n/** @typedef {Comment | Token} CommentOrToken */\n\n/**\n * Creates the negate function of the given function.\n * @param {function(CommentOrToken):boolean} f - The function to negate.\n * @returns {function(CommentOrToken):boolean} Negated function.\n */\nfunction negate(f) {\n    return (token) => !f(token)\n}\n\n/**\n * Checks if the given token is a PunctuatorToken with the given value\n * @template {string} Value\n * @param {CommentOrToken} token - The token to check.\n * @param {Value} value - The value to check.\n * @returns {token is PunctuatorToken<Value>} `true` if the token is a PunctuatorToken with the given value.\n */\nfunction isPunctuatorTokenWithValue(token, value) {\n    return token.type === \"Punctuator\" && token.value === value\n}\n\n/**\n * Checks if the given token is an arrow token or not.\n * @param {CommentOrToken} token - The token to check.\n * @returns {token is ArrowToken} `true` if the token is an arrow token.\n */\nexport function isArrowToken(token) {\n    return isPunctuatorTokenWithValue(token, \"=>\")\n}\n\n/**\n * Checks if the given token is a comma token or not.\n * @param {CommentOrToken} token - The token to check.\n * @returns {token is CommaToken} `true` if the token is a comma token.\n */\nexport function isCommaToken(token) {\n    return isPunctuatorTokenWithValue(token, \",\")\n}\n\n/**\n * Checks if the given token is a semicolon token or not.\n * @param {CommentOrToken} token - The token to check.\n * @returns {token is SemicolonToken} `true` if the token is a semicolon token.\n */\nexport function isSemicolonToken(token) {\n    return isPunctuatorTokenWithValue(token, \";\")\n}\n\n/**\n * Checks if the given token is a colon token or not.\n * @param {CommentOrToken} token - The token to check.\n * @returns {token is ColonToken} `true` if the token is a colon token.\n */\nexport function isColonToken(token) {\n    return isPunctuatorTokenWithValue(token, \":\")\n}\n\n/**\n * Checks if the given token is an opening parenthesis token or not.\n * @param {CommentOrToken} token - The token to check.\n * @returns {token is OpeningParenToken} `true` if the token is an opening parenthesis token.\n */\nexport function isOpeningParenToken(token) {\n    return isPunctuatorTokenWithValue(token, \"(\")\n}\n\n/**\n * Checks if the given token is a closing parenthesis token or not.\n * @param {CommentOrToken} token - The token to check.\n * @returns {token is ClosingParenToken} `true` if the token is a closing parenthesis token.\n */\nexport function isClosingParenToken(token) {\n    return isPunctuatorTokenWithValue(token, \")\")\n}\n\n/**\n * Checks if the given token is an opening square bracket token or not.\n * @param {CommentOrToken} token - The token to check.\n * @returns {token is OpeningBracketToken} `true` if the token is an opening square bracket token.\n */\nexport function isOpeningBracketToken(token) {\n    return isPunctuatorTokenWithValue(token, \"[\")\n}\n\n/**\n * Checks if the given token is a closing square bracket token or not.\n * @param {CommentOrToken} token - The token to check.\n * @returns {token is ClosingBracketToken} `true` if the token is a closing square bracket token.\n */\nexport function isClosingBracketToken(token) {\n    return isPunctuatorTokenWithValue(token, \"]\")\n}\n\n/**\n * Checks if the given token is an opening brace token or not.\n * @param {CommentOrToken} token - The token to check.\n * @returns {token is OpeningBraceToken} `true` if the token is an opening brace token.\n */\nexport function isOpeningBraceToken(token) {\n    return isPunctuatorTokenWithValue(token, \"{\")\n}\n\n/**\n * Checks if the given token is a closing brace token or not.\n * @param {CommentOrToken} token - The token to check.\n * @returns {token is ClosingBraceToken} `true` if the token is a closing brace token.\n */\nexport function isClosingBraceToken(token) {\n    return isPunctuatorTokenWithValue(token, \"}\")\n}\n\n/**\n * Checks if the given token is a comment token or not.\n * @param {CommentOrToken} token - The token to check.\n * @returns {token is Comment} `true` if the token is a comment token.\n */\nexport function isCommentToken(token) {\n    return [\"Block\", \"Line\", \"Shebang\"].includes(token.type)\n}\n\nexport const isNotArrowToken = negate(isArrowToken)\nexport const isNotCommaToken = negate(isCommaToken)\nexport const isNotSemicolonToken = negate(isSemicolonToken)\nexport const isNotColonToken = negate(isColonToken)\nexport const isNotOpeningParenToken = negate(isOpeningParenToken)\nexport const isNotClosingParenToken = negate(isClosingParenToken)\nexport const isNotOpeningBracketToken = negate(isOpeningBracketToken)\nexport const isNotClosingBracketToken = negate(isClosingBracketToken)\nexport const isNotOpeningBraceToken = negate(isOpeningBraceToken)\nexport const isNotClosingBraceToken = negate(isClosingBraceToken)\nexport const isNotCommentToken = negate(isCommentToken)\n", "import { isArrowToken, isOpeningParenToken } from \"./token-predicate.mjs\"\n/** @typedef {import(\"eslint\").Rule.Node} RuleNode */\n/** @typedef {import(\"eslint\").SourceCode} SourceCode */\n/** @typedef {import(\"eslint\").AST.Token} Token */\n/** @typedef {import(\"estree\").Function} FunctionNode */\n/** @typedef {import(\"estree\").FunctionDeclaration} FunctionDeclaration */\n/** @typedef {import(\"estree\").FunctionExpression} FunctionExpression */\n/** @typedef {import(\"estree\").SourceLocation} SourceLocation */\n/** @typedef {import(\"estree\").Position} Position */\n\n/**\n * Get the `(` token of the given function node.\n * @param {FunctionExpression | FunctionDeclaration} node - The function node to get.\n * @param {SourceCode} sourceCode - The source code object to get tokens.\n * @returns {Token} `(` token.\n */\nfunction getOpeningParenOfParams(node, sourceCode) {\n    return node.id\n        ? /** @type {Token} */ (\n              sourceCode.getTokenAfter(node.id, isOpeningParenToken)\n          )\n        : /** @type {Token} */ (\n              sourceCode.getFirstToken(node, isOpeningParenToken)\n          )\n}\n\n/**\n * Get the location of the given function node for reporting.\n * @param {FunctionNode} node - The function node to get.\n * @param {SourceCode} sourceCode - The source code object to get tokens.\n * @returns {SourceLocation|null} The location of the function node for reporting.\n */\nexport function getFunctionHeadLocation(node, sourceCode) {\n    const parent = /** @type {RuleNode} */ (node).parent\n\n    /** @type {Position|null} */\n    let start = null\n    /** @type {Position|null} */\n    let end = null\n\n    if (node.type === \"ArrowFunctionExpression\") {\n        const arrowToken = /** @type {Token} */ (\n            sourceCode.getTokenBefore(node.body, isArrowToken)\n        )\n\n        start = arrowToken.loc.start\n        end = arrowToken.loc.end\n    } else if (\n        parent.type === \"Property\" ||\n        parent.type === \"MethodDefinition\" ||\n        parent.type === \"PropertyDefinition\"\n    ) {\n        start = /** @type {SourceLocation} */ (parent.loc).start\n        end = getOpeningParenOfParams(node, sourceCode).loc.start\n    } else {\n        start = /** @type {SourceLocation} */ (node.loc).start\n        end = getOpeningParenOfParams(node, sourceCode).loc.start\n    }\n\n    return {\n        start: { ...start },\n        end: { ...end },\n    }\n}\n", "/* globals globalThis, global, self, window */\n\nimport { findVariable } from \"./find-variable.mjs\"\n/** @typedef {import(\"./types.mjs\").StaticValue} StaticValue */\n/** @typedef {import(\"eslint\").Scope.Scope} Scope */\n/** @typedef {import(\"estree\").Node} Node */\n/** @typedef {import(\"@typescript-eslint/types\").TSESTree.Node} TSESTreeNode */\n/** @typedef {import(\"@typescript-eslint/types\").TSESTree.AST_NODE_TYPES} TSESTreeNodeTypes */\n/** @typedef {import(\"@typescript-eslint/types\").TSESTree.MemberExpression} MemberExpression */\n/** @typedef {import(\"@typescript-eslint/types\").TSESTree.Property} Property */\n/** @typedef {import(\"@typescript-eslint/types\").TSESTree.RegExpLiteral} RegExpLiteral */\n/** @typedef {import(\"@typescript-eslint/types\").TSESTree.BigIntLiteral} BigIntLiteral */\n/** @typedef {import(\"@typescript-eslint/types\").TSESTree.Literal} Literal */\n\nconst globalObject =\n    typeof globalThis !== \"undefined\"\n        ? globalThis\n        : // @ts-ignore\n        typeof self !== \"undefined\"\n        ? // @ts-ignore\n          self\n        : // @ts-ignore\n        typeof window !== \"undefined\"\n        ? // @ts-ignore\n          window\n        : typeof global !== \"undefined\"\n        ? global\n        : {}\n\nconst builtinNames = Object.freeze(\n    new Set([\n        \"Array\",\n        \"ArrayBuffer\",\n        \"BigInt\",\n        \"BigInt64Array\",\n        \"BigUint64Array\",\n        \"Boolean\",\n        \"DataView\",\n        \"Date\",\n        \"decodeURI\",\n        \"decodeURIComponent\",\n        \"encodeURI\",\n        \"encodeURIComponent\",\n        \"escape\",\n        \"Float32Array\",\n        \"Float64Array\",\n        \"Function\",\n        \"Infinity\",\n        \"Int16Array\",\n        \"Int32Array\",\n        \"Int8Array\",\n        \"isFinite\",\n        \"isNaN\",\n        \"isPrototypeOf\",\n        \"JSON\",\n        \"Map\",\n        \"Math\",\n        \"NaN\",\n        \"Number\",\n        \"Object\",\n        \"parseFloat\",\n        \"parseInt\",\n        \"Promise\",\n        \"Proxy\",\n        \"Reflect\",\n        \"RegExp\",\n        \"Set\",\n        \"String\",\n        \"Symbol\",\n        \"Uint16Array\",\n        \"Uint32Array\",\n        \"Uint8Array\",\n        \"Uint8ClampedArray\",\n        \"undefined\",\n        \"unescape\",\n        \"WeakMap\",\n        \"WeakSet\",\n    ]),\n)\nconst callAllowed = new Set(\n    [\n        Array.isArray,\n        Array.of,\n        Array.prototype.at,\n        Array.prototype.concat,\n        Array.prototype.entries,\n        Array.prototype.every,\n        Array.prototype.filter,\n        Array.prototype.find,\n        Array.prototype.findIndex,\n        Array.prototype.flat,\n        Array.prototype.includes,\n        Array.prototype.indexOf,\n        Array.prototype.join,\n        Array.prototype.keys,\n        Array.prototype.lastIndexOf,\n        Array.prototype.slice,\n        Array.prototype.some,\n        Array.prototype.toString,\n        Array.prototype.values,\n        typeof BigInt === \"function\" ? BigInt : undefined,\n        Boolean,\n        Date,\n        Date.parse,\n        decodeURI,\n        decodeURIComponent,\n        encodeURI,\n        encodeURIComponent,\n        escape,\n        isFinite,\n        isNaN,\n        // @ts-ignore\n        isPrototypeOf,\n        Map,\n        Map.prototype.entries,\n        Map.prototype.get,\n        Map.prototype.has,\n        Map.prototype.keys,\n        Map.prototype.values,\n        .../** @type {(keyof typeof Math)[]} */ (\n            Object.getOwnPropertyNames(Math)\n        )\n            .filter((k) => k !== \"random\")\n            .map((k) => Math[k])\n            .filter((f) => typeof f === \"function\"),\n        Number,\n        Number.isFinite,\n        Number.isNaN,\n        Number.parseFloat,\n        Number.parseInt,\n        Number.prototype.toExponential,\n        Number.prototype.toFixed,\n        Number.prototype.toPrecision,\n        Number.prototype.toString,\n        Object,\n        Object.entries,\n        Object.is,\n        Object.isExtensible,\n        Object.isFrozen,\n        Object.isSealed,\n        Object.keys,\n        Object.values,\n        parseFloat,\n        parseInt,\n        RegExp,\n        Set,\n        Set.prototype.entries,\n        Set.prototype.has,\n        Set.prototype.keys,\n        Set.prototype.values,\n        String,\n        String.fromCharCode,\n        String.fromCodePoint,\n        String.raw,\n        String.prototype.at,\n        String.prototype.charAt,\n        String.prototype.charCodeAt,\n        String.prototype.codePointAt,\n        String.prototype.concat,\n        String.prototype.endsWith,\n        String.prototype.includes,\n        String.prototype.indexOf,\n        String.prototype.lastIndexOf,\n        String.prototype.normalize,\n        String.prototype.padEnd,\n        String.prototype.padStart,\n        String.prototype.slice,\n        String.prototype.startsWith,\n        String.prototype.substr,\n        String.prototype.substring,\n        String.prototype.toLowerCase,\n        String.prototype.toString,\n        String.prototype.toUpperCase,\n        String.prototype.trim,\n        String.prototype.trimEnd,\n        String.prototype.trimLeft,\n        String.prototype.trimRight,\n        String.prototype.trimStart,\n        Symbol.for,\n        Symbol.keyFor,\n        unescape,\n    ].filter((f) => typeof f === \"function\"),\n)\nconst callPassThrough = new Set([\n    Object.freeze,\n    Object.preventExtensions,\n    Object.seal,\n])\n\n/** @type {ReadonlyArray<readonly [Function, ReadonlySet<string>]>} */\nconst getterAllowed = [\n    [Map, new Set([\"size\"])],\n    [\n        RegExp,\n        new Set([\n            \"dotAll\",\n            \"flags\",\n            \"global\",\n            \"hasIndices\",\n            \"ignoreCase\",\n            \"multiline\",\n            \"source\",\n            \"sticky\",\n            \"unicode\",\n        ]),\n    ],\n    [Set, new Set([\"size\"])],\n]\n\n/**\n * Get the property descriptor.\n * @param {object} object The object to get.\n * @param {string|number|symbol} name The property name to get.\n */\nfunction getPropertyDescriptor(object, name) {\n    let x = object\n    while ((typeof x === \"object\" || typeof x === \"function\") && x !== null) {\n        const d = Object.getOwnPropertyDescriptor(x, name)\n        if (d) {\n            return d\n        }\n        x = Object.getPrototypeOf(x)\n    }\n    return null\n}\n\n/**\n * Check if a property is getter or not.\n * @param {object} object The object to check.\n * @param {string|number|symbol} name The property name to check.\n */\nfunction isGetter(object, name) {\n    const d = getPropertyDescriptor(object, name)\n    return d != null && d.get != null\n}\n\n/**\n * Get the element values of a given node list.\n * @param {(Node|TSESTreeNode|null)[]} nodeList The node list to get values.\n * @param {Scope|undefined|null} initialScope The initial scope to find variables.\n * @returns {any[]|null} The value list if all nodes are constant. Otherwise, null.\n */\nfunction getElementValues(nodeList, initialScope) {\n    const valueList = []\n\n    for (let i = 0; i < nodeList.length; ++i) {\n        const elementNode = nodeList[i]\n\n        if (elementNode == null) {\n            valueList.length = i + 1\n        } else if (elementNode.type === \"SpreadElement\") {\n            const argument = getStaticValueR(elementNode.argument, initialScope)\n            if (argument == null) {\n                return null\n            }\n            valueList.push(.../** @type {Iterable<any>} */ (argument.value))\n        } else {\n            const element = getStaticValueR(elementNode, initialScope)\n            if (element == null) {\n                return null\n            }\n            valueList.push(element.value)\n        }\n    }\n\n    return valueList\n}\n\n/**\n * Returns whether the given variable is never written to after initialization.\n * @param {import(\"eslint\").Scope.Variable} variable\n * @returns {boolean}\n */\nfunction isEffectivelyConst(variable) {\n    const refs = variable.references\n\n    const inits = refs.filter((r) => r.init).length\n    const reads = refs.filter((r) => r.isReadOnly()).length\n    if (inits === 1 && reads + inits === refs.length) {\n        // there is only one init and all other references only read\n        return true\n    }\n    return false\n}\n\n/**\n * @template {TSESTreeNodeTypes} T\n * @callback VisitorCallback\n * @param {TSESTreeNode & { type: T }} node\n * @param {Scope|undefined|null} initialScope\n * @returns {StaticValue | null}\n */\n/**\n * @typedef { { [K in TSESTreeNodeTypes]?: VisitorCallback<K> } } Operations\n */\n/**\n * @type {Operations}\n */\nconst operations = Object.freeze({\n    ArrayExpression(node, initialScope) {\n        const elements = getElementValues(node.elements, initialScope)\n        return elements != null ? { value: elements } : null\n    },\n\n    AssignmentExpression(node, initialScope) {\n        if (node.operator === \"=\") {\n            return getStaticValueR(node.right, initialScope)\n        }\n        return null\n    },\n\n    //eslint-disable-next-line complexity\n    BinaryExpression(node, initialScope) {\n        if (node.operator === \"in\" || node.operator === \"instanceof\") {\n            // Not supported.\n            return null\n        }\n\n        const left = getStaticValueR(node.left, initialScope)\n        const right = getStaticValueR(node.right, initialScope)\n        if (left != null && right != null) {\n            switch (node.operator) {\n                case \"==\":\n                    return { value: left.value == right.value } //eslint-disable-line eqeqeq\n                case \"!=\":\n                    return { value: left.value != right.value } //eslint-disable-line eqeqeq\n                case \"===\":\n                    return { value: left.value === right.value }\n                case \"!==\":\n                    return { value: left.value !== right.value }\n                case \"<\":\n                    return {\n                        value:\n                            /** @type {any} */ (left.value) <\n                            /** @type {any} */ (right.value),\n                    }\n                case \"<=\":\n                    return {\n                        value:\n                            /** @type {any} */ (left.value) <=\n                            /** @type {any} */ (right.value),\n                    }\n                case \">\":\n                    return {\n                        value:\n                            /** @type {any} */ (left.value) >\n                            /** @type {any} */ (right.value),\n                    }\n                case \">=\":\n                    return {\n                        value:\n                            /** @type {any} */ (left.value) >=\n                            /** @type {any} */ (right.value),\n                    }\n                case \"<<\":\n                    return {\n                        value:\n                            /** @type {any} */ (left.value) <<\n                            /** @type {any} */ (right.value),\n                    }\n                case \">>\":\n                    return {\n                        value:\n                            /** @type {any} */ (left.value) >>\n                            /** @type {any} */ (right.value),\n                    }\n                case \">>>\":\n                    return {\n                        value:\n                            /** @type {any} */ (left.value) >>>\n                            /** @type {any} */ (right.value),\n                    }\n                case \"+\":\n                    return {\n                        value:\n                            /** @type {any} */ (left.value) +\n                            /** @type {any} */ (right.value),\n                    }\n                case \"-\":\n                    return {\n                        value:\n                            /** @type {any} */ (left.value) -\n                            /** @type {any} */ (right.value),\n                    }\n                case \"*\":\n                    return {\n                        value:\n                            /** @type {any} */ (left.value) *\n                            /** @type {any} */ (right.value),\n                    }\n                case \"/\":\n                    return {\n                        value:\n                            /** @type {any} */ (left.value) /\n                            /** @type {any} */ (right.value),\n                    }\n                case \"%\":\n                    return {\n                        value:\n                            /** @type {any} */ (left.value) %\n                            /** @type {any} */ (right.value),\n                    }\n                case \"**\":\n                    return {\n                        value:\n                            /** @type {any} */ (left.value) **\n                            /** @type {any} */ (right.value),\n                    }\n                case \"|\":\n                    return {\n                        value:\n                            /** @type {any} */ (left.value) |\n                            /** @type {any} */ (right.value),\n                    }\n                case \"^\":\n                    return {\n                        value:\n                            /** @type {any} */ (left.value) ^\n                            /** @type {any} */ (right.value),\n                    }\n                case \"&\":\n                    return {\n                        value:\n                            /** @type {any} */ (left.value) &\n                            /** @type {any} */ (right.value),\n                    }\n\n                // no default\n            }\n        }\n\n        return null\n    },\n\n    CallExpression(node, initialScope) {\n        const calleeNode = node.callee\n        const args = getElementValues(node.arguments, initialScope)\n\n        if (args != null) {\n            if (calleeNode.type === \"MemberExpression\") {\n                if (calleeNode.property.type === \"PrivateIdentifier\") {\n                    return null\n                }\n                const object = getStaticValueR(calleeNode.object, initialScope)\n                if (object != null) {\n                    if (\n                        object.value == null &&\n                        (object.optional || node.optional)\n                    ) {\n                        return { value: undefined, optional: true }\n                    }\n                    const property = getStaticPropertyNameValue(\n                        calleeNode,\n                        initialScope,\n                    )\n\n                    if (property != null) {\n                        const receiver =\n                            /** @type {Record<PropertyKey, (...args: any[]) => any>} */ (\n                                object.value\n                            )\n                        const methodName = /** @type {PropertyKey} */ (\n                            property.value\n                        )\n                        if (callAllowed.has(receiver[methodName])) {\n                            return {\n                                value: receiver[methodName](...args),\n                            }\n                        }\n                        if (callPassThrough.has(receiver[methodName])) {\n                            return { value: args[0] }\n                        }\n                    }\n                }\n            } else {\n                const callee = getStaticValueR(calleeNode, initialScope)\n                if (callee != null) {\n                    if (callee.value == null && node.optional) {\n                        return { value: undefined, optional: true }\n                    }\n                    const func = /** @type {(...args: any[]) => any} */ (\n                        callee.value\n                    )\n                    if (callAllowed.has(func)) {\n                        return { value: func(...args) }\n                    }\n                    if (callPassThrough.has(func)) {\n                        return { value: args[0] }\n                    }\n                }\n            }\n        }\n\n        return null\n    },\n\n    ConditionalExpression(node, initialScope) {\n        const test = getStaticValueR(node.test, initialScope)\n        if (test != null) {\n            return test.value\n                ? getStaticValueR(node.consequent, initialScope)\n                : getStaticValueR(node.alternate, initialScope)\n        }\n        return null\n    },\n\n    ExpressionStatement(node, initialScope) {\n        return getStaticValueR(node.expression, initialScope)\n    },\n\n    Identifier(node, initialScope) {\n        if (initialScope != null) {\n            const variable = findVariable(initialScope, node)\n\n            // Built-in globals.\n            if (\n                variable != null &&\n                variable.defs.length === 0 &&\n                builtinNames.has(variable.name) &&\n                variable.name in globalObject\n            ) {\n                return { value: globalObject[variable.name] }\n            }\n\n            // Constants.\n            if (variable != null && variable.defs.length === 1) {\n                const def = variable.defs[0]\n                if (\n                    def.parent &&\n                    def.type === \"Variable\" &&\n                    (def.parent.kind === \"const\" ||\n                        isEffectivelyConst(variable)) &&\n                    // TODO(mysticatea): don't support destructuring here.\n                    def.node.id.type === \"Identifier\"\n                ) {\n                    return getStaticValueR(def.node.init, initialScope)\n                }\n            }\n        }\n        return null\n    },\n\n    Literal(node) {\n        const literal =\n            /** @type {Partial<Literal> & Partial<RegExpLiteral> & Partial<BigIntLiteral>} */ (\n                node\n            )\n        //istanbul ignore if : this is implementation-specific behavior.\n        if (\n            (literal.regex != null || literal.bigint != null) &&\n            literal.value == null\n        ) {\n            // It was a RegExp/BigInt literal, but Node.js didn't support it.\n            return null\n        }\n        return { value: literal.value }\n    },\n\n    LogicalExpression(node, initialScope) {\n        const left = getStaticValueR(node.left, initialScope)\n        if (left != null) {\n            if (\n                (node.operator === \"||\" && Boolean(left.value) === true) ||\n                (node.operator === \"&&\" && Boolean(left.value) === false) ||\n                (node.operator === \"??\" && left.value != null)\n            ) {\n                return left\n            }\n\n            const right = getStaticValueR(node.right, initialScope)\n            if (right != null) {\n                return right\n            }\n        }\n\n        return null\n    },\n\n    MemberExpression(node, initialScope) {\n        if (node.property.type === \"PrivateIdentifier\") {\n            return null\n        }\n        const object = getStaticValueR(node.object, initialScope)\n        if (object != null) {\n            if (object.value == null && (object.optional || node.optional)) {\n                return { value: undefined, optional: true }\n            }\n            const property = getStaticPropertyNameValue(node, initialScope)\n\n            if (property != null) {\n                if (\n                    !isGetter(\n                        /** @type {object} */ (object.value),\n                        /** @type {PropertyKey} */ (property.value),\n                    )\n                ) {\n                    return {\n                        value: /** @type {Record<PropertyKey, unknown>} */ (\n                            object.value\n                        )[/** @type {PropertyKey} */ (property.value)],\n                    }\n                }\n\n                for (const [classFn, allowed] of getterAllowed) {\n                    if (\n                        object.value instanceof classFn &&\n                        allowed.has(/** @type {string} */ (property.value))\n                    ) {\n                        return {\n                            value: /** @type {Record<PropertyKey, unknown>} */ (\n                                object.value\n                            )[/** @type {PropertyKey} */ (property.value)],\n                        }\n                    }\n                }\n            }\n        }\n        return null\n    },\n\n    ChainExpression(node, initialScope) {\n        const expression = getStaticValueR(node.expression, initialScope)\n        if (expression != null) {\n            return { value: expression.value }\n        }\n        return null\n    },\n\n    NewExpression(node, initialScope) {\n        const callee = getStaticValueR(node.callee, initialScope)\n        const args = getElementValues(node.arguments, initialScope)\n\n        if (callee != null && args != null) {\n            const Func = /** @type {new (...args: any[]) => any} */ (\n                callee.value\n            )\n            if (callAllowed.has(Func)) {\n                return { value: new Func(...args) }\n            }\n        }\n\n        return null\n    },\n\n    ObjectExpression(node, initialScope) {\n        /** @type {Record<PropertyKey, unknown>} */\n        const object = {}\n\n        for (const propertyNode of node.properties) {\n            if (propertyNode.type === \"Property\") {\n                if (propertyNode.kind !== \"init\") {\n                    return null\n                }\n                const key = getStaticPropertyNameValue(\n                    propertyNode,\n                    initialScope,\n                )\n                const value = getStaticValueR(propertyNode.value, initialScope)\n                if (key == null || value == null) {\n                    return null\n                }\n                object[/** @type {PropertyKey} */ (key.value)] = value.value\n            } else if (\n                propertyNode.type === \"SpreadElement\" ||\n                // @ts-expect-error -- Backward compatibility\n                propertyNode.type === \"ExperimentalSpreadProperty\"\n            ) {\n                const argument = getStaticValueR(\n                    propertyNode.argument,\n                    initialScope,\n                )\n                if (argument == null) {\n                    return null\n                }\n                Object.assign(object, argument.value)\n            } else {\n                return null\n            }\n        }\n\n        return { value: object }\n    },\n\n    SequenceExpression(node, initialScope) {\n        const last = node.expressions[node.expressions.length - 1]\n        return getStaticValueR(last, initialScope)\n    },\n\n    TaggedTemplateExpression(node, initialScope) {\n        const tag = getStaticValueR(node.tag, initialScope)\n        const expressions = getElementValues(\n            node.quasi.expressions,\n            initialScope,\n        )\n\n        if (tag != null && expressions != null) {\n            const func = /** @type {(...args: any[]) => any} */ (tag.value)\n            /** @type {any[] & { raw?: string[] }} */\n            const strings = node.quasi.quasis.map((q) => q.value.cooked)\n            strings.raw = node.quasi.quasis.map((q) => q.value.raw)\n\n            if (func === String.raw) {\n                return { value: func(strings, ...expressions) }\n            }\n        }\n\n        return null\n    },\n\n    TemplateLiteral(node, initialScope) {\n        const expressions = getElementValues(node.expressions, initialScope)\n        if (expressions != null) {\n            let value = node.quasis[0].value.cooked\n            for (let i = 0; i < expressions.length; ++i) {\n                value += expressions[i]\n                value += /** @type {string} */ (node.quasis[i + 1].value.cooked)\n            }\n            return { value }\n        }\n        return null\n    },\n\n    UnaryExpression(node, initialScope) {\n        if (node.operator === \"delete\") {\n            // Not supported.\n            return null\n        }\n        if (node.operator === \"void\") {\n            return { value: undefined }\n        }\n\n        const arg = getStaticValueR(node.argument, initialScope)\n        if (arg != null) {\n            switch (node.operator) {\n                case \"-\":\n                    return { value: -(/** @type {any} */ (arg.value)) }\n                case \"+\":\n                    return { value: +(/** @type {any} */ (arg.value)) } //eslint-disable-line no-implicit-coercion\n                case \"!\":\n                    return { value: !arg.value }\n                case \"~\":\n                    return { value: ~(/** @type {any} */ (arg.value)) }\n                case \"typeof\":\n                    return { value: typeof arg.value }\n\n                // no default\n            }\n        }\n\n        return null\n    },\n    TSAsExpression(node, initialScope) {\n        return getStaticValueR(node.expression, initialScope)\n    },\n    TSSatisfiesExpression(node, initialScope) {\n        return getStaticValueR(node.expression, initialScope)\n    },\n    TSTypeAssertion(node, initialScope) {\n        return getStaticValueR(node.expression, initialScope)\n    },\n    TSNonNullExpression(node, initialScope) {\n        return getStaticValueR(node.expression, initialScope)\n    },\n    TSInstantiationExpression(node, initialScope) {\n        return getStaticValueR(node.expression, initialScope)\n    },\n})\n\n/**\n * Get the value of a given node if it's a static value.\n * @param {Node|TSESTreeNode|null|undefined} node The node to get.\n * @param {Scope|undefined|null} initialScope The scope to start finding variable.\n * @returns {StaticValue|null} The static value of the node, or `null`.\n */\nfunction getStaticValueR(node, initialScope) {\n    if (node != null && Object.hasOwnProperty.call(operations, node.type)) {\n        return /** @type {VisitorCallback<any>} */ (operations[node.type])(\n            /** @type {TSESTreeNode} */ (node),\n            initialScope,\n        )\n    }\n    return null\n}\n\n/**\n * Get the static value of property name from a MemberExpression node or a Property node.\n * @param {MemberExpression|Property} node The node to get.\n * @param {Scope|null} [initialScope] The scope to start finding variable. Optional. If the node is a computed property node and this scope was given, this checks the computed property name by the `getStringIfConstant` function with the scope, and returns the value of it.\n * @returns {StaticValue|null} The static value of the property name of the node, or `null`.\n */\nfunction getStaticPropertyNameValue(node, initialScope) {\n    const nameNode = node.type === \"Property\" ? node.key : node.property\n\n    if (node.computed) {\n        return getStaticValueR(nameNode, initialScope)\n    }\n\n    if (nameNode.type === \"Identifier\") {\n        return { value: nameNode.name }\n    }\n\n    if (nameNode.type === \"Literal\") {\n        if (/** @type {Partial<BigIntLiteral>} */ (nameNode).bigint) {\n            return { value: /** @type {BigIntLiteral} */ (nameNode).bigint }\n        }\n        return { value: String(nameNode.value) }\n    }\n\n    return null\n}\n\n/**\n * Get the value of a given node if it's a static value.\n * @param {Node} node The node to get.\n * @param {Scope|null} [initialScope] The scope to start finding variable. Optional. If this scope was given, this tries to resolve identifier references which are in the given node as much as possible.\n * @returns {StaticValue | null} The static value of the node, or `null`.\n */\nexport function getStaticValue(node, initialScope = null) {\n    try {\n        return getStaticValueR(node, initialScope)\n    } catch (_error) {\n        return null\n    }\n}\n", "import { getStaticValue } from \"./get-static-value.mjs\"\n/** @typedef {import(\"eslint\").Scope.Scope} Scope */\n/** @typedef {import(\"estree\").Node} Node */\n/** @typedef {import(\"estree\").RegExpLiteral} RegExpLiteral */\n/** @typedef {import(\"estree\").BigIntLiteral} BigIntLiteral */\n/** @typedef {import(\"estree\").SimpleLiteral} SimpleLiteral */\n\n/**\n * Get the value of a given node if it's a literal or a template literal.\n * @param {Node} node The node to get.\n * @param {Scope|null} [initialScope] The scope to start finding variable. Optional. If the node is an Identifier node and this scope was given, this checks the variable of the identifier, and returns the value of it if the variable is a constant.\n * @returns {string|null} The value of the node, or `null`.\n */\nexport function getStringIfConstant(node, initialScope = null) {\n    // Handle the literals that the platform doesn't support natively.\n    if (node && node.type === \"Literal\" && node.value === null) {\n        const literal =\n            /** @type {Partial<SimpleLiteral> & Partial<RegExpLiteral> & Partial<BigIntLiteral>} */ (\n                node\n            )\n        if (literal.regex) {\n            return `/${literal.regex.pattern}/${literal.regex.flags}`\n        }\n        if (literal.bigint) {\n            return literal.bigint\n        }\n    }\n\n    const evaluated = getStaticValue(node, initialScope)\n\n    if (evaluated) {\n        // `String(Symbol.prototype)` throws error\n        try {\n            return String(evaluated.value)\n        } catch {\n            // No op\n        }\n    }\n\n    return null\n}\n", "import { getStringIfConstant } from \"./get-string-if-constant.mjs\"\n/** @typedef {import(\"eslint\").Scope.Scope} Scope */\n/** @typedef {import(\"estree\").MemberExpression} MemberExpression */\n/** @typedef {import(\"estree\").MethodDefinition} MethodDefinition */\n/** @typedef {import(\"estree\").Property} Property */\n/** @typedef {import(\"estree\").PropertyDefinition} PropertyDefinition */\n/** @typedef {import(\"estree\").Identifier} Identifier */\n\n/**\n * Get the property name from a MemberExpression node or a Property node.\n * @param {MemberExpression | MethodDefinition | Property | PropertyDefinition} node The node to get.\n * @param {Scope} [initialScope] The scope to start finding variable. Optional. If the node is a computed property node and this scope was given, this checks the computed property name by the `getStringIfConstant` function with the scope, and returns the value of it.\n * @returns {string|null|undefined} The property name of the node.\n */\nexport function getPropertyName(node, initialScope) {\n    switch (node.type) {\n        case \"MemberExpression\":\n            if (node.computed) {\n                return getStringIfConstant(node.property, initialScope)\n            }\n            if (node.property.type === \"PrivateIdentifier\") {\n                return null\n            }\n            return /** @type {Partial<Identifier>} */ (node.property).name\n\n        case \"Property\":\n        case \"MethodDefinition\":\n        case \"PropertyDefinition\":\n            if (node.computed) {\n                return getStringIfConstant(node.key, initialScope)\n            }\n            if (node.key.type === \"Literal\") {\n                return String(node.key.value)\n            }\n            if (node.key.type === \"PrivateIdentifier\") {\n                return null\n            }\n            return /** @type {Partial<Identifier>} */ (node.key).name\n\n        default:\n            break\n    }\n\n    return null\n}\n", "import { getPropertyName } from \"./get-property-name.mjs\"\n/** @typedef {import(\"eslint\").Rule.Node} RuleNode */\n/** @typedef {import(\"eslint\").SourceCode} SourceCode */\n/** @typedef {import(\"estree\").Function} FunctionNode */\n/** @typedef {import(\"estree\").FunctionDeclaration} FunctionDeclaration */\n/** @typedef {import(\"estree\").FunctionExpression} FunctionExpression */\n/** @typedef {import(\"estree\").Identifier} Identifier */\n\n/**\n * Get the name and kind of the given function node.\n * @param {FunctionNode} node - The function node to get.\n * @param {SourceCode} [sourceCode] The source code object to get the code of computed property keys.\n * @returns {string} The name and kind of the function node.\n */\n// eslint-disable-next-line complexity\nexport function getFunctionNameWithKind(node, sourceCode) {\n    const parent = /** @type {RuleNode} */ (node).parent\n    const tokens = []\n    const isObjectMethod = parent.type === \"Property\" && parent.value === node\n    const isClassMethod =\n        parent.type === \"MethodDefinition\" && parent.value === node\n    const isClassFieldMethod =\n        parent.type === \"PropertyDefinition\" && parent.value === node\n\n    // Modifiers.\n    if (isClassMethod || isClassFieldMethod) {\n        if (parent.static) {\n            tokens.push(\"static\")\n        }\n        if (parent.key.type === \"PrivateIdentifier\") {\n            tokens.push(\"private\")\n        }\n    }\n    if (node.async) {\n        tokens.push(\"async\")\n    }\n    if (node.generator) {\n        tokens.push(\"generator\")\n    }\n\n    // Kinds.\n    if (isObjectMethod || isClassMethod) {\n        if (parent.kind === \"constructor\") {\n            return \"constructor\"\n        }\n        if (parent.kind === \"get\") {\n            tokens.push(\"getter\")\n        } else if (parent.kind === \"set\") {\n            tokens.push(\"setter\")\n        } else {\n            tokens.push(\"method\")\n        }\n    } else if (isClassFieldMethod) {\n        tokens.push(\"method\")\n    } else {\n        if (node.type === \"ArrowFunctionExpression\") {\n            tokens.push(\"arrow\")\n        }\n        tokens.push(\"function\")\n    }\n\n    // Names.\n    if (isObjectMethod || isClassMethod || isClassFieldMethod) {\n        if (parent.key.type === \"PrivateIdentifier\") {\n            tokens.push(`#${parent.key.name}`)\n        } else {\n            const name = getPropertyName(parent)\n            if (name) {\n                tokens.push(`'${name}'`)\n            } else if (sourceCode) {\n                const keyText = sourceCode.getText(parent.key)\n                if (!keyText.includes(\"\\n\")) {\n                    tokens.push(`[${keyText}]`)\n                }\n            }\n        }\n    } else if (hasId(node)) {\n        tokens.push(`'${node.id.name}'`)\n    } else if (\n        parent.type === \"VariableDeclarator\" &&\n        parent.id &&\n        parent.id.type === \"Identifier\"\n    ) {\n        tokens.push(`'${parent.id.name}'`)\n    } else if (\n        (parent.type === \"AssignmentExpression\" ||\n            parent.type === \"AssignmentPattern\") &&\n        parent.left &&\n        parent.left.type === \"Identifier\"\n    ) {\n        tokens.push(`'${parent.left.name}'`)\n    } else if (\n        parent.type === \"ExportDefaultDeclaration\" &&\n        parent.declaration === node\n    ) {\n        tokens.push(\"'default'\")\n    }\n\n    return tokens.join(\" \")\n}\n\n/**\n * @param {FunctionNode} node\n * @returns {node is FunctionDeclaration | FunctionExpression & { id: Identifier }}\n */\nfunction hasId(node) {\n    return Boolean(\n        /** @type {Partial<FunctionDeclaration | FunctionExpression>} */ (node)\n            .id,\n    )\n}\n", "import { get<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from \"eslint-visitor-keys\"\n/** @typedef {import(\"estree\").Node} Node */\n/** @typedef {import(\"eslint\").SourceCode} SourceCode */\n/** @typedef {import(\"./types.mjs\").HasSideEffectOptions} HasSideEffectOptions */\n/** @typedef {import(\"estree\").BinaryExpression} BinaryExpression */\n/** @typedef {import(\"estree\").MemberExpression} MemberExpression */\n/** @typedef {import(\"estree\").MethodDefinition} MethodDefinition */\n/** @typedef {import(\"estree\").Property} Property */\n/** @typedef {import(\"estree\").PropertyDefinition} PropertyDefinition */\n/** @typedef {import(\"estree\").UnaryExpression} UnaryExpression */\n\nconst typeConversionBinaryOps = Object.freeze(\n    new Set([\n        \"==\",\n        \"!=\",\n        \"<\",\n        \"<=\",\n        \">\",\n        \">=\",\n        \"<<\",\n        \">>\",\n        \">>>\",\n        \"+\",\n        \"-\",\n        \"*\",\n        \"/\",\n        \"%\",\n        \"|\",\n        \"^\",\n        \"&\",\n        \"in\",\n    ]),\n)\nconst typeConversionUnaryOps = Object.freeze(new Set([\"-\", \"+\", \"!\", \"~\"]))\n\n/**\n * Check whether the given value is an ASTNode or not.\n * @param {any} x The value to check.\n * @returns {x is Node} `true` if the value is an ASTNode.\n */\nfunction isNode(x) {\n    return x !== null && typeof x === \"object\" && typeof x.type === \"string\"\n}\n\nconst visitor = Object.freeze(\n    Object.assign(Object.create(null), {\n        /**\n         * @param {Node} node\n         * @param {HasSideEffectOptions} options\n         * @param {Record<string, string[]>} visitorKeys\n         */\n        $visit(node, options, visitorKeys) {\n            const { type } = node\n\n            if (typeof (/** @type {any} */ (this)[type]) === \"function\") {\n                return /** @type {any} */ (this)[type](\n                    node,\n                    options,\n                    visitorKeys,\n                )\n            }\n\n            return this.$visitChildren(node, options, visitorKeys)\n        },\n\n        /**\n         * @param {Node} node\n         * @param {HasSideEffectOptions} options\n         * @param {Record<string, string[]>} visitorKeys\n         */\n        $visitChildren(node, options, visitorKeys) {\n            const { type } = node\n\n            for (const key of /** @type {(keyof Node)[]} */ (\n                visitorKeys[type] || getKeys(node)\n            )) {\n                const value = node[key]\n\n                if (Array.isArray(value)) {\n                    for (const element of value) {\n                        if (\n                            isNode(element) &&\n                            this.$visit(element, options, visitorKeys)\n                        ) {\n                            return true\n                        }\n                    }\n                } else if (\n                    isNode(value) &&\n                    this.$visit(value, options, visitorKeys)\n                ) {\n                    return true\n                }\n            }\n\n            return false\n        },\n\n        ArrowFunctionExpression() {\n            return false\n        },\n        AssignmentExpression() {\n            return true\n        },\n        AwaitExpression() {\n            return true\n        },\n        /**\n         * @param {BinaryExpression} node\n         * @param {HasSideEffectOptions} options\n         * @param {Record<string, string[]>} visitorKeys\n         */\n        BinaryExpression(node, options, visitorKeys) {\n            if (\n                options.considerImplicitTypeConversion &&\n                typeConversionBinaryOps.has(node.operator) &&\n                (node.left.type !== \"Literal\" || node.right.type !== \"Literal\")\n            ) {\n                return true\n            }\n            return this.$visitChildren(node, options, visitorKeys)\n        },\n        CallExpression() {\n            return true\n        },\n        FunctionExpression() {\n            return false\n        },\n        ImportExpression() {\n            return true\n        },\n        /**\n         * @param {MemberExpression} node\n         * @param {HasSideEffectOptions} options\n         * @param {Record<string, string[]>} visitorKeys\n         */\n        MemberExpression(node, options, visitorKeys) {\n            if (options.considerGetters) {\n                return true\n            }\n            if (\n                options.considerImplicitTypeConversion &&\n                node.computed &&\n                node.property.type !== \"Literal\"\n            ) {\n                return true\n            }\n            return this.$visitChildren(node, options, visitorKeys)\n        },\n        /**\n         * @param {MethodDefinition} node\n         * @param {HasSideEffectOptions} options\n         * @param {Record<string, string[]>} visitorKeys\n         */\n        MethodDefinition(node, options, visitorKeys) {\n            if (\n                options.considerImplicitTypeConversion &&\n                node.computed &&\n                node.key.type !== \"Literal\"\n            ) {\n                return true\n            }\n            return this.$visitChildren(node, options, visitorKeys)\n        },\n        NewExpression() {\n            return true\n        },\n        /**\n         * @param {Property} node\n         * @param {HasSideEffectOptions} options\n         * @param {Record<string, string[]>} visitorKeys\n         */\n        Property(node, options, visitorKeys) {\n            if (\n                options.considerImplicitTypeConversion &&\n                node.computed &&\n                node.key.type !== \"Literal\"\n            ) {\n                return true\n            }\n            return this.$visitChildren(node, options, visitorKeys)\n        },\n        /**\n         * @param {PropertyDefinition} node\n         * @param {HasSideEffectOptions} options\n         * @param {Record<string, string[]>} visitorKeys\n         */\n        PropertyDefinition(node, options, visitorKeys) {\n            if (\n                options.considerImplicitTypeConversion &&\n                node.computed &&\n                node.key.type !== \"Literal\"\n            ) {\n                return true\n            }\n            return this.$visitChildren(node, options, visitorKeys)\n        },\n        /**\n         * @param {UnaryExpression} node\n         * @param {HasSideEffectOptions} options\n         * @param {Record<string, string[]>} visitorKeys\n         */\n        UnaryExpression(node, options, visitorKeys) {\n            if (node.operator === \"delete\") {\n                return true\n            }\n            if (\n                options.considerImplicitTypeConversion &&\n                typeConversionUnaryOps.has(node.operator) &&\n                node.argument.type !== \"Literal\"\n            ) {\n                return true\n            }\n            return this.$visitChildren(node, options, visitorKeys)\n        },\n        UpdateExpression() {\n            return true\n        },\n        YieldExpression() {\n            return true\n        },\n    }),\n)\n\n/**\n * Check whether a given node has any side effect or not.\n * @param {Node} node The node to get.\n * @param {SourceCode} sourceCode The source code object.\n * @param {HasSideEffectOptions} [options] The option object.\n * @returns {boolean} `true` if the node has a certain side effect.\n */\nexport function hasSideEffect(node, sourceCode, options = {}) {\n    const { considerGetters = false, considerImplicitTypeConversion = false } =\n        options\n    return visitor.$visit(\n        node,\n        { considerGetters, considerImplicitTypeConversion },\n        sourceCode.visitorKeys || KEYS,\n    )\n}\n", "import { isClosingParenToken, isOpeningParenToken } from \"./token-predicate.mjs\"\n/** @typedef {import(\"estree\").Node} Node */\n/** @typedef {import(\"eslint\").SourceCode} SourceCode */\n/** @typedef {import(\"eslint\").AST.Token} Token */\n/** @typedef {import(\"eslint\").Rule.Node} RuleNode */\n\n/**\n * Get the left parenthesis of the parent node syntax if it exists.\n * E.g., `if (a) {}` then the `(`.\n * @param {Node} node The AST node to check.\n * @param {SourceCode} sourceCode The source code object to get tokens.\n * @returns {Token|null} The left parenthesis of the parent node syntax\n */\nfunction getParentSyntaxParen(node, sourceCode) {\n    const parent = /** @type {RuleNode} */ (node).parent\n\n    switch (parent.type) {\n        case \"CallExpression\":\n        case \"NewExpression\":\n            if (parent.arguments.length === 1 && parent.arguments[0] === node) {\n                return sourceCode.getTokenAfter(\n                    parent.callee,\n                    isOpeningParenToken,\n                )\n            }\n            return null\n\n        case \"DoWhileStatement\":\n            if (parent.test === node) {\n                return sourceCode.getTokenAfter(\n                    parent.body,\n                    isOpeningParenToken,\n                )\n            }\n            return null\n\n        case \"IfStatement\":\n        case \"WhileStatement\":\n            if (parent.test === node) {\n                return sourceCode.getFirstToken(parent, 1)\n            }\n            return null\n\n        case \"ImportExpression\":\n            if (parent.source === node) {\n                return sourceCode.getFirstToken(parent, 1)\n            }\n            return null\n\n        case \"SwitchStatement\":\n            if (parent.discriminant === node) {\n                return sourceCode.getFirstToken(parent, 1)\n            }\n            return null\n\n        case \"WithStatement\":\n            if (parent.object === node) {\n                return sourceCode.getFirstToken(parent, 1)\n            }\n            return null\n\n        default:\n            return null\n    }\n}\n\n/**\n * Check whether a given node is parenthesized or not.\n * @param {number} times The number of parantheses.\n * @param {Node} node The AST node to check.\n * @param {SourceCode} sourceCode The source code object to get tokens.\n * @returns {boolean} `true` if the node is parenthesized the given times.\n */\n/**\n * Check whether a given node is parenthesized or not.\n * @param {Node} node The AST node to check.\n * @param {SourceCode} sourceCode The source code object to get tokens.\n * @returns {boolean} `true` if the node is parenthesized.\n */\n/**\n * Check whether a given node is parenthesized or not.\n * @param {Node|number} timesOrNode The first parameter.\n * @param {Node|SourceCode} nodeOrSourceCode The second parameter.\n * @param {SourceCode} [optionalSourceCode] The third parameter.\n * @returns {boolean} `true` if the node is parenthesized.\n */\nexport function isParenthesized(\n    timesOrNode,\n    nodeOrSourceCode,\n    optionalSourceCode,\n) {\n    /** @type {number} */\n    let times,\n        /** @type {RuleNode} */\n        node,\n        /** @type {SourceCode} */\n        sourceCode,\n        maybeLeftParen,\n        maybeRightParen\n    if (typeof timesOrNode === \"number\") {\n        times = timesOrNode | 0\n        node = /** @type {RuleNode} */ (nodeOrSourceCode)\n        sourceCode = /** @type {SourceCode} */ (optionalSourceCode)\n        if (!(times >= 1)) {\n            throw new TypeError(\"'times' should be a positive integer.\")\n        }\n    } else {\n        times = 1\n        node = /** @type {RuleNode} */ (timesOrNode)\n        sourceCode = /** @type {SourceCode} */ (nodeOrSourceCode)\n    }\n\n    if (\n        node == null ||\n        // `Program` can't be parenthesized\n        node.parent == null ||\n        // `CatchClause.param` can't be parenthesized, example `try {} catch (error) {}`\n        (node.parent.type === \"CatchClause\" && node.parent.param === node)\n    ) {\n        return false\n    }\n\n    maybeLeftParen = maybeRightParen = node\n    do {\n        maybeLeftParen = sourceCode.getTokenBefore(maybeLeftParen)\n        maybeRightParen = sourceCode.getTokenAfter(maybeRightParen)\n    } while (\n        maybeLeftParen != null &&\n        maybeRightParen != null &&\n        isOpeningParenToken(maybeLeftParen) &&\n        isClosingParenToken(maybeRightParen) &&\n        // Avoid false positive such as `if (a) {}`\n        maybeLeftParen !== getParentSyntaxParen(node, sourceCode) &&\n        --times > 0\n    )\n\n    return times === 0\n}\n", "/**\n * <AUTHOR> <https://github.com/mysticatea>\n * See LICENSE file in root directory for full license.\n */\n\nconst placeholder = /\\$(?:[$&`']|[1-9][0-9]?)/gu\n\n/** @type {WeakMap<PatternMatcher, {pattern:RegExp,escaped:boolean}>} */\nconst internal = new WeakMap()\n\n/**\n * Check whether a given character is escaped or not.\n * @param {string} str The string to check.\n * @param {number} index The location of the character to check.\n * @returns {boolean} `true` if the character is escaped.\n */\nfunction isEscaped(str, index) {\n    let escaped = false\n    for (let i = index - 1; i >= 0 && str.charCodeAt(i) === 0x5c; --i) {\n        escaped = !escaped\n    }\n    return escaped\n}\n\n/**\n * Replace a given string by a given matcher.\n * @param {PatternMatcher} matcher The pattern matcher.\n * @param {string} str The string to be replaced.\n * @param {string} replacement The new substring to replace each matched part.\n * @returns {string} The replaced string.\n */\nfunction replaceS(matcher, str, replacement) {\n    const chunks = []\n    let index = 0\n\n    /**\n     * @param {string} key The placeholder.\n     * @param {RegExpExecArray} match The matched information.\n     * @returns {string} The replaced string.\n     */\n    function replacer(key, match) {\n        switch (key) {\n            case \"$$\":\n                return \"$\"\n            case \"$&\":\n                return match[0]\n            case \"$`\":\n                return str.slice(0, match.index)\n            case \"$'\":\n                return str.slice(match.index + match[0].length)\n            default: {\n                const i = key.slice(1)\n                if (i in match) {\n                    return match[/** @type {any} */ (i)]\n                }\n                return key\n            }\n        }\n    }\n\n    for (const match of matcher.execAll(str)) {\n        chunks.push(str.slice(index, match.index))\n        chunks.push(\n            replacement.replace(placeholder, (key) => replacer(key, match)),\n        )\n        index = match.index + match[0].length\n    }\n    chunks.push(str.slice(index))\n\n    return chunks.join(\"\")\n}\n\n/**\n * Replace a given string by a given matcher.\n * @param {PatternMatcher} matcher The pattern matcher.\n * @param {string} str The string to be replaced.\n * @param {(substring: string, ...args: any[]) => string} replace The function to replace each matched part.\n * @returns {string} The replaced string.\n */\nfunction replaceF(matcher, str, replace) {\n    const chunks = []\n    let index = 0\n\n    for (const match of matcher.execAll(str)) {\n        chunks.push(str.slice(index, match.index))\n        chunks.push(\n            String(\n                replace(\n                    .../** @type {[string, ...string[]]} */ (\n                        /** @type {string[]} */ (match)\n                    ),\n                    match.index,\n                    match.input,\n                ),\n            ),\n        )\n        index = match.index + match[0].length\n    }\n    chunks.push(str.slice(index))\n\n    return chunks.join(\"\")\n}\n\n/**\n * The class to find patterns as considering escape sequences.\n */\nexport class PatternMatcher {\n    /**\n     * Initialize this matcher.\n     * @param {RegExp} pattern The pattern to match.\n     * @param {{escaped?:boolean}} [options] The options.\n     */\n    constructor(pattern, options = {}) {\n        const { escaped = false } = options\n        if (!(pattern instanceof RegExp)) {\n            throw new TypeError(\"'pattern' should be a RegExp instance.\")\n        }\n        if (!pattern.flags.includes(\"g\")) {\n            throw new Error(\"'pattern' should contains 'g' flag.\")\n        }\n\n        internal.set(this, {\n            pattern: new RegExp(pattern.source, pattern.flags),\n            escaped: Boolean(escaped),\n        })\n    }\n\n    /**\n     * Find the pattern in a given string.\n     * @param {string} str The string to find.\n     * @returns {IterableIterator<RegExpExecArray>} The iterator which iterate the matched information.\n     */\n    *execAll(str) {\n        const { pattern, escaped } =\n            /** @type {{pattern:RegExp,escaped:boolean}} */ (internal.get(this))\n        let match = null\n        let lastIndex = 0\n\n        pattern.lastIndex = 0\n        while ((match = pattern.exec(str)) != null) {\n            if (escaped || !isEscaped(str, match.index)) {\n                lastIndex = pattern.lastIndex\n                yield match\n                pattern.lastIndex = lastIndex\n            }\n        }\n    }\n\n    /**\n     * Check whether the pattern is found in a given string.\n     * @param {string} str The string to check.\n     * @returns {boolean} `true` if the pattern was found in the string.\n     */\n    test(str) {\n        const it = this.execAll(str)\n        const ret = it.next()\n        return !ret.done\n    }\n\n    /**\n     * Replace a given string.\n     * @param {string} str The string to be replaced.\n     * @param {(string|((...strs:string[])=>string))} replacer The string or function to replace. This is the same as the 2nd argument of `String.prototype.replace`.\n     * @returns {string} The replaced string.\n     */\n    [Symbol.replace](str, replacer) {\n        return typeof replacer === \"function\"\n            ? replaceF(this, String(str), replacer)\n            : replaceS(this, String(str), String(replacer))\n    }\n}\n", "import { findVariable } from \"./find-variable.mjs\"\nimport { getPropertyName } from \"./get-property-name.mjs\"\nimport { getStringIfConstant } from \"./get-string-if-constant.mjs\"\n/** @typedef {import(\"eslint\").Scope.Scope} Scope */\n/** @typedef {import(\"eslint\").Scope.Variable} Variable */\n/** @typedef {import(\"eslint\").Rule.Node} RuleNode */\n/** @typedef {import(\"estree\").Node} Node */\n/** @typedef {import(\"estree\").Expression} Expression */\n/** @typedef {import(\"estree\").Pattern} Pattern */\n/** @typedef {import(\"estree\").Identifier} Identifier */\n/** @typedef {import(\"estree\").SimpleCallExpression} CallExpression */\n/** @typedef {import(\"estree\").Program} Program */\n/** @typedef {import(\"estree\").ImportDeclaration} ImportDeclaration */\n/** @typedef {import(\"estree\").ExportAllDeclaration} ExportAllDeclaration */\n/** @typedef {import(\"estree\").ExportDefaultDeclaration} ExportDefaultDeclaration */\n/** @typedef {import(\"estree\").ExportNamedDeclaration} ExportNamedDeclaration */\n/** @typedef {import(\"estree\").ImportSpecifier} ImportSpecifier */\n/** @typedef {import(\"estree\").ImportDefaultSpecifier} ImportDefaultSpecifier */\n/** @typedef {import(\"estree\").ImportNamespaceSpecifier} ImportNamespaceSpecifier */\n/** @typedef {import(\"estree\").ExportSpecifier} ExportSpecifier */\n/** @typedef {import(\"estree\").Property} Property */\n/** @typedef {import(\"estree\").AssignmentProperty} AssignmentProperty */\n/** @typedef {import(\"estree\").Literal} Literal */\n/** @typedef {import(\"@typescript-eslint/types\").TSESTree.Node} TSESTreeNode */\n/** @typedef {import(\"./types.mjs\").ReferenceTrackerOptions} ReferenceTrackerOptions */\n/**\n * @template T\n * @typedef {import(\"./types.mjs\").TraceMap<T>} TraceMap\n */\n/**\n * @template T\n * @typedef {import(\"./types.mjs\").TraceMapObject<T>} TraceMapObject\n */\n/**\n * @template T\n * @typedef {import(\"./types.mjs\").TrackedReferences<T>} TrackedReferences\n */\n\nconst IMPORT_TYPE = /^(?:Import|Export(?:All|Default|Named))Declaration$/u\n\n/**\n * Check whether a given node is an import node or not.\n * @param {Node} node\n * @returns {node is ImportDeclaration|ExportAllDeclaration|ExportNamedDeclaration&{source: Literal}} `true` if the node is an import node.\n */\nfunction isHasSource(node) {\n    return (\n        IMPORT_TYPE.test(node.type) &&\n        /** @type {ImportDeclaration|ExportAllDeclaration|ExportNamedDeclaration} */ (\n            node\n        ).source != null\n    )\n}\nconst has =\n    /** @type {<T>(traceMap: TraceMap<unknown>, v: T) => v is (string extends T ? string : T)} */ (\n        Function.call.bind(Object.hasOwnProperty)\n    )\n\nexport const READ = Symbol(\"read\")\nexport const CALL = Symbol(\"call\")\nexport const CONSTRUCT = Symbol(\"construct\")\nexport const ESM = Symbol(\"esm\")\n\nconst requireCall = { require: { [CALL]: true } }\n\n/**\n * Check whether a given variable is modified or not.\n * @param {Variable|undefined} variable The variable to check.\n * @returns {boolean} `true` if the variable is modified.\n */\nfunction isModifiedGlobal(variable) {\n    return (\n        variable == null ||\n        variable.defs.length !== 0 ||\n        variable.references.some((r) => r.isWrite())\n    )\n}\n\n/**\n * Check if the value of a given node is passed through to the parent syntax as-is.\n * For example, `a` and `b` in (`a || b` and `c ? a : b`) are passed through.\n * @param {Node} node A node to check.\n * @returns {node is RuleNode & {parent: Expression}} `true` if the node is passed through.\n */\nfunction isPassThrough(node) {\n    const parent = /** @type {TSESTreeNode} */ (node).parent\n\n    if (parent) {\n        switch (parent.type) {\n            case \"ConditionalExpression\":\n                return parent.consequent === node || parent.alternate === node\n            case \"LogicalExpression\":\n                return true\n            case \"SequenceExpression\":\n                return (\n                    parent.expressions[parent.expressions.length - 1] === node\n                )\n            case \"ChainExpression\":\n                return true\n            case \"TSAsExpression\":\n            case \"TSSatisfiesExpression\":\n            case \"TSTypeAssertion\":\n            case \"TSNonNullExpression\":\n            case \"TSInstantiationExpression\":\n                return true\n\n            default:\n                return false\n        }\n    }\n    return false\n}\n\n/**\n * The reference tracker.\n */\nexport class ReferenceTracker {\n    /**\n     * Initialize this tracker.\n     * @param {Scope} globalScope The global scope.\n     * @param {object} [options] The options.\n     * @param {\"legacy\"|\"strict\"} [options.mode=\"strict\"] The mode to determine the ImportDeclaration's behavior for CJS modules.\n     * @param {string[]} [options.globalObjectNames=[\"global\",\"globalThis\",\"self\",\"window\"]] The variable names for Global Object.\n     */\n    constructor(globalScope, options = {}) {\n        const {\n            mode = \"strict\",\n            globalObjectNames = [\"global\", \"globalThis\", \"self\", \"window\"],\n        } = options\n        /** @private @type {Variable[]} */\n        this.variableStack = []\n        /** @private */\n        this.globalScope = globalScope\n        /** @private */\n        this.mode = mode\n        /** @private */\n        this.globalObjectNames = globalObjectNames.slice(0)\n    }\n\n    /**\n     * Iterate the references of global variables.\n     * @template T\n     * @param {TraceMap<T>} traceMap The trace map.\n     * @returns {IterableIterator<TrackedReferences<T>>} The iterator to iterate references.\n     */\n    *iterateGlobalReferences(traceMap) {\n        for (const key of Object.keys(traceMap)) {\n            const nextTraceMap = traceMap[key]\n            const path = [key]\n            const variable = this.globalScope.set.get(key)\n\n            if (isModifiedGlobal(variable)) {\n                continue\n            }\n\n            yield* this._iterateVariableReferences(\n                /** @type {Variable} */ (variable),\n                path,\n                nextTraceMap,\n                true,\n            )\n        }\n\n        for (const key of this.globalObjectNames) {\n            /** @type {string[]} */\n            const path = []\n            const variable = this.globalScope.set.get(key)\n\n            if (isModifiedGlobal(variable)) {\n                continue\n            }\n\n            yield* this._iterateVariableReferences(\n                /** @type {Variable} */ (variable),\n                path,\n                traceMap,\n                false,\n            )\n        }\n    }\n\n    /**\n     * Iterate the references of CommonJS modules.\n     * @template T\n     * @param {TraceMap<T>} traceMap The trace map.\n     * @returns {IterableIterator<TrackedReferences<T>>} The iterator to iterate references.\n     */\n    *iterateCjsReferences(traceMap) {\n        for (const { node } of this.iterateGlobalReferences(requireCall)) {\n            const key = getStringIfConstant(\n                /** @type {CallExpression} */ (node).arguments[0],\n            )\n            if (key == null || !has(traceMap, key)) {\n                continue\n            }\n\n            const nextTraceMap = traceMap[key]\n            const path = [key]\n\n            if (nextTraceMap[READ]) {\n                yield {\n                    node,\n                    path,\n                    type: READ,\n                    info: nextTraceMap[READ],\n                }\n            }\n            yield* this._iteratePropertyReferences(\n                /** @type {CallExpression} */ (node),\n                path,\n                nextTraceMap,\n            )\n        }\n    }\n\n    /**\n     * Iterate the references of ES modules.\n     * @template T\n     * @param {TraceMap<T>} traceMap The trace map.\n     * @returns {IterableIterator<TrackedReferences<T>>} The iterator to iterate references.\n     */\n    *iterateEsmReferences(traceMap) {\n        const programNode = /** @type {Program} */ (this.globalScope.block)\n\n        for (const node of programNode.body) {\n            if (!isHasSource(node)) {\n                continue\n            }\n            const moduleId = /** @type {string} */ (node.source.value)\n\n            if (!has(traceMap, moduleId)) {\n                continue\n            }\n            const nextTraceMap = traceMap[moduleId]\n            const path = [moduleId]\n\n            if (nextTraceMap[READ]) {\n                yield {\n                    // eslint-disable-next-line object-shorthand -- apply type\n                    node: /** @type {RuleNode} */ (node),\n                    path,\n                    type: READ,\n                    info: nextTraceMap[READ],\n                }\n            }\n\n            if (node.type === \"ExportAllDeclaration\") {\n                for (const key of Object.keys(nextTraceMap)) {\n                    const exportTraceMap = nextTraceMap[key]\n                    if (exportTraceMap[READ]) {\n                        yield {\n                            // eslint-disable-next-line object-shorthand -- apply type\n                            node: /** @type {RuleNode} */ (node),\n                            path: path.concat(key),\n                            type: READ,\n                            info: exportTraceMap[READ],\n                        }\n                    }\n                }\n            } else {\n                for (const specifier of node.specifiers) {\n                    const esm = has(nextTraceMap, ESM)\n                    const it = this._iterateImportReferences(\n                        specifier,\n                        path,\n                        esm\n                            ? nextTraceMap\n                            : this.mode === \"legacy\"\n                            ? { default: nextTraceMap, ...nextTraceMap }\n                            : { default: nextTraceMap },\n                    )\n\n                    if (esm) {\n                        yield* it\n                    } else {\n                        for (const report of it) {\n                            report.path = report.path.filter(exceptDefault)\n                            if (\n                                report.path.length >= 2 ||\n                                report.type !== READ\n                            ) {\n                                yield report\n                            }\n                        }\n                    }\n                }\n            }\n        }\n    }\n\n    /**\n     * Iterate the property references for a given expression AST node.\n     * @template T\n     * @param {Expression} node The expression AST node to iterate property references.\n     * @param {TraceMap<T>} traceMap The trace map.\n     * @returns {IterableIterator<TrackedReferences<T>>} The iterator to iterate property references.\n     */\n    *iteratePropertyReferences(node, traceMap) {\n        yield* this._iteratePropertyReferences(node, [], traceMap)\n    }\n\n    /**\n     * Iterate the references for a given variable.\n     * @private\n     * @template T\n     * @param {Variable} variable The variable to iterate that references.\n     * @param {string[]} path The current path.\n     * @param {TraceMapObject<T>} traceMap The trace map.\n     * @param {boolean} shouldReport = The flag to report those references.\n     * @returns {IterableIterator<TrackedReferences<T>>} The iterator to iterate references.\n     */\n    *_iterateVariableReferences(variable, path, traceMap, shouldReport) {\n        if (this.variableStack.includes(variable)) {\n            return\n        }\n        this.variableStack.push(variable)\n        try {\n            for (const reference of variable.references) {\n                if (!reference.isRead()) {\n                    continue\n                }\n                const node = /** @type {RuleNode & Identifier} */ (\n                    reference.identifier\n                )\n\n                if (shouldReport && traceMap[READ]) {\n                    yield { node, path, type: READ, info: traceMap[READ] }\n                }\n                yield* this._iteratePropertyReferences(node, path, traceMap)\n            }\n        } finally {\n            this.variableStack.pop()\n        }\n    }\n\n    /**\n     * Iterate the references for a given AST node.\n     * @private\n     * @template T\n     * @param {Expression} rootNode The AST node to iterate references.\n     * @param {string[]} path The current path.\n     * @param {TraceMapObject<T>} traceMap The trace map.\n     * @returns {IterableIterator<TrackedReferences<T>>} The iterator to iterate references.\n     */\n    //eslint-disable-next-line complexity\n    *_iteratePropertyReferences(rootNode, path, traceMap) {\n        let node = rootNode\n        while (isPassThrough(node)) {\n            node = node.parent\n        }\n\n        const parent = /** @type {RuleNode} */ (node).parent\n        if (parent.type === \"MemberExpression\") {\n            if (parent.object === node) {\n                const key = getPropertyName(parent)\n                if (key == null || !has(traceMap, key)) {\n                    return\n                }\n\n                path = path.concat(key) //eslint-disable-line no-param-reassign\n                const nextTraceMap = traceMap[key]\n                if (nextTraceMap[READ]) {\n                    yield {\n                        node: parent,\n                        path,\n                        type: READ,\n                        info: nextTraceMap[READ],\n                    }\n                }\n                yield* this._iteratePropertyReferences(\n                    parent,\n                    path,\n                    nextTraceMap,\n                )\n            }\n            return\n        }\n        if (parent.type === \"CallExpression\") {\n            if (parent.callee === node && traceMap[CALL]) {\n                yield { node: parent, path, type: CALL, info: traceMap[CALL] }\n            }\n            return\n        }\n        if (parent.type === \"NewExpression\") {\n            if (parent.callee === node && traceMap[CONSTRUCT]) {\n                yield {\n                    node: parent,\n                    path,\n                    type: CONSTRUCT,\n                    info: traceMap[CONSTRUCT],\n                }\n            }\n            return\n        }\n        if (parent.type === \"AssignmentExpression\") {\n            if (parent.right === node) {\n                yield* this._iterateLhsReferences(parent.left, path, traceMap)\n                yield* this._iteratePropertyReferences(parent, path, traceMap)\n            }\n            return\n        }\n        if (parent.type === \"AssignmentPattern\") {\n            if (parent.right === node) {\n                yield* this._iterateLhsReferences(parent.left, path, traceMap)\n            }\n            return\n        }\n        if (parent.type === \"VariableDeclarator\") {\n            if (parent.init === node) {\n                yield* this._iterateLhsReferences(parent.id, path, traceMap)\n            }\n        }\n    }\n\n    /**\n     * Iterate the references for a given Pattern node.\n     * @private\n     * @template T\n     * @param {Pattern} patternNode The Pattern node to iterate references.\n     * @param {string[]} path The current path.\n     * @param {TraceMapObject<T>} traceMap The trace map.\n     * @returns {IterableIterator<TrackedReferences<T>>} The iterator to iterate references.\n     */\n    *_iterateLhsReferences(patternNode, path, traceMap) {\n        if (patternNode.type === \"Identifier\") {\n            const variable = findVariable(this.globalScope, patternNode)\n            if (variable != null) {\n                yield* this._iterateVariableReferences(\n                    variable,\n                    path,\n                    traceMap,\n                    false,\n                )\n            }\n            return\n        }\n        if (patternNode.type === \"ObjectPattern\") {\n            for (const property of patternNode.properties) {\n                const key = getPropertyName(\n                    /** @type {AssignmentProperty} */ (property),\n                )\n\n                if (key == null || !has(traceMap, key)) {\n                    continue\n                }\n\n                const nextPath = path.concat(key)\n                const nextTraceMap = traceMap[key]\n                if (nextTraceMap[READ]) {\n                    yield {\n                        node: /** @type {RuleNode} */ (property),\n                        path: nextPath,\n                        type: READ,\n                        info: nextTraceMap[READ],\n                    }\n                }\n                yield* this._iterateLhsReferences(\n                    /** @type {AssignmentProperty} */ (property).value,\n                    nextPath,\n                    nextTraceMap,\n                )\n            }\n            return\n        }\n        if (patternNode.type === \"AssignmentPattern\") {\n            yield* this._iterateLhsReferences(patternNode.left, path, traceMap)\n        }\n    }\n\n    /**\n     * Iterate the references for a given ModuleSpecifier node.\n     * @private\n     * @template T\n     * @param {ImportSpecifier | ImportDefaultSpecifier | ImportNamespaceSpecifier | ExportSpecifier} specifierNode The ModuleSpecifier node to iterate references.\n     * @param {string[]} path The current path.\n     * @param {TraceMapObject<T>} traceMap The trace map.\n     * @returns {IterableIterator<TrackedReferences<T>>} The iterator to iterate references.\n     */\n    *_iterateImportReferences(specifierNode, path, traceMap) {\n        const type = specifierNode.type\n\n        if (type === \"ImportSpecifier\" || type === \"ImportDefaultSpecifier\") {\n            const key =\n                type === \"ImportDefaultSpecifier\"\n                    ? \"default\"\n                    : specifierNode.imported.type === \"Identifier\"\n                    ? specifierNode.imported.name\n                    : specifierNode.imported.value\n            if (!has(traceMap, key)) {\n                return\n            }\n\n            path = path.concat(key) //eslint-disable-line no-param-reassign\n            const nextTraceMap = traceMap[key]\n            if (nextTraceMap[READ]) {\n                yield {\n                    node: /** @type {RuleNode} */ (specifierNode),\n                    path,\n                    type: READ,\n                    info: nextTraceMap[READ],\n                }\n            }\n            yield* this._iterateVariableReferences(\n                /** @type {Variable} */ (\n                    findVariable(this.globalScope, specifierNode.local)\n                ),\n                path,\n                nextTraceMap,\n                false,\n            )\n\n            return\n        }\n\n        if (type === \"ImportNamespaceSpecifier\") {\n            yield* this._iterateVariableReferences(\n                /** @type {Variable} */ (\n                    findVariable(this.globalScope, specifierNode.local)\n                ),\n                path,\n                traceMap,\n                false,\n            )\n            return\n        }\n\n        if (type === \"ExportSpecifier\") {\n            const key =\n                specifierNode.local.type === \"Identifier\"\n                    ? specifierNode.local.name\n                    : specifierNode.local.value\n            if (!has(traceMap, key)) {\n                return\n            }\n\n            path = path.concat(key) //eslint-disable-line no-param-reassign\n            const nextTraceMap = traceMap[key]\n            if (nextTraceMap[READ]) {\n                yield {\n                    node: /** @type {RuleNode} */ (specifierNode),\n                    path,\n                    type: READ,\n                    info: nextTraceMap[READ],\n                }\n            }\n        }\n    }\n}\n\nReferenceTracker.READ = READ\nReferenceTracker.CALL = CALL\nReferenceTracker.CONSTRUCT = CONSTRUCT\nReferenceTracker.ESM = ESM\n\n/**\n * This is a predicate function for Array#filter.\n * @param {string} name A name part.\n * @param {number} index The index of the name.\n * @returns {boolean} `false` if it's default.\n */\nfunction exceptDefault(name, index) {\n    return !(index === 1 && name === \"default\")\n}\n", "/** @typedef {import(\"./types.mjs\").StaticValue} StaticValue */\n/** @typedef {import(\"./types.mjs\").StaticValueOptional} StaticValueOptional */\n/** @typedef {import(\"./types.mjs\").StaticValueProvided} StaticValueProvided */\n/** @typedef {import(\"./types.mjs\").ReferenceTrackerOptions} ReferenceTrackerOptions */\n/**\n * @template T\n * @typedef {import(\"./types.mjs\").TraceMap<T>} TraceMap\n */\n/**\n * @template T\n * @typedef {import(\"./types.mjs\").TrackedReferences<T>} TrackedReferences\n */\n/** @typedef {import(\"./types.mjs\").HasSideEffectOptions} HasSideEffectOptions */\n/** @typedef {import(\"./types.mjs\").ArrowToken} ArrowToken */\n/** @typedef {import(\"./types.mjs\").CommaToken} CommaToken */\n/** @typedef {import(\"./types.mjs\").SemicolonToken} SemicolonToken */\n/** @typedef {import(\"./types.mjs\").ColonToken} ColonToken */\n/** @typedef {import(\"./types.mjs\").OpeningParenToken} OpeningParenToken */\n/** @typedef {import(\"./types.mjs\").ClosingParenToken} ClosingParenToken */\n/** @typedef {import(\"./types.mjs\").OpeningBracketToken} OpeningBracketToken */\n/** @typedef {import(\"./types.mjs\").ClosingBracketToken} ClosingBracketToken */\n/** @typedef {import(\"./types.mjs\").OpeningBraceToken} OpeningBraceToken */\n/** @typedef {import(\"./types.mjs\").ClosingBraceToken} ClosingBraceToken */\n\nimport { findVariable } from \"./find-variable.mjs\"\nimport { getFunctionHeadLocation } from \"./get-function-head-location.mjs\"\nimport { getFunctionNameWithKind } from \"./get-function-name-with-kind.mjs\"\nimport { getInnermostScope } from \"./get-innermost-scope.mjs\"\nimport { getPropertyName } from \"./get-property-name.mjs\"\nimport { getStaticValue } from \"./get-static-value.mjs\"\nimport { getStringIfConstant } from \"./get-string-if-constant.mjs\"\nimport { hasSideEffect } from \"./has-side-effect.mjs\"\nimport { isParenthesized } from \"./is-parenthesized.mjs\"\nimport { PatternMatcher } from \"./pattern-matcher.mjs\"\nimport {\n    CALL,\n    CONSTRUCT,\n    ESM,\n    READ,\n    ReferenceTracker,\n} from \"./reference-tracker.mjs\"\nimport {\n    isArrowToken,\n    isClosingBraceToken,\n    isClosingBracketToken,\n    isClosingParenToken,\n    isColonToken,\n    isCommaToken,\n    isCommentToken,\n    isNotArrowToken,\n    isNotClosingBraceToken,\n    isNotClosingBracketToken,\n    isNotClosingParenToken,\n    isNotColonToken,\n    isNotCommaToken,\n    isNotCommentToken,\n    isNotOpeningBraceToken,\n    isNotOpeningBracketToken,\n    isNotOpeningParenToken,\n    isNotSemicolonToken,\n    isOpeningBraceToken,\n    isOpeningBracketToken,\n    isOpeningParenToken,\n    isSemicolonToken,\n} from \"./token-predicate.mjs\"\n\nexport default {\n    CALL,\n    CONSTRUCT,\n    ESM,\n    findVariable,\n    getFunctionHeadLocation,\n    getFunctionNameWithKind,\n    getInnermostScope,\n    getPropertyName,\n    getStaticValue,\n    getStringIfConstant,\n    hasSideEffect,\n    isArrowToken,\n    isClosingBraceToken,\n    isClosingBracketToken,\n    isClosingParenToken,\n    isColonToken,\n    isCommaToken,\n    isCommentToken,\n    isNotArrowToken,\n    isNotClosingBraceToken,\n    isNotClosingBracketToken,\n    isNotClosingParenToken,\n    isNotColonToken,\n    isNotCommaToken,\n    isNotCommentToken,\n    isNotOpeningBraceToken,\n    isNotOpeningBracketToken,\n    isNotOpeningParenToken,\n    isNotSemicolonToken,\n    isOpeningBraceToken,\n    isOpeningBracketToken,\n    isOpeningParenToken,\n    isParenthesized,\n    isSemicolonToken,\n    PatternMatcher,\n    READ,\n    ReferenceTracker,\n}\nexport {\n    CALL,\n    CONSTRUCT,\n    ESM,\n    findVariable,\n    getFunctionHeadLocation,\n    getFunctionNameWithKind,\n    getInnermostScope,\n    getPropertyName,\n    getStaticValue,\n    getStringIfConstant,\n    hasSideEffect,\n    isArrowToken,\n    isClosingBraceToken,\n    isClosingBracketToken,\n    isClosingParenToken,\n    isColonToken,\n    isCommaToken,\n    isCommentToken,\n    isNotArrowToken,\n    isNotClosingBraceToken,\n    isNotClosingBracketToken,\n    isNotClosingParenToken,\n    isNotColonToken,\n    isNotCommaToken,\n    isNotCommentToken,\n    isNotOpeningBraceToken,\n    isNotOpeningBracketToken,\n    isNotOpeningParenToken,\n    isNotSemicolonToken,\n    isOpeningBraceToken,\n    isOpeningBracketToken,\n    isOpeningParenToken,\n    isParenthesized,\n    isSemicolonToken,\n    PatternMatcher,\n    READ,\n    ReferenceTracker,\n}\n"], "names": [], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,iBAAiB,CAAC,YAAY,EAAE,IAAI,EAAE;AACtD,IAAI,MAAM,QAAQ,mCAAmC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,EAAC;AACpE;AACA,IAAI,IAAI,KAAK,GAAG,aAAY;AAC5B,IAAI,IAAI,KAAK,GAAG,MAAK;AACrB,IAAI,GAAG;AACP,QAAQ,KAAK,GAAG,MAAK;AACrB,QAAQ,KAAK,MAAM,UAAU,IAAI,KAAK,CAAC,WAAW,EAAE;AACpD,YAAY,MAAM,KAAK;AACvB,gBAAgB,UAAU,CAAC,KAAK,CAAC,KAAK;AACtC,cAAa;AACb;AACA,YAAY,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,QAAQ,IAAI,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE;AAC7D,gBAAgB,KAAK,GAAG,WAAU;AAClC,gBAAgB,KAAK,GAAG,KAAI;AAC5B,gBAAgB,KAAK;AACrB,aAAa;AACb,SAAS;AACT,KAAK,QAAQ,KAAK,CAAC;AACnB;AACA,IAAI,OAAO,KAAK;AAChB;;AC7BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,YAAY,CAAC,YAAY,EAAE,UAAU,EAAE;AACvD,IAAI,IAAI,IAAI,GAAG,GAAE;AACjB;AACA,IAAI,IAAI,KAAK,GAAG,aAAY;AAC5B;AACA,IAAI,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;AACxC,QAAQ,IAAI,GAAG,WAAU;AACzB,KAAK,MAAM;AACX,QAAQ,IAAI,GAAG,UAAU,CAAC,KAAI;AAC9B,QAAQ,KAAK,GAAG,iBAAiB,CAAC,KAAK,EAAE,UAAU,EAAC;AACpD,KAAK;AACL;AACA,IAAI,OAAO,KAAK,IAAI,IAAI,EAAE;AAC1B,QAAQ,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAC;AAC5C,QAAQ,IAAI,QAAQ,IAAI,IAAI,EAAE;AAC9B,YAAY,OAAO,QAAQ;AAC3B,SAAS;AACT,QAAQ,KAAK,GAAG,KAAK,CAAC,MAAK;AAC3B,KAAK;AACL;AACA,IAAI,OAAO,IAAI;AACf;;AChCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,MAAM,CAAC,CAAC,EAAE;AACnB,IAAI,OAAO,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;AAC/B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,0BAA0B,CAAC,KAAK,EAAE,KAAK,EAAE;AAClD,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,YAAY,IAAI,KAAK,CAAC,KAAK,KAAK,KAAK;AAC/D,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,YAAY,CAAC,KAAK,EAAE;AACpC,IAAI,OAAO,0BAA0B,CAAC,KAAK,EAAE,IAAI,CAAC;AAClD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,YAAY,CAAC,KAAK,EAAE;AACpC,IAAI,OAAO,0BAA0B,CAAC,KAAK,EAAE,GAAG,CAAC;AACjD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,gBAAgB,CAAC,KAAK,EAAE;AACxC,IAAI,OAAO,0BAA0B,CAAC,KAAK,EAAE,GAAG,CAAC;AACjD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,YAAY,CAAC,KAAK,EAAE;AACpC,IAAI,OAAO,0BAA0B,CAAC,KAAK,EAAE,GAAG,CAAC;AACjD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,mBAAmB,CAAC,KAAK,EAAE;AAC3C,IAAI,OAAO,0BAA0B,CAAC,KAAK,EAAE,GAAG,CAAC;AACjD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,mBAAmB,CAAC,KAAK,EAAE;AAC3C,IAAI,OAAO,0BAA0B,CAAC,KAAK,EAAE,GAAG,CAAC;AACjD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,qBAAqB,CAAC,KAAK,EAAE;AAC7C,IAAI,OAAO,0BAA0B,CAAC,KAAK,EAAE,GAAG,CAAC;AACjD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,qBAAqB,CAAC,KAAK,EAAE;AAC7C,IAAI,OAAO,0BAA0B,CAAC,KAAK,EAAE,GAAG,CAAC;AACjD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,mBAAmB,CAAC,KAAK,EAAE;AAC3C,IAAI,OAAO,0BAA0B,CAAC,KAAK,EAAE,GAAG,CAAC;AACjD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,mBAAmB,CAAC,KAAK,EAAE;AAC3C,IAAI,OAAO,0BAA0B,CAAC,KAAK,EAAE,GAAG,CAAC;AACjD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,cAAc,CAAC,KAAK,EAAE;AACtC,IAAI,OAAO,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC;AAC5D,CAAC;AACD;AACY,MAAC,eAAe,GAAG,MAAM,CAAC,YAAY,EAAC;AACvC,MAAC,eAAe,GAAG,MAAM,CAAC,YAAY,EAAC;AACvC,MAAC,mBAAmB,GAAG,MAAM,CAAC,gBAAgB,EAAC;AAC/C,MAAC,eAAe,GAAG,MAAM,CAAC,YAAY,EAAC;AACvC,MAAC,sBAAsB,GAAG,MAAM,CAAC,mBAAmB,EAAC;AACrD,MAAC,sBAAsB,GAAG,MAAM,CAAC,mBAAmB,EAAC;AACrD,MAAC,wBAAwB,GAAG,MAAM,CAAC,qBAAqB,EAAC;AACzD,MAAC,wBAAwB,GAAG,MAAM,CAAC,qBAAqB,EAAC;AACzD,MAAC,sBAAsB,GAAG,MAAM,CAAC,mBAAmB,EAAC;AACrD,MAAC,sBAAsB,GAAG,MAAM,CAAC,mBAAmB,EAAC;AACrD,MAAC,iBAAiB,GAAG,MAAM,CAAC,cAAc;;ACnJtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,uBAAuB,CAAC,IAAI,EAAE,UAAU,EAAE;AACnD,IAAI,OAAO,IAAI,CAAC,EAAE;AAClB;AACA,cAAc,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,EAAE,mBAAmB,CAAC;AACpE;AACA;AACA,cAAc,UAAU,CAAC,aAAa,CAAC,IAAI,EAAE,mBAAmB,CAAC;AACjE,WAAW;AACX,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,uBAAuB,CAAC,IAAI,EAAE,UAAU,EAAE;AAC1D,IAAI,MAAM,MAAM,2BAA2B,CAAC,IAAI,EAAE,OAAM;AACxD;AACA;AACA,IAAI,IAAI,KAAK,GAAG,KAAI;AACpB;AACA,IAAI,IAAI,GAAG,GAAG,KAAI;AAClB;AACA,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,yBAAyB,EAAE;AACjD,QAAQ,MAAM,UAAU;AACxB,YAAY,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,CAAC;AAC9D,UAAS;AACT;AACA,QAAQ,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,MAAK;AACpC,QAAQ,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC,IAAG;AAChC,KAAK,MAAM;AACX,QAAQ,MAAM,CAAC,IAAI,KAAK,UAAU;AAClC,QAAQ,MAAM,CAAC,IAAI,KAAK,kBAAkB;AAC1C,QAAQ,MAAM,CAAC,IAAI,KAAK,oBAAoB;AAC5C,MAAM;AACN,QAAQ,KAAK,iCAAiC,CAAC,MAAM,CAAC,GAAG,EAAE,MAAK;AAChE,QAAQ,GAAG,GAAG,uBAAuB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,GAAG,CAAC,MAAK;AACjE,KAAK,MAAM;AACX,QAAQ,KAAK,iCAAiC,CAAC,IAAI,CAAC,GAAG,EAAE,MAAK;AAC9D,QAAQ,GAAG,GAAG,uBAAuB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,GAAG,CAAC,MAAK;AACjE,KAAK;AACL;AACA,IAAI,OAAO;AACX,QAAQ,KAAK,EAAE,EAAE,GAAG,KAAK,EAAE;AAC3B,QAAQ,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE;AACvB,KAAK;AACL;;AC/DA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,YAAY;AAClB,IAAI,OAAO,UAAU,KAAK,WAAW;AACrC,UAAU,UAAU;AACpB;AACA,QAAQ,OAAO,IAAI,KAAK,WAAW;AACnC;AACA,UAAU,IAAI;AACd;AACA,QAAQ,OAAO,MAAM,KAAK,WAAW;AACrC;AACA,UAAU,MAAM;AAChB,UAAU,OAAO,MAAM,KAAK,WAAW;AACvC,UAAU,MAAM;AAChB,UAAU,GAAE;AACZ;AACA,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM;AAClC,IAAI,IAAI,GAAG,CAAC;AACZ,QAAQ,OAAO;AACf,QAAQ,aAAa;AACrB,QAAQ,QAAQ;AAChB,QAAQ,eAAe;AACvB,QAAQ,gBAAgB;AACxB,QAAQ,SAAS;AACjB,QAAQ,UAAU;AAClB,QAAQ,MAAM;AACd,QAAQ,WAAW;AACnB,QAAQ,oBAAoB;AAC5B,QAAQ,WAAW;AACnB,QAAQ,oBAAoB;AAC5B,QAAQ,QAAQ;AAChB,QAAQ,cAAc;AACtB,QAAQ,cAAc;AACtB,QAAQ,UAAU;AAClB,QAAQ,UAAU;AAClB,QAAQ,YAAY;AACpB,QAAQ,YAAY;AACpB,QAAQ,WAAW;AACnB,QAAQ,UAAU;AAClB,QAAQ,OAAO;AACf,QAAQ,eAAe;AACvB,QAAQ,MAAM;AACd,QAAQ,KAAK;AACb,QAAQ,MAAM;AACd,QAAQ,KAAK;AACb,QAAQ,QAAQ;AAChB,QAAQ,QAAQ;AAChB,QAAQ,YAAY;AACpB,QAAQ,UAAU;AAClB,QAAQ,SAAS;AACjB,QAAQ,OAAO;AACf,QAAQ,SAAS;AACjB,QAAQ,QAAQ;AAChB,QAAQ,KAAK;AACb,QAAQ,QAAQ;AAChB,QAAQ,QAAQ;AAChB,QAAQ,aAAa;AACrB,QAAQ,aAAa;AACrB,QAAQ,YAAY;AACpB,QAAQ,mBAAmB;AAC3B,QAAQ,WAAW;AACnB,QAAQ,UAAU;AAClB,QAAQ,SAAS;AACjB,QAAQ,SAAS;AACjB,KAAK,CAAC;AACN,EAAC;AACD,MAAM,WAAW,GAAG,IAAI,GAAG;AAC3B,IAAI;AACJ,QAAQ,KAAK,CAAC,OAAO;AACrB,QAAQ,KAAK,CAAC,EAAE;AAChB,QAAQ,KAAK,CAAC,SAAS,CAAC,EAAE;AAC1B,QAAQ,KAAK,CAAC,SAAS,CAAC,MAAM;AAC9B,QAAQ,KAAK,CAAC,SAAS,CAAC,OAAO;AAC/B,QAAQ,KAAK,CAAC,SAAS,CAAC,KAAK;AAC7B,QAAQ,KAAK,CAAC,SAAS,CAAC,MAAM;AAC9B,QAAQ,KAAK,CAAC,SAAS,CAAC,IAAI;AAC5B,QAAQ,KAAK,CAAC,SAAS,CAAC,SAAS;AACjC,QAAQ,KAAK,CAAC,SAAS,CAAC,IAAI;AAC5B,QAAQ,KAAK,CAAC,SAAS,CAAC,QAAQ;AAChC,QAAQ,KAAK,CAAC,SAAS,CAAC,OAAO;AAC/B,QAAQ,KAAK,CAAC,SAAS,CAAC,IAAI;AAC5B,QAAQ,KAAK,CAAC,SAAS,CAAC,IAAI;AAC5B,QAAQ,KAAK,CAAC,SAAS,CAAC,WAAW;AACnC,QAAQ,KAAK,CAAC,SAAS,CAAC,KAAK;AAC7B,QAAQ,KAAK,CAAC,SAAS,CAAC,IAAI;AAC5B,QAAQ,KAAK,CAAC,SAAS,CAAC,QAAQ;AAChC,QAAQ,KAAK,CAAC,SAAS,CAAC,MAAM;AAC9B,QAAQ,OAAO,MAAM,KAAK,UAAU,GAAG,MAAM,GAAG,SAAS;AACzD,QAAQ,OAAO;AACf,QAAQ,IAAI;AACZ,QAAQ,IAAI,CAAC,KAAK;AAClB,QAAQ,SAAS;AACjB,QAAQ,kBAAkB;AAC1B,QAAQ,SAAS;AACjB,QAAQ,kBAAkB;AAC1B,QAAQ,MAAM;AACd,QAAQ,QAAQ;AAChB,QAAQ,KAAK;AACb;AACA,QAAQ,aAAa;AACrB,QAAQ,GAAG;AACX,QAAQ,GAAG,CAAC,SAAS,CAAC,OAAO;AAC7B,QAAQ,GAAG,CAAC,SAAS,CAAC,GAAG;AACzB,QAAQ,GAAG,CAAC,SAAS,CAAC,GAAG;AACzB,QAAQ,GAAG,CAAC,SAAS,CAAC,IAAI;AAC1B,QAAQ,GAAG,CAAC,SAAS,CAAC,MAAM;AAC5B,QAAQ,wCAAwC;AAChD,YAAY,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC;AAC5C;AACA,aAAa,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,QAAQ,CAAC;AAC1C,aAAa,GAAG,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC;AAChC,aAAa,MAAM,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC,KAAK,UAAU,CAAC;AACnD,QAAQ,MAAM;AACd,QAAQ,MAAM,CAAC,QAAQ;AACvB,QAAQ,MAAM,CAAC,KAAK;AACpB,QAAQ,MAAM,CAAC,UAAU;AACzB,QAAQ,MAAM,CAAC,QAAQ;AACvB,QAAQ,MAAM,CAAC,SAAS,CAAC,aAAa;AACtC,QAAQ,MAAM,CAAC,SAAS,CAAC,OAAO;AAChC,QAAQ,MAAM,CAAC,SAAS,CAAC,WAAW;AACpC,QAAQ,MAAM,CAAC,SAAS,CAAC,QAAQ;AACjC,QAAQ,MAAM;AACd,QAAQ,MAAM,CAAC,OAAO;AACtB,QAAQ,MAAM,CAAC,EAAE;AACjB,QAAQ,MAAM,CAAC,YAAY;AAC3B,QAAQ,MAAM,CAAC,QAAQ;AACvB,QAAQ,MAAM,CAAC,QAAQ;AACvB,QAAQ,MAAM,CAAC,IAAI;AACnB,QAAQ,MAAM,CAAC,MAAM;AACrB,QAAQ,UAAU;AAClB,QAAQ,QAAQ;AAChB,QAAQ,MAAM;AACd,QAAQ,GAAG;AACX,QAAQ,GAAG,CAAC,SAAS,CAAC,OAAO;AAC7B,QAAQ,GAAG,CAAC,SAAS,CAAC,GAAG;AACzB,QAAQ,GAAG,CAAC,SAAS,CAAC,IAAI;AAC1B,QAAQ,GAAG,CAAC,SAAS,CAAC,MAAM;AAC5B,QAAQ,MAAM;AACd,QAAQ,MAAM,CAAC,YAAY;AAC3B,QAAQ,MAAM,CAAC,aAAa;AAC5B,QAAQ,MAAM,CAAC,GAAG;AAClB,QAAQ,MAAM,CAAC,SAAS,CAAC,EAAE;AAC3B,QAAQ,MAAM,CAAC,SAAS,CAAC,MAAM;AAC/B,QAAQ,MAAM,CAAC,SAAS,CAAC,UAAU;AACnC,QAAQ,MAAM,CAAC,SAAS,CAAC,WAAW;AACpC,QAAQ,MAAM,CAAC,SAAS,CAAC,MAAM;AAC/B,QAAQ,MAAM,CAAC,SAAS,CAAC,QAAQ;AACjC,QAAQ,MAAM,CAAC,SAAS,CAAC,QAAQ;AACjC,QAAQ,MAAM,CAAC,SAAS,CAAC,OAAO;AAChC,QAAQ,MAAM,CAAC,SAAS,CAAC,WAAW;AACpC,QAAQ,MAAM,CAAC,SAAS,CAAC,SAAS;AAClC,QAAQ,MAAM,CAAC,SAAS,CAAC,MAAM;AAC/B,QAAQ,MAAM,CAAC,SAAS,CAAC,QAAQ;AACjC,QAAQ,MAAM,CAAC,SAAS,CAAC,KAAK;AAC9B,QAAQ,MAAM,CAAC,SAAS,CAAC,UAAU;AACnC,QAAQ,MAAM,CAAC,SAAS,CAAC,MAAM;AAC/B,QAAQ,MAAM,CAAC,SAAS,CAAC,SAAS;AAClC,QAAQ,MAAM,CAAC,SAAS,CAAC,WAAW;AACpC,QAAQ,MAAM,CAAC,SAAS,CAAC,QAAQ;AACjC,QAAQ,MAAM,CAAC,SAAS,CAAC,WAAW;AACpC,QAAQ,MAAM,CAAC,SAAS,CAAC,IAAI;AAC7B,QAAQ,MAAM,CAAC,SAAS,CAAC,OAAO;AAChC,QAAQ,MAAM,CAAC,SAAS,CAAC,QAAQ;AACjC,QAAQ,MAAM,CAAC,SAAS,CAAC,SAAS;AAClC,QAAQ,MAAM,CAAC,SAAS,CAAC,SAAS;AAClC,QAAQ,MAAM,CAAC,GAAG;AAClB,QAAQ,MAAM,CAAC,MAAM;AACrB,QAAQ,QAAQ;AAChB,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC,KAAK,UAAU,CAAC;AAC5C,EAAC;AACD,MAAM,eAAe,GAAG,IAAI,GAAG,CAAC;AAChC,IAAI,MAAM,CAAC,MAAM;AACjB,IAAI,MAAM,CAAC,iBAAiB;AAC5B,IAAI,MAAM,CAAC,IAAI;AACf,CAAC,EAAC;AACF;AACA;AACA,MAAM,aAAa,GAAG;AACtB,IAAI,CAAC,GAAG,EAAE,IAAI,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;AAC5B,IAAI;AACJ,QAAQ,MAAM;AACd,QAAQ,IAAI,GAAG,CAAC;AAChB,YAAY,QAAQ;AACpB,YAAY,OAAO;AACnB,YAAY,QAAQ;AACpB,YAAY,YAAY;AACxB,YAAY,YAAY;AACxB,YAAY,WAAW;AACvB,YAAY,QAAQ;AACpB,YAAY,QAAQ;AACpB,YAAY,SAAS;AACrB,SAAS,CAAC;AACV,KAAK;AACL,IAAI,CAAC,GAAG,EAAE,IAAI,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;AAC5B,EAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,qBAAqB,CAAC,MAAM,EAAE,IAAI,EAAE;AAC7C,IAAI,IAAI,CAAC,GAAG,OAAM;AAClB,IAAI,OAAO,CAAC,OAAO,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,UAAU,KAAK,CAAC,KAAK,IAAI,EAAE;AAC7E,QAAQ,MAAM,CAAC,GAAG,MAAM,CAAC,wBAAwB,CAAC,CAAC,EAAE,IAAI,EAAC;AAC1D,QAAQ,IAAI,CAAC,EAAE;AACf,YAAY,OAAO,CAAC;AACpB,SAAS;AACT,QAAQ,CAAC,GAAG,MAAM,CAAC,cAAc,CAAC,CAAC,EAAC;AACpC,KAAK;AACL,IAAI,OAAO,IAAI;AACf,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,QAAQ,CAAC,MAAM,EAAE,IAAI,EAAE;AAChC,IAAI,MAAM,CAAC,GAAG,qBAAqB,CAAC,MAAM,EAAE,IAAI,EAAC;AACjD,IAAI,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI;AACrC,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,gBAAgB,CAAC,QAAQ,EAAE,YAAY,EAAE;AAClD,IAAI,MAAM,SAAS,GAAG,GAAE;AACxB;AACA,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;AAC9C,QAAQ,MAAM,WAAW,GAAG,QAAQ,CAAC,CAAC,EAAC;AACvC;AACA,QAAQ,IAAI,WAAW,IAAI,IAAI,EAAE;AACjC,YAAY,SAAS,CAAC,MAAM,GAAG,CAAC,GAAG,EAAC;AACpC,SAAS,MAAM,IAAI,WAAW,CAAC,IAAI,KAAK,eAAe,EAAE;AACzD,YAAY,MAAM,QAAQ,GAAG,eAAe,CAAC,WAAW,CAAC,QAAQ,EAAE,YAAY,EAAC;AAChF,YAAY,IAAI,QAAQ,IAAI,IAAI,EAAE;AAClC,gBAAgB,OAAO,IAAI;AAC3B,aAAa;AACb,YAAY,SAAS,CAAC,IAAI,CAAC,iCAAiC,QAAQ,CAAC,KAAK,CAAC,EAAC;AAC5E,SAAS,MAAM;AACf,YAAY,MAAM,OAAO,GAAG,eAAe,CAAC,WAAW,EAAE,YAAY,EAAC;AACtE,YAAY,IAAI,OAAO,IAAI,IAAI,EAAE;AACjC,gBAAgB,OAAO,IAAI;AAC3B,aAAa;AACb,YAAY,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAC;AACzC,SAAS;AACT,KAAK;AACL;AACA,IAAI,OAAO,SAAS;AACpB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,kBAAkB,CAAC,QAAQ,EAAE;AACtC,IAAI,MAAM,IAAI,GAAG,QAAQ,CAAC,WAAU;AACpC;AACA,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,OAAM;AACnD,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,OAAM;AAC3D,IAAI,IAAI,KAAK,KAAK,CAAC,IAAI,KAAK,GAAG,KAAK,KAAK,IAAI,CAAC,MAAM,EAAE;AACtD;AACA,QAAQ,OAAO,IAAI;AACnB,KAAK;AACL,IAAI,OAAO,KAAK;AAChB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC;AACjC,IAAI,eAAe,CAAC,IAAI,EAAE,YAAY,EAAE;AACxC,QAAQ,MAAM,QAAQ,GAAG,gBAAgB,CAAC,IAAI,CAAC,QAAQ,EAAE,YAAY,EAAC;AACtE,QAAQ,OAAO,QAAQ,IAAI,IAAI,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,IAAI;AAC5D,KAAK;AACL;AACA,IAAI,oBAAoB,CAAC,IAAI,EAAE,YAAY,EAAE;AAC7C,QAAQ,IAAI,IAAI,CAAC,QAAQ,KAAK,GAAG,EAAE;AACnC,YAAY,OAAO,eAAe,CAAC,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC;AAC5D,SAAS;AACT,QAAQ,OAAO,IAAI;AACnB,KAAK;AACL;AACA;AACA,IAAI,gBAAgB,CAAC,IAAI,EAAE,YAAY,EAAE;AACzC,QAAQ,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,IAAI,IAAI,CAAC,QAAQ,KAAK,YAAY,EAAE;AACtE;AACA,YAAY,OAAO,IAAI;AACvB,SAAS;AACT;AACA,QAAQ,MAAM,IAAI,GAAG,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,EAAC;AAC7D,QAAQ,MAAM,KAAK,GAAG,eAAe,CAAC,IAAI,CAAC,KAAK,EAAE,YAAY,EAAC;AAC/D,QAAQ,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE;AAC3C,YAAY,QAAQ,IAAI,CAAC,QAAQ;AACjC,gBAAgB,KAAK,IAAI;AACzB,oBAAoB,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE;AAC/D,gBAAgB,KAAK,IAAI;AACzB,oBAAoB,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE;AAC/D,gBAAgB,KAAK,KAAK;AAC1B,oBAAoB,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,KAAK,EAAE;AAChE,gBAAgB,KAAK,KAAK;AAC1B,oBAAoB,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,KAAK,EAAE;AAChE,gBAAgB,KAAK,GAAG;AACxB,oBAAoB,OAAO;AAC3B,wBAAwB,KAAK;AAC7B,+CAA+C,CAAC,IAAI,CAAC,KAAK;AAC1D,gDAAgD,KAAK,CAAC,KAAK,CAAC;AAC5D,qBAAqB;AACrB,gBAAgB,KAAK,IAAI;AACzB,oBAAoB,OAAO;AAC3B,wBAAwB,KAAK;AAC7B,+CAA+C,CAAC,IAAI,CAAC,KAAK;AAC1D,gDAAgD,KAAK,CAAC,KAAK,CAAC;AAC5D,qBAAqB;AACrB,gBAAgB,KAAK,GAAG;AACxB,oBAAoB,OAAO;AAC3B,wBAAwB,KAAK;AAC7B,+CAA+C,CAAC,IAAI,CAAC,KAAK;AAC1D,gDAAgD,KAAK,CAAC,KAAK,CAAC;AAC5D,qBAAqB;AACrB,gBAAgB,KAAK,IAAI;AACzB,oBAAoB,OAAO;AAC3B,wBAAwB,KAAK;AAC7B,+CAA+C,CAAC,IAAI,CAAC,KAAK;AAC1D,gDAAgD,KAAK,CAAC,KAAK,CAAC;AAC5D,qBAAqB;AACrB,gBAAgB,KAAK,IAAI;AACzB,oBAAoB,OAAO;AAC3B,wBAAwB,KAAK;AAC7B,+CAA+C,CAAC,IAAI,CAAC,KAAK;AAC1D,gDAAgD,KAAK,CAAC,KAAK,CAAC;AAC5D,qBAAqB;AACrB,gBAAgB,KAAK,IAAI;AACzB,oBAAoB,OAAO;AAC3B,wBAAwB,KAAK;AAC7B,+CAA+C,CAAC,IAAI,CAAC,KAAK;AAC1D,gDAAgD,KAAK,CAAC,KAAK,CAAC;AAC5D,qBAAqB;AACrB,gBAAgB,KAAK,KAAK;AAC1B,oBAAoB,OAAO;AAC3B,wBAAwB,KAAK;AAC7B,+CAA+C,CAAC,IAAI,CAAC,KAAK;AAC1D,gDAAgD,KAAK,CAAC,KAAK,CAAC;AAC5D,qBAAqB;AACrB,gBAAgB,KAAK,GAAG;AACxB,oBAAoB,OAAO;AAC3B,wBAAwB,KAAK;AAC7B,+CAA+C,CAAC,IAAI,CAAC,KAAK;AAC1D,gDAAgD,KAAK,CAAC,KAAK,CAAC;AAC5D,qBAAqB;AACrB,gBAAgB,KAAK,GAAG;AACxB,oBAAoB,OAAO;AAC3B,wBAAwB,KAAK;AAC7B,+CAA+C,CAAC,IAAI,CAAC,KAAK;AAC1D,gDAAgD,KAAK,CAAC,KAAK,CAAC;AAC5D,qBAAqB;AACrB,gBAAgB,KAAK,GAAG;AACxB,oBAAoB,OAAO;AAC3B,wBAAwB,KAAK;AAC7B,+CAA+C,CAAC,IAAI,CAAC,KAAK;AAC1D,gDAAgD,KAAK,CAAC,KAAK,CAAC;AAC5D,qBAAqB;AACrB,gBAAgB,KAAK,GAAG;AACxB,oBAAoB,OAAO;AAC3B,wBAAwB,KAAK;AAC7B,+CAA+C,CAAC,IAAI,CAAC,KAAK;AAC1D,gDAAgD,KAAK,CAAC,KAAK,CAAC;AAC5D,qBAAqB;AACrB,gBAAgB,KAAK,GAAG;AACxB,oBAAoB,OAAO;AAC3B,wBAAwB,KAAK;AAC7B,+CAA+C,CAAC,IAAI,CAAC,KAAK;AAC1D,gDAAgD,KAAK,CAAC,KAAK,CAAC;AAC5D,qBAAqB;AACrB,gBAAgB,KAAK,IAAI;AACzB,oBAAoB,OAAO;AAC3B,wBAAwB,KAAK;AAC7B,+CAA+C,CAAC,IAAI,CAAC,KAAK;AAC1D,gDAAgD,KAAK,CAAC,KAAK,CAAC;AAC5D,qBAAqB;AACrB,gBAAgB,KAAK,GAAG;AACxB,oBAAoB,OAAO;AAC3B,wBAAwB,KAAK;AAC7B,+CAA+C,CAAC,IAAI,CAAC,KAAK;AAC1D,gDAAgD,KAAK,CAAC,KAAK,CAAC;AAC5D,qBAAqB;AACrB,gBAAgB,KAAK,GAAG;AACxB,oBAAoB,OAAO;AAC3B,wBAAwB,KAAK;AAC7B,+CAA+C,CAAC,IAAI,CAAC,KAAK;AAC1D,gDAAgD,KAAK,CAAC,KAAK,CAAC;AAC5D,qBAAqB;AACrB,gBAAgB,KAAK,GAAG;AACxB,oBAAoB,OAAO;AAC3B,wBAAwB,KAAK;AAC7B,+CAA+C,CAAC,IAAI,CAAC,KAAK;AAC1D,gDAAgD,KAAK,CAAC,KAAK,CAAC;AAC5D,qBAAqB;AACrB;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA,QAAQ,OAAO,IAAI;AACnB,KAAK;AACL;AACA,IAAI,cAAc,CAAC,IAAI,EAAE,YAAY,EAAE;AACvC,QAAQ,MAAM,UAAU,GAAG,IAAI,CAAC,OAAM;AACtC,QAAQ,MAAM,IAAI,GAAG,gBAAgB,CAAC,IAAI,CAAC,SAAS,EAAE,YAAY,EAAC;AACnE;AACA,QAAQ,IAAI,IAAI,IAAI,IAAI,EAAE;AAC1B,YAAY,IAAI,UAAU,CAAC,IAAI,KAAK,kBAAkB,EAAE;AACxD,gBAAgB,IAAI,UAAU,CAAC,QAAQ,CAAC,IAAI,KAAK,mBAAmB,EAAE;AACtE,oBAAoB,OAAO,IAAI;AAC/B,iBAAiB;AACjB,gBAAgB,MAAM,MAAM,GAAG,eAAe,CAAC,UAAU,CAAC,MAAM,EAAE,YAAY,EAAC;AAC/E,gBAAgB,IAAI,MAAM,IAAI,IAAI,EAAE;AACpC,oBAAoB;AACpB,wBAAwB,MAAM,CAAC,KAAK,IAAI,IAAI;AAC5C,yBAAyB,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC;AAC1D,sBAAsB;AACtB,wBAAwB,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE;AACnE,qBAAqB;AACrB,oBAAoB,MAAM,QAAQ,GAAG,0BAA0B;AAC/D,wBAAwB,UAAU;AAClC,wBAAwB,YAAY;AACpC,sBAAqB;AACrB;AACA,oBAAoB,IAAI,QAAQ,IAAI,IAAI,EAAE;AAC1C,wBAAwB,MAAM,QAAQ;AACtC;AACA,gCAAgC,MAAM,CAAC,KAAK;AAC5C,8BAA6B;AAC7B,wBAAwB,MAAM,UAAU;AACxC,4BAA4B,QAAQ,CAAC,KAAK;AAC1C,0BAAyB;AACzB,wBAAwB,IAAI,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,EAAE;AACnE,4BAA4B,OAAO;AACnC,gCAAgC,KAAK,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC;AACpE,6BAA6B;AAC7B,yBAAyB;AACzB,wBAAwB,IAAI,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,EAAE;AACvE,4BAA4B,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE;AACrD,yBAAyB;AACzB,qBAAqB;AACrB,iBAAiB;AACjB,aAAa,MAAM;AACnB,gBAAgB,MAAM,MAAM,GAAG,eAAe,CAAC,UAAU,EAAE,YAAY,EAAC;AACxE,gBAAgB,IAAI,MAAM,IAAI,IAAI,EAAE;AACpC,oBAAoB,IAAI,MAAM,CAAC,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE;AAC/D,wBAAwB,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE;AACnE,qBAAqB;AACrB,oBAAoB,MAAM,IAAI;AAC9B,wBAAwB,MAAM,CAAC,KAAK;AACpC,sBAAqB;AACrB,oBAAoB,IAAI,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AAC/C,wBAAwB,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE;AACvD,qBAAqB;AACrB,oBAAoB,IAAI,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AACnD,wBAAwB,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE;AACjD,qBAAqB;AACrB,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT;AACA,QAAQ,OAAO,IAAI;AACnB,KAAK;AACL;AACA,IAAI,qBAAqB,CAAC,IAAI,EAAE,YAAY,EAAE;AAC9C,QAAQ,MAAM,IAAI,GAAG,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,EAAC;AAC7D,QAAQ,IAAI,IAAI,IAAI,IAAI,EAAE;AAC1B,YAAY,OAAO,IAAI,CAAC,KAAK;AAC7B,kBAAkB,eAAe,CAAC,IAAI,CAAC,UAAU,EAAE,YAAY,CAAC;AAChE,kBAAkB,eAAe,CAAC,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC;AAC/D,SAAS;AACT,QAAQ,OAAO,IAAI;AACnB,KAAK;AACL;AACA,IAAI,mBAAmB,CAAC,IAAI,EAAE,YAAY,EAAE;AAC5C,QAAQ,OAAO,eAAe,CAAC,IAAI,CAAC,UAAU,EAAE,YAAY,CAAC;AAC7D,KAAK;AACL;AACA,IAAI,UAAU,CAAC,IAAI,EAAE,YAAY,EAAE;AACnC,QAAQ,IAAI,YAAY,IAAI,IAAI,EAAE;AAClC,YAAY,MAAM,QAAQ,GAAG,YAAY,CAAC,YAAY,EAAE,IAAI,EAAC;AAC7D;AACA;AACA,YAAY;AACZ,gBAAgB,QAAQ,IAAI,IAAI;AAChC,gBAAgB,QAAQ,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC;AAC1C,gBAAgB,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC;AAC/C,gBAAgB,QAAQ,CAAC,IAAI,IAAI,YAAY;AAC7C,cAAc;AACd,gBAAgB,OAAO,EAAE,KAAK,EAAE,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AAC7D,aAAa;AACb;AACA;AACA,YAAY,IAAI,QAAQ,IAAI,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;AAChE,gBAAgB,MAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAC;AAC5C,gBAAgB;AAChB,oBAAoB,GAAG,CAAC,MAAM;AAC9B,oBAAoB,GAAG,CAAC,IAAI,KAAK,UAAU;AAC3C,qBAAqB,GAAG,CAAC,MAAM,CAAC,IAAI,KAAK,OAAO;AAChD,wBAAwB,kBAAkB,CAAC,QAAQ,CAAC,CAAC;AACrD;AACA,oBAAoB,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,YAAY;AACrD,kBAAkB;AAClB,oBAAoB,OAAO,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,CAAC;AACvE,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT,QAAQ,OAAO,IAAI;AACnB,KAAK;AACL;AACA,IAAI,OAAO,CAAC,IAAI,EAAE;AAClB,QAAQ,MAAM,OAAO;AACrB;AACA,gBAAgB,IAAI;AACpB,cAAa;AACb;AACA,QAAQ;AACR,YAAY,CAAC,OAAO,CAAC,KAAK,IAAI,IAAI,IAAI,OAAO,CAAC,MAAM,IAAI,IAAI;AAC5D,YAAY,OAAO,CAAC,KAAK,IAAI,IAAI;AACjC,UAAU;AACV;AACA,YAAY,OAAO,IAAI;AACvB,SAAS;AACT,QAAQ,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE;AACvC,KAAK;AACL;AACA,IAAI,iBAAiB,CAAC,IAAI,EAAE,YAAY,EAAE;AAC1C,QAAQ,MAAM,IAAI,GAAG,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,EAAC;AAC7D,QAAQ,IAAI,IAAI,IAAI,IAAI,EAAE;AAC1B,YAAY;AACZ,gBAAgB,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI;AACvE,iBAAiB,IAAI,CAAC,QAAQ,KAAK,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC;AACzE,iBAAiB,IAAI,CAAC,QAAQ,KAAK,IAAI,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC;AAC9D,cAAc;AACd,gBAAgB,OAAO,IAAI;AAC3B,aAAa;AACb;AACA,YAAY,MAAM,KAAK,GAAG,eAAe,CAAC,IAAI,CAAC,KAAK,EAAE,YAAY,EAAC;AACnE,YAAY,IAAI,KAAK,IAAI,IAAI,EAAE;AAC/B,gBAAgB,OAAO,KAAK;AAC5B,aAAa;AACb,SAAS;AACT;AACA,QAAQ,OAAO,IAAI;AACnB,KAAK;AACL;AACA,IAAI,gBAAgB,CAAC,IAAI,EAAE,YAAY,EAAE;AACzC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,mBAAmB,EAAE;AACxD,YAAY,OAAO,IAAI;AACvB,SAAS;AACT,QAAQ,MAAM,MAAM,GAAG,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,EAAC;AACjE,QAAQ,IAAI,MAAM,IAAI,IAAI,EAAE;AAC5B,YAAY,IAAI,MAAM,CAAC,KAAK,IAAI,IAAI,KAAK,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE;AAC5E,gBAAgB,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE;AAC3D,aAAa;AACb,YAAY,MAAM,QAAQ,GAAG,0BAA0B,CAAC,IAAI,EAAE,YAAY,EAAC;AAC3E;AACA,YAAY,IAAI,QAAQ,IAAI,IAAI,EAAE;AAClC,gBAAgB;AAChB,oBAAoB,CAAC,QAAQ;AAC7B,+CAA+C,MAAM,CAAC,KAAK;AAC3D,oDAAoD,QAAQ,CAAC,KAAK;AAClE,qBAAqB;AACrB,kBAAkB;AAClB,oBAAoB,OAAO;AAC3B,wBAAwB,KAAK,8CAA8C;AAC3E,4BAA4B,MAAM,CAAC,KAAK;AACxC,sDAAsD,QAAQ,CAAC,KAAK,EAAE;AACtE,qBAAqB;AACrB,iBAAiB;AACjB;AACA,gBAAgB,KAAK,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,IAAI,aAAa,EAAE;AAChE,oBAAoB;AACpB,wBAAwB,MAAM,CAAC,KAAK,YAAY,OAAO;AACvD,wBAAwB,OAAO,CAAC,GAAG,wBAAwB,QAAQ,CAAC,KAAK,EAAE;AAC3E,sBAAsB;AACtB,wBAAwB,OAAO;AAC/B,4BAA4B,KAAK,8CAA8C;AAC/E,gCAAgC,MAAM,CAAC,KAAK;AAC5C,0DAA0D,QAAQ,CAAC,KAAK,EAAE;AAC1E,yBAAyB;AACzB,qBAAqB;AACrB,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT,QAAQ,OAAO,IAAI;AACnB,KAAK;AACL;AACA,IAAI,eAAe,CAAC,IAAI,EAAE,YAAY,EAAE;AACxC,QAAQ,MAAM,UAAU,GAAG,eAAe,CAAC,IAAI,CAAC,UAAU,EAAE,YAAY,EAAC;AACzE,QAAQ,IAAI,UAAU,IAAI,IAAI,EAAE;AAChC,YAAY,OAAO,EAAE,KAAK,EAAE,UAAU,CAAC,KAAK,EAAE;AAC9C,SAAS;AACT,QAAQ,OAAO,IAAI;AACnB,KAAK;AACL;AACA,IAAI,aAAa,CAAC,IAAI,EAAE,YAAY,EAAE;AACtC,QAAQ,MAAM,MAAM,GAAG,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,EAAC;AACjE,QAAQ,MAAM,IAAI,GAAG,gBAAgB,CAAC,IAAI,CAAC,SAAS,EAAE,YAAY,EAAC;AACnE;AACA,QAAQ,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAC5C,YAAY,MAAM,IAAI;AACtB,gBAAgB,MAAM,CAAC,KAAK;AAC5B,cAAa;AACb,YAAY,IAAI,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AACvC,gBAAgB,OAAO,EAAE,KAAK,EAAE,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE;AACnD,aAAa;AACb,SAAS;AACT;AACA,QAAQ,OAAO,IAAI;AACnB,KAAK;AACL;AACA,IAAI,gBAAgB,CAAC,IAAI,EAAE,YAAY,EAAE;AACzC;AACA,QAAQ,MAAM,MAAM,GAAG,GAAE;AACzB;AACA,QAAQ,KAAK,MAAM,YAAY,IAAI,IAAI,CAAC,UAAU,EAAE;AACpD,YAAY,IAAI,YAAY,CAAC,IAAI,KAAK,UAAU,EAAE;AAClD,gBAAgB,IAAI,YAAY,CAAC,IAAI,KAAK,MAAM,EAAE;AAClD,oBAAoB,OAAO,IAAI;AAC/B,iBAAiB;AACjB,gBAAgB,MAAM,GAAG,GAAG,0BAA0B;AACtD,oBAAoB,YAAY;AAChC,oBAAoB,YAAY;AAChC,kBAAiB;AACjB,gBAAgB,MAAM,KAAK,GAAG,eAAe,CAAC,YAAY,CAAC,KAAK,EAAE,YAAY,EAAC;AAC/E,gBAAgB,IAAI,GAAG,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE;AAClD,oBAAoB,OAAO,IAAI;AAC/B,iBAAiB;AACjB,gBAAgB,MAAM,6BAA6B,GAAG,CAAC,KAAK,EAAE,GAAG,KAAK,CAAC,MAAK;AAC5E,aAAa,MAAM;AACnB,gBAAgB,YAAY,CAAC,IAAI,KAAK,eAAe;AACrD;AACA,gBAAgB,YAAY,CAAC,IAAI,KAAK,4BAA4B;AAClE,cAAc;AACd,gBAAgB,MAAM,QAAQ,GAAG,eAAe;AAChD,oBAAoB,YAAY,CAAC,QAAQ;AACzC,oBAAoB,YAAY;AAChC,kBAAiB;AACjB,gBAAgB,IAAI,QAAQ,IAAI,IAAI,EAAE;AACtC,oBAAoB,OAAO,IAAI;AAC/B,iBAAiB;AACjB,gBAAgB,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,KAAK,EAAC;AACrD,aAAa,MAAM;AACnB,gBAAgB,OAAO,IAAI;AAC3B,aAAa;AACb,SAAS;AACT;AACA,QAAQ,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE;AAChC,KAAK;AACL;AACA,IAAI,kBAAkB,CAAC,IAAI,EAAE,YAAY,EAAE;AAC3C,QAAQ,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAC;AAClE,QAAQ,OAAO,eAAe,CAAC,IAAI,EAAE,YAAY,CAAC;AAClD,KAAK;AACL;AACA,IAAI,wBAAwB,CAAC,IAAI,EAAE,YAAY,EAAE;AACjD,QAAQ,MAAM,GAAG,GAAG,eAAe,CAAC,IAAI,CAAC,GAAG,EAAE,YAAY,EAAC;AAC3D,QAAQ,MAAM,WAAW,GAAG,gBAAgB;AAC5C,YAAY,IAAI,CAAC,KAAK,CAAC,WAAW;AAClC,YAAY,YAAY;AACxB,UAAS;AACT;AACA,QAAQ,IAAI,GAAG,IAAI,IAAI,IAAI,WAAW,IAAI,IAAI,EAAE;AAChD,YAAY,MAAM,IAAI,2CAA2C,GAAG,CAAC,KAAK,EAAC;AAC3E;AACA,YAAY,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,MAAM,EAAC;AACxE,YAAY,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,GAAG,EAAC;AACnE;AACA,YAAY,IAAI,IAAI,KAAK,MAAM,CAAC,GAAG,EAAE;AACrC,gBAAgB,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,OAAO,EAAE,GAAG,WAAW,CAAC,EAAE;AAC/D,aAAa;AACb,SAAS;AACT;AACA,QAAQ,OAAO,IAAI;AACnB,KAAK;AACL;AACA,IAAI,eAAe,CAAC,IAAI,EAAE,YAAY,EAAE;AACxC,QAAQ,MAAM,WAAW,GAAG,gBAAgB,CAAC,IAAI,CAAC,WAAW,EAAE,YAAY,EAAC;AAC5E,QAAQ,IAAI,WAAW,IAAI,IAAI,EAAE;AACjC,YAAY,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAM;AACnD,YAAY,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;AACzD,gBAAgB,KAAK,IAAI,WAAW,CAAC,CAAC,EAAC;AACvC,gBAAgB,KAAK,2BAA2B,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,EAAC;AAChF,aAAa;AACb,YAAY,OAAO,EAAE,KAAK,EAAE;AAC5B,SAAS;AACT,QAAQ,OAAO,IAAI;AACnB,KAAK;AACL;AACA,IAAI,eAAe,CAAC,IAAI,EAAE,YAAY,EAAE;AACxC,QAAQ,IAAI,IAAI,CAAC,QAAQ,KAAK,QAAQ,EAAE;AACxC;AACA,YAAY,OAAO,IAAI;AACvB,SAAS;AACT,QAAQ,IAAI,IAAI,CAAC,QAAQ,KAAK,MAAM,EAAE;AACtC,YAAY,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE;AACvC,SAAS;AACT;AACA,QAAQ,MAAM,GAAG,GAAG,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,YAAY,EAAC;AAChE,QAAQ,IAAI,GAAG,IAAI,IAAI,EAAE;AACzB,YAAY,QAAQ,IAAI,CAAC,QAAQ;AACjC,gBAAgB,KAAK,GAAG;AACxB,oBAAoB,OAAO,EAAE,KAAK,EAAE,sBAAsB,GAAG,CAAC,KAAK,EAAE,EAAE;AACvE,gBAAgB,KAAK,GAAG;AACxB,oBAAoB,OAAO,EAAE,KAAK,EAAE,sBAAsB,GAAG,CAAC,KAAK,EAAE,EAAE;AACvE,gBAAgB,KAAK,GAAG;AACxB,oBAAoB,OAAO,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE;AAChD,gBAAgB,KAAK,GAAG;AACxB,oBAAoB,OAAO,EAAE,KAAK,EAAE,sBAAsB,GAAG,CAAC,KAAK,EAAE,EAAE;AACvE,gBAAgB,KAAK,QAAQ;AAC7B,oBAAoB,OAAO,EAAE,KAAK,EAAE,OAAO,GAAG,CAAC,KAAK,EAAE;AACtD;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA,QAAQ,OAAO,IAAI;AACnB,KAAK;AACL,IAAI,cAAc,CAAC,IAAI,EAAE,YAAY,EAAE;AACvC,QAAQ,OAAO,eAAe,CAAC,IAAI,CAAC,UAAU,EAAE,YAAY,CAAC;AAC7D,KAAK;AACL,IAAI,qBAAqB,CAAC,IAAI,EAAE,YAAY,EAAE;AAC9C,QAAQ,OAAO,eAAe,CAAC,IAAI,CAAC,UAAU,EAAE,YAAY,CAAC;AAC7D,KAAK;AACL,IAAI,eAAe,CAAC,IAAI,EAAE,YAAY,EAAE;AACxC,QAAQ,OAAO,eAAe,CAAC,IAAI,CAAC,UAAU,EAAE,YAAY,CAAC;AAC7D,KAAK;AACL,IAAI,mBAAmB,CAAC,IAAI,EAAE,YAAY,EAAE;AAC5C,QAAQ,OAAO,eAAe,CAAC,IAAI,CAAC,UAAU,EAAE,YAAY,CAAC;AAC7D,KAAK;AACL,IAAI,yBAAyB,CAAC,IAAI,EAAE,YAAY,EAAE;AAClD,QAAQ,OAAO,eAAe,CAAC,IAAI,CAAC,UAAU,EAAE,YAAY,CAAC;AAC7D,KAAK;AACL,CAAC,EAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,eAAe,CAAC,IAAI,EAAE,YAAY,EAAE;AAC7C,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE;AAC3E,QAAQ,2CAA2C,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;AACzE,yCAAyC,IAAI;AAC7C,YAAY,YAAY;AACxB,SAAS;AACT,KAAK;AACL,IAAI,OAAO,IAAI;AACf,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,0BAA0B,CAAC,IAAI,EAAE,YAAY,EAAE;AACxD,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,KAAK,UAAU,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,SAAQ;AACxE;AACA,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE;AACvB,QAAQ,OAAO,eAAe,CAAC,QAAQ,EAAE,YAAY,CAAC;AACtD,KAAK;AACL;AACA,IAAI,IAAI,QAAQ,CAAC,IAAI,KAAK,YAAY,EAAE;AACxC,QAAQ,OAAO,EAAE,KAAK,EAAE,QAAQ,CAAC,IAAI,EAAE;AACvC,KAAK;AACL;AACA,IAAI,IAAI,QAAQ,CAAC,IAAI,KAAK,SAAS,EAAE;AACrC,QAAQ,0CAA0C,CAAC,QAAQ,EAAE,MAAM,EAAE;AACrE,YAAY,OAAO,EAAE,KAAK,+BAA+B,CAAC,QAAQ,EAAE,MAAM,EAAE;AAC5E,SAAS;AACT,QAAQ,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;AAChD,KAAK;AACL;AACA,IAAI,OAAO,IAAI;AACf,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,cAAc,CAAC,IAAI,EAAE,YAAY,GAAG,IAAI,EAAE;AAC1D,IAAI,IAAI;AACR,QAAQ,OAAO,eAAe,CAAC,IAAI,EAAE,YAAY,CAAC;AAClD,KAAK,CAAC,OAAO,MAAM,EAAE;AACrB,QAAQ,OAAO,IAAI;AACnB,KAAK;AACL;;ACtzBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,mBAAmB,CAAC,IAAI,EAAE,YAAY,GAAG,IAAI,EAAE;AAC/D;AACA,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,EAAE;AAChE,QAAQ,MAAM,OAAO;AACrB;AACA,gBAAgB,IAAI;AACpB,cAAa;AACb,QAAQ,IAAI,OAAO,CAAC,KAAK,EAAE;AAC3B,YAAY,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACrE,SAAS;AACT,QAAQ,IAAI,OAAO,CAAC,MAAM,EAAE;AAC5B,YAAY,OAAO,OAAO,CAAC,MAAM;AACjC,SAAS;AACT,KAAK;AACL;AACA,IAAI,MAAM,SAAS,GAAG,cAAc,CAAC,IAAI,EAAE,YAAY,EAAC;AACxD;AACA,IAAI,IAAI,SAAS,EAAE;AACnB;AACA,QAAQ,IAAI;AACZ,YAAY,OAAO,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC;AAC1C,SAAS,CAAC,MAAM;AAChB;AACA,SAAS;AACT,KAAK;AACL;AACA,IAAI,OAAO,IAAI;AACf;;ACvCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,eAAe,CAAC,IAAI,EAAE,YAAY,EAAE;AACpD,IAAI,QAAQ,IAAI,CAAC,IAAI;AACrB,QAAQ,KAAK,kBAAkB;AAC/B,YAAY,IAAI,IAAI,CAAC,QAAQ,EAAE;AAC/B,gBAAgB,OAAO,mBAAmB,CAAC,IAAI,CAAC,QAAQ,EAAE,YAAY,CAAC;AACvE,aAAa;AACb,YAAY,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,mBAAmB,EAAE;AAC5D,gBAAgB,OAAO,IAAI;AAC3B,aAAa;AACb,YAAY,0CAA0C,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI;AAC1E;AACA,QAAQ,KAAK,UAAU,CAAC;AACxB,QAAQ,KAAK,kBAAkB,CAAC;AAChC,QAAQ,KAAK,oBAAoB;AACjC,YAAY,IAAI,IAAI,CAAC,QAAQ,EAAE;AAC/B,gBAAgB,OAAO,mBAAmB,CAAC,IAAI,CAAC,GAAG,EAAE,YAAY,CAAC;AAClE,aAAa;AACb,YAAY,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,SAAS,EAAE;AAC7C,gBAAgB,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;AAC7C,aAAa;AACb,YAAY,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,mBAAmB,EAAE;AACvD,gBAAgB,OAAO,IAAI;AAC3B,aAAa;AACb,YAAY,0CAA0C,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI;AAIrE,KAAK;AACL;AACA,IAAI,OAAO,IAAI;AACf;;AC3CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,uBAAuB,CAAC,IAAI,EAAE,UAAU,EAAE;AAC1D,IAAI,MAAM,MAAM,2BAA2B,CAAC,IAAI,EAAE,OAAM;AACxD,IAAI,MAAM,MAAM,GAAG,GAAE;AACrB,IAAI,MAAM,cAAc,GAAG,MAAM,CAAC,IAAI,KAAK,UAAU,IAAI,MAAM,CAAC,KAAK,KAAK,KAAI;AAC9E,IAAI,MAAM,aAAa;AACvB,QAAQ,MAAM,CAAC,IAAI,KAAK,kBAAkB,IAAI,MAAM,CAAC,KAAK,KAAK,KAAI;AACnE,IAAI,MAAM,kBAAkB;AAC5B,QAAQ,MAAM,CAAC,IAAI,KAAK,oBAAoB,IAAI,MAAM,CAAC,KAAK,KAAK,KAAI;AACrE;AACA;AACA,IAAI,IAAI,aAAa,IAAI,kBAAkB,EAAE;AAC7C,QAAQ,IAAI,MAAM,CAAC,MAAM,EAAE;AAC3B,YAAY,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAC;AACjC,SAAS;AACT,QAAQ,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,KAAK,mBAAmB,EAAE;AACrD,YAAY,MAAM,CAAC,IAAI,CAAC,SAAS,EAAC;AAClC,SAAS;AACT,KAAK;AACL,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;AACpB,QAAQ,MAAM,CAAC,IAAI,CAAC,OAAO,EAAC;AAC5B,KAAK;AACL,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;AACxB,QAAQ,MAAM,CAAC,IAAI,CAAC,WAAW,EAAC;AAChC,KAAK;AACL;AACA;AACA,IAAI,IAAI,cAAc,IAAI,aAAa,EAAE;AACzC,QAAQ,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa,EAAE;AAC3C,YAAY,OAAO,aAAa;AAChC,SAAS;AACT,QAAQ,IAAI,MAAM,CAAC,IAAI,KAAK,KAAK,EAAE;AACnC,YAAY,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAC;AACjC,SAAS,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,KAAK,EAAE;AAC1C,YAAY,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAC;AACjC,SAAS,MAAM;AACf,YAAY,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAC;AACjC,SAAS;AACT,KAAK,MAAM,IAAI,kBAAkB,EAAE;AACnC,QAAQ,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAC;AAC7B,KAAK,MAAM;AACX,QAAQ,IAAI,IAAI,CAAC,IAAI,KAAK,yBAAyB,EAAE;AACrD,YAAY,MAAM,CAAC,IAAI,CAAC,OAAO,EAAC;AAChC,SAAS;AACT,QAAQ,MAAM,CAAC,IAAI,CAAC,UAAU,EAAC;AAC/B,KAAK;AACL;AACA;AACA,IAAI,IAAI,cAAc,IAAI,aAAa,IAAI,kBAAkB,EAAE;AAC/D,QAAQ,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,KAAK,mBAAmB,EAAE;AACrD,YAAY,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAC;AAC9C,SAAS,MAAM;AACf,YAAY,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,EAAC;AAChD,YAAY,IAAI,IAAI,EAAE;AACtB,gBAAgB,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAC;AACxC,aAAa,MAAM,IAAI,UAAU,EAAE;AACnC,gBAAgB,MAAM,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,EAAC;AAC9D,gBAAgB,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AAC7C,oBAAoB,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAC;AAC/C,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT,KAAK,MAAM,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE;AAC5B,QAAQ,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAC;AACxC,KAAK,MAAM;AACX,QAAQ,MAAM,CAAC,IAAI,KAAK,oBAAoB;AAC5C,QAAQ,MAAM,CAAC,EAAE;AACjB,QAAQ,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,YAAY;AACvC,MAAM;AACN,QAAQ,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAC;AAC1C,KAAK,MAAM;AACX,QAAQ,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAsB;AAC/C,YAAY,MAAM,CAAC,IAAI,KAAK,mBAAmB;AAC/C,QAAQ,MAAM,CAAC,IAAI;AACnB,QAAQ,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,YAAY;AACzC,MAAM;AACN,QAAQ,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAC;AAC5C,KAAK,MAAM;AACX,QAAQ,MAAM,CAAC,IAAI,KAAK,0BAA0B;AAClD,QAAQ,MAAM,CAAC,WAAW,KAAK,IAAI;AACnC,MAAM;AACN,QAAQ,MAAM,CAAC,IAAI,CAAC,WAAW,EAAC;AAChC,KAAK;AACL;AACA,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;AAC3B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,SAAS,KAAK,CAAC,IAAI,EAAE;AACrB,IAAI,OAAO,OAAO;AAClB,yEAAyE,CAAC,IAAI;AAC9E,aAAa,EAAE;AACf,KAAK;AACL;;AC7GA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,uBAAuB,GAAG,MAAM,CAAC,MAAM;AAC7C,IAAI,IAAI,GAAG,CAAC;AACZ,QAAQ,IAAI;AACZ,QAAQ,IAAI;AACZ,QAAQ,GAAG;AACX,QAAQ,IAAI;AACZ,QAAQ,GAAG;AACX,QAAQ,IAAI;AACZ,QAAQ,IAAI;AACZ,QAAQ,IAAI;AACZ,QAAQ,KAAK;AACb,QAAQ,GAAG;AACX,QAAQ,GAAG;AACX,QAAQ,GAAG;AACX,QAAQ,GAAG;AACX,QAAQ,GAAG;AACX,QAAQ,GAAG;AACX,QAAQ,GAAG;AACX,QAAQ,GAAG;AACX,QAAQ,IAAI;AACZ,KAAK,CAAC;AACN,EAAC;AACD,MAAM,sBAAsB,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,EAAC;AAC3E;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,MAAM,CAAC,CAAC,EAAE;AACnB,IAAI,OAAO,CAAC,KAAK,IAAI,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,CAAC,IAAI,KAAK,QAAQ;AAC5E,CAAC;AACD;AACA,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM;AAC7B,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;AACvC;AACA;AACA;AACA;AACA;AACA,QAAQ,MAAM,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE;AAC3C,YAAY,MAAM,EAAE,IAAI,EAAE,GAAG,KAAI;AACjC;AACA,YAAY,IAAI,2BAA2B,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,KAAK,UAAU,EAAE;AACzE,gBAAgB,0BAA0B,CAAC,IAAI,EAAE,IAAI,CAAC;AACtD,oBAAoB,IAAI;AACxB,oBAAoB,OAAO;AAC3B,oBAAoB,WAAW;AAC/B,iBAAiB;AACjB,aAAa;AACb;AACA,YAAY,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,CAAC;AAClE,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE;AACnD,YAAY,MAAM,EAAE,IAAI,EAAE,GAAG,KAAI;AACjC;AACA,YAAY,KAAK,MAAM,GAAG;AAC1B,gBAAgB,WAAW,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC;AAClD,eAAe;AACf,gBAAgB,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,EAAC;AACvC;AACA,gBAAgB,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AAC1C,oBAAoB,KAAK,MAAM,OAAO,IAAI,KAAK,EAAE;AACjD,wBAAwB;AACxB,4BAA4B,MAAM,CAAC,OAAO,CAAC;AAC3C,4BAA4B,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,WAAW,CAAC;AACtE,0BAA0B;AAC1B,4BAA4B,OAAO,IAAI;AACvC,yBAAyB;AACzB,qBAAqB;AACrB,iBAAiB,MAAM;AACvB,oBAAoB,MAAM,CAAC,KAAK,CAAC;AACjC,oBAAoB,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,EAAE,WAAW,CAAC;AAC5D,kBAAkB;AAClB,oBAAoB,OAAO,IAAI;AAC/B,iBAAiB;AACjB,aAAa;AACb;AACA,YAAY,OAAO,KAAK;AACxB,SAAS;AACT;AACA,QAAQ,uBAAuB,GAAG;AAClC,YAAY,OAAO,KAAK;AACxB,SAAS;AACT,QAAQ,oBAAoB,GAAG;AAC/B,YAAY,OAAO,IAAI;AACvB,SAAS;AACT,QAAQ,eAAe,GAAG;AAC1B,YAAY,OAAO,IAAI;AACvB,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,QAAQ,gBAAgB,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE;AACrD,YAAY;AACZ,gBAAgB,OAAO,CAAC,8BAA8B;AACtD,gBAAgB,uBAAuB,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC1D,iBAAiB,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC;AAC/E,cAAc;AACd,gBAAgB,OAAO,IAAI;AAC3B,aAAa;AACb,YAAY,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,CAAC;AAClE,SAAS;AACT,QAAQ,cAAc,GAAG;AACzB,YAAY,OAAO,IAAI;AACvB,SAAS;AACT,QAAQ,kBAAkB,GAAG;AAC7B,YAAY,OAAO,KAAK;AACxB,SAAS;AACT,QAAQ,gBAAgB,GAAG;AAC3B,YAAY,OAAO,IAAI;AACvB,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,QAAQ,gBAAgB,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE;AACrD,YAAY,IAAI,OAAO,CAAC,eAAe,EAAE;AACzC,gBAAgB,OAAO,IAAI;AAC3B,aAAa;AACb,YAAY;AACZ,gBAAgB,OAAO,CAAC,8BAA8B;AACtD,gBAAgB,IAAI,CAAC,QAAQ;AAC7B,gBAAgB,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,SAAS;AAChD,cAAc;AACd,gBAAgB,OAAO,IAAI;AAC3B,aAAa;AACb,YAAY,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,CAAC;AAClE,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,QAAQ,gBAAgB,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE;AACrD,YAAY;AACZ,gBAAgB,OAAO,CAAC,8BAA8B;AACtD,gBAAgB,IAAI,CAAC,QAAQ;AAC7B,gBAAgB,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,SAAS;AAC3C,cAAc;AACd,gBAAgB,OAAO,IAAI;AAC3B,aAAa;AACb,YAAY,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,CAAC;AAClE,SAAS;AACT,QAAQ,aAAa,GAAG;AACxB,YAAY,OAAO,IAAI;AACvB,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,QAAQ,QAAQ,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE;AAC7C,YAAY;AACZ,gBAAgB,OAAO,CAAC,8BAA8B;AACtD,gBAAgB,IAAI,CAAC,QAAQ;AAC7B,gBAAgB,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,SAAS;AAC3C,cAAc;AACd,gBAAgB,OAAO,IAAI;AAC3B,aAAa;AACb,YAAY,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,CAAC;AAClE,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,QAAQ,kBAAkB,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE;AACvD,YAAY;AACZ,gBAAgB,OAAO,CAAC,8BAA8B;AACtD,gBAAgB,IAAI,CAAC,QAAQ;AAC7B,gBAAgB,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,SAAS;AAC3C,cAAc;AACd,gBAAgB,OAAO,IAAI;AAC3B,aAAa;AACb,YAAY,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,CAAC;AAClE,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,QAAQ,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE;AACpD,YAAY,IAAI,IAAI,CAAC,QAAQ,KAAK,QAAQ,EAAE;AAC5C,gBAAgB,OAAO,IAAI;AAC3B,aAAa;AACb,YAAY;AACZ,gBAAgB,OAAO,CAAC,8BAA8B;AACtD,gBAAgB,sBAAsB,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC;AACzD,gBAAgB,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,SAAS;AAChD,cAAc;AACd,gBAAgB,OAAO,IAAI;AAC3B,aAAa;AACb,YAAY,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,CAAC;AAClE,SAAS;AACT,QAAQ,gBAAgB,GAAG;AAC3B,YAAY,OAAO,IAAI;AACvB,SAAS;AACT,QAAQ,eAAe,GAAG;AAC1B,YAAY,OAAO,IAAI;AACvB,SAAS;AACT,KAAK,CAAC;AACN,EAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,aAAa,CAAC,IAAI,EAAE,UAAU,EAAE,OAAO,GAAG,EAAE,EAAE;AAC9D,IAAI,MAAM,EAAE,eAAe,GAAG,KAAK,EAAE,8BAA8B,GAAG,KAAK,EAAE;AAC7E,QAAQ,QAAO;AACf,IAAI,OAAO,OAAO,CAAC,MAAM;AACzB,QAAQ,IAAI;AACZ,QAAQ,EAAE,eAAe,EAAE,8BAA8B,EAAE;AAC3D,QAAQ,UAAU,CAAC,WAAW,IAAI,IAAI;AACtC,KAAK;AACL;;AC9OA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,oBAAoB,CAAC,IAAI,EAAE,UAAU,EAAE;AAChD,IAAI,MAAM,MAAM,2BAA2B,CAAC,IAAI,EAAE,OAAM;AACxD;AACA,IAAI,QAAQ,MAAM,CAAC,IAAI;AACvB,QAAQ,KAAK,gBAAgB,CAAC;AAC9B,QAAQ,KAAK,eAAe;AAC5B,YAAY,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;AAC/E,gBAAgB,OAAO,UAAU,CAAC,aAAa;AAC/C,oBAAoB,MAAM,CAAC,MAAM;AACjC,oBAAoB,mBAAmB;AACvC,iBAAiB;AACjB,aAAa;AACb,YAAY,OAAO,IAAI;AACvB;AACA,QAAQ,KAAK,kBAAkB;AAC/B,YAAY,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,EAAE;AACtC,gBAAgB,OAAO,UAAU,CAAC,aAAa;AAC/C,oBAAoB,MAAM,CAAC,IAAI;AAC/B,oBAAoB,mBAAmB;AACvC,iBAAiB;AACjB,aAAa;AACb,YAAY,OAAO,IAAI;AACvB;AACA,QAAQ,KAAK,aAAa,CAAC;AAC3B,QAAQ,KAAK,gBAAgB;AAC7B,YAAY,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,EAAE;AACtC,gBAAgB,OAAO,UAAU,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC;AAC1D,aAAa;AACb,YAAY,OAAO,IAAI;AACvB;AACA,QAAQ,KAAK,kBAAkB;AAC/B,YAAY,IAAI,MAAM,CAAC,MAAM,KAAK,IAAI,EAAE;AACxC,gBAAgB,OAAO,UAAU,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC;AAC1D,aAAa;AACb,YAAY,OAAO,IAAI;AACvB;AACA,QAAQ,KAAK,iBAAiB;AAC9B,YAAY,IAAI,MAAM,CAAC,YAAY,KAAK,IAAI,EAAE;AAC9C,gBAAgB,OAAO,UAAU,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC;AAC1D,aAAa;AACb,YAAY,OAAO,IAAI;AACvB;AACA,QAAQ,KAAK,eAAe;AAC5B,YAAY,IAAI,MAAM,CAAC,MAAM,KAAK,IAAI,EAAE;AACxC,gBAAgB,OAAO,UAAU,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC;AAC1D,aAAa;AACb,YAAY,OAAO,IAAI;AACvB;AACA,QAAQ;AACR,YAAY,OAAO,IAAI;AACvB,KAAK;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,eAAe;AAC/B,IAAI,WAAW;AACf,IAAI,gBAAgB;AACpB,IAAI,kBAAkB;AACtB,EAAE;AACF;AACA,IAAI,IAAI,KAAK;AACb;AACA,QAAQ,IAAI;AACZ;AACA,QAAQ,UAAU;AAClB,QAAQ,cAAc;AACtB,QAAQ,gBAAe;AACvB,IAAI,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;AACzC,QAAQ,KAAK,GAAG,WAAW,GAAG,EAAC;AAC/B,QAAQ,IAAI,4BAA4B,gBAAgB,EAAC;AACzD,QAAQ,UAAU,8BAA8B,kBAAkB,EAAC;AACnE,QAAQ,IAAI,EAAE,KAAK,IAAI,CAAC,CAAC,EAAE;AAC3B,YAAY,MAAM,IAAI,SAAS,CAAC,uCAAuC,CAAC;AACxE,SAAS;AACT,KAAK,MAAM;AACX,QAAQ,KAAK,GAAG,EAAC;AACjB,QAAQ,IAAI,4BAA4B,WAAW,EAAC;AACpD,QAAQ,UAAU,8BAA8B,gBAAgB,EAAC;AACjE,KAAK;AACL;AACA,IAAI;AACJ,QAAQ,IAAI,IAAI,IAAI;AACpB;AACA,QAAQ,IAAI,CAAC,MAAM,IAAI,IAAI;AAC3B;AACA,SAAS,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,aAAa,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC;AAC1E,MAAM;AACN,QAAQ,OAAO,KAAK;AACpB,KAAK;AACL;AACA,IAAI,cAAc,GAAG,eAAe,GAAG,KAAI;AAC3C,IAAI,GAAG;AACP,QAAQ,cAAc,GAAG,UAAU,CAAC,cAAc,CAAC,cAAc,EAAC;AAClE,QAAQ,eAAe,GAAG,UAAU,CAAC,aAAa,CAAC,eAAe,EAAC;AACnE,KAAK;AACL,QAAQ,cAAc,IAAI,IAAI;AAC9B,QAAQ,eAAe,IAAI,IAAI;AAC/B,QAAQ,mBAAmB,CAAC,cAAc,CAAC;AAC3C,QAAQ,mBAAmB,CAAC,eAAe,CAAC;AAC5C;AACA,QAAQ,cAAc,KAAK,oBAAoB,CAAC,IAAI,EAAE,UAAU,CAAC;AACjE,QAAQ,EAAE,KAAK,GAAG,CAAC;AACnB,KAAK;AACL;AACA,IAAI,OAAO,KAAK,KAAK,CAAC;AACtB;;ACzIA;AACA;AACA;AACA;AACA;AACA,MAAM,WAAW,GAAG,6BAA4B;AAChD;AACA;AACA,MAAM,QAAQ,GAAG,IAAI,OAAO,GAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,SAAS,CAAC,GAAG,EAAE,KAAK,EAAE;AAC/B,IAAI,IAAI,OAAO,GAAG,MAAK;AACvB,IAAI,KAAK,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE,EAAE,CAAC,EAAE;AACvE,QAAQ,OAAO,GAAG,CAAC,QAAO;AAC1B,KAAK;AACL,IAAI,OAAO,OAAO;AAClB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,WAAW,EAAE;AAC7C,IAAI,MAAM,MAAM,GAAG,GAAE;AACrB,IAAI,IAAI,KAAK,GAAG,EAAC;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,SAAS,QAAQ,CAAC,GAAG,EAAE,KAAK,EAAE;AAClC,QAAQ,QAAQ,GAAG;AACnB,YAAY,KAAK,IAAI;AACrB,gBAAgB,OAAO,GAAG;AAC1B,YAAY,KAAK,IAAI;AACrB,gBAAgB,OAAO,KAAK,CAAC,CAAC,CAAC;AAC/B,YAAY,KAAK,IAAI;AACrB,gBAAgB,OAAO,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC;AAChD,YAAY,KAAK,IAAI;AACrB,gBAAgB,OAAO,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;AAC/D,YAAY,SAAS;AACrB,gBAAgB,MAAM,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,EAAC;AACtC,gBAAgB,IAAI,CAAC,IAAI,KAAK,EAAE;AAChC,oBAAoB,OAAO,KAAK,qBAAqB,CAAC,EAAE;AACxD,iBAAiB;AACjB,gBAAgB,OAAO,GAAG;AAC1B,aAAa;AACb,SAAS;AACT,KAAK;AACL;AACA,IAAI,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;AAC9C,QAAQ,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,EAAC;AAClD,QAAQ,MAAM,CAAC,IAAI;AACnB,YAAY,WAAW,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,GAAG,KAAK,QAAQ,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AAC3E,UAAS;AACT,QAAQ,KAAK,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,OAAM;AAC7C,KAAK;AACL,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,EAAC;AACjC;AACA,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;AAC1B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE;AACzC,IAAI,MAAM,MAAM,GAAG,GAAE;AACrB,IAAI,IAAI,KAAK,GAAG,EAAC;AACjB;AACA,IAAI,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;AAC9C,QAAQ,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,EAAC;AAClD,QAAQ,MAAM,CAAC,IAAI;AACnB,YAAY,MAAM;AAClB,gBAAgB,OAAO;AACvB,oBAAoB;AACpB,iDAAiD,KAAK;AACtD,qBAAqB;AACrB,oBAAoB,KAAK,CAAC,KAAK;AAC/B,oBAAoB,KAAK,CAAC,KAAK;AAC/B,iBAAiB;AACjB,aAAa;AACb,UAAS;AACT,QAAQ,KAAK,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,OAAM;AAC7C,KAAK;AACL,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,EAAC;AACjC;AACA,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;AAC1B,CAAC;AACD;AACA;AACA;AACA;AACO,MAAM,cAAc,CAAC;AAC5B;AACA;AACA;AACA;AACA;AACA,IAAI,WAAW,CAAC,OAAO,EAAE,OAAO,GAAG,EAAE,EAAE;AACvC,QAAQ,MAAM,EAAE,OAAO,GAAG,KAAK,EAAE,GAAG,QAAO;AAC3C,QAAQ,IAAI,EAAE,OAAO,YAAY,MAAM,CAAC,EAAE;AAC1C,YAAY,MAAM,IAAI,SAAS,CAAC,wCAAwC,CAAC;AACzE,SAAS;AACT,QAAQ,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AAC1C,YAAY,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC;AAClE,SAAS;AACT;AACA,QAAQ,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE;AAC3B,YAAY,OAAO,EAAE,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,KAAK,CAAC;AAC9D,YAAY,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC;AACrC,SAAS,EAAC;AACV,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;AAClB,QAAQ,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE;AAClC,6DAA6D,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,EAAC;AAChF,QAAQ,IAAI,KAAK,GAAG,KAAI;AACxB,QAAQ,IAAI,SAAS,GAAG,EAAC;AACzB;AACA,QAAQ,OAAO,CAAC,SAAS,GAAG,EAAC;AAC7B,QAAQ,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE;AACpD,YAAY,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK,CAAC,EAAE;AACzD,gBAAgB,SAAS,GAAG,OAAO,CAAC,UAAS;AAC7C,gBAAgB,MAAM,MAAK;AAC3B,gBAAgB,OAAO,CAAC,SAAS,GAAG,UAAS;AAC7C,aAAa;AACb,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,GAAG,EAAE;AACd,QAAQ,MAAM,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,EAAC;AACpC,QAAQ,MAAM,GAAG,GAAG,EAAE,CAAC,IAAI,GAAE;AAC7B,QAAQ,OAAO,CAAC,GAAG,CAAC,IAAI;AACxB,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,QAAQ,EAAE;AACpC,QAAQ,OAAO,OAAO,QAAQ,KAAK,UAAU;AAC7C,cAAc,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,EAAE,QAAQ,CAAC;AACnD,cAAc,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;AAC3D,KAAK;AACL;;ACvKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,WAAW,GAAG,uDAAsD;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,WAAW,CAAC,IAAI,EAAE;AAC3B,IAAI;AACJ,QAAQ,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;AACnC,qFAAqF;AACrF,YAAY,IAAI;AAChB,UAAU,MAAM,IAAI,IAAI;AACxB,KAAK;AACL,CAAC;AACD,MAAM,GAAG;AACT;AACA,QAAQ,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;AACjD,MAAK;AACL;AACY,MAAC,IAAI,GAAG,MAAM,CAAC,MAAM,EAAC;AACtB,MAAC,IAAI,GAAG,MAAM,CAAC,MAAM,EAAC;AACtB,MAAC,SAAS,GAAG,MAAM,CAAC,WAAW,EAAC;AAChC,MAAC,GAAG,GAAG,MAAM,CAAC,KAAK,EAAC;AAChC;AACA,MAAM,WAAW,GAAG,EAAE,OAAO,EAAE,EAAE,CAAC,IAAI,GAAG,IAAI,EAAE,GAAE;AACjD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,gBAAgB,CAAC,QAAQ,EAAE;AACpC,IAAI;AACJ,QAAQ,QAAQ,IAAI,IAAI;AACxB,QAAQ,QAAQ,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC;AAClC,QAAQ,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,CAAC;AACpD,KAAK;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,aAAa,CAAC,IAAI,EAAE;AAC7B,IAAI,MAAM,MAAM,+BAA+B,CAAC,IAAI,EAAE,OAAM;AAC5D;AACA,IAAI,IAAI,MAAM,EAAE;AAChB,QAAQ,QAAQ,MAAM,CAAC,IAAI;AAC3B,YAAY,KAAK,uBAAuB;AACxC,gBAAgB,OAAO,MAAM,CAAC,UAAU,KAAK,IAAI,IAAI,MAAM,CAAC,SAAS,KAAK,IAAI;AAC9E,YAAY,KAAK,mBAAmB;AACpC,gBAAgB,OAAO,IAAI;AAC3B,YAAY,KAAK,oBAAoB;AACrC,gBAAgB;AAChB,oBAAoB,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI;AAC9E,iBAAiB;AACjB,YAAY,KAAK,iBAAiB;AAClC,gBAAgB,OAAO,IAAI;AAC3B,YAAY,KAAK,gBAAgB,CAAC;AAClC,YAAY,KAAK,uBAAuB,CAAC;AACzC,YAAY,KAAK,iBAAiB,CAAC;AACnC,YAAY,KAAK,qBAAqB,CAAC;AACvC,YAAY,KAAK,2BAA2B;AAC5C,gBAAgB,OAAO,IAAI;AAC3B;AACA,YAAY;AACZ,gBAAgB,OAAO,KAAK;AAC5B,SAAS;AACT,KAAK;AACL,IAAI,OAAO,KAAK;AAChB,CAAC;AACD;AACA;AACA;AACA;AACO,MAAM,gBAAgB,CAAC;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,WAAW,CAAC,WAAW,EAAE,OAAO,GAAG,EAAE,EAAE;AAC3C,QAAQ,MAAM;AACd,YAAY,IAAI,GAAG,QAAQ;AAC3B,YAAY,iBAAiB,GAAG,CAAC,QAAQ,EAAE,YAAY,EAAE,MAAM,EAAE,QAAQ,CAAC;AAC1E,SAAS,GAAG,QAAO;AACnB;AACA,QAAQ,IAAI,CAAC,aAAa,GAAG,GAAE;AAC/B;AACA,QAAQ,IAAI,CAAC,WAAW,GAAG,YAAW;AACtC;AACA,QAAQ,IAAI,CAAC,IAAI,GAAG,KAAI;AACxB;AACA,QAAQ,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC,EAAC;AAC3D,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE;AACvC,QAAQ,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;AACjD,YAAY,MAAM,YAAY,GAAG,QAAQ,CAAC,GAAG,EAAC;AAC9C,YAAY,MAAM,IAAI,GAAG,CAAC,GAAG,EAAC;AAC9B,YAAY,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAC;AAC1D;AACA,YAAY,IAAI,gBAAgB,CAAC,QAAQ,CAAC,EAAE;AAC5C,gBAAgB,QAAQ;AACxB,aAAa;AACb;AACA,YAAY,OAAO,IAAI,CAAC,0BAA0B;AAClD,yCAAyC,QAAQ;AACjD,gBAAgB,IAAI;AACpB,gBAAgB,YAAY;AAC5B,gBAAgB,IAAI;AACpB,cAAa;AACb,SAAS;AACT;AACA,QAAQ,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,iBAAiB,EAAE;AAClD;AACA,YAAY,MAAM,IAAI,GAAG,GAAE;AAC3B,YAAY,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAC;AAC1D;AACA,YAAY,IAAI,gBAAgB,CAAC,QAAQ,CAAC,EAAE;AAC5C,gBAAgB,QAAQ;AACxB,aAAa;AACb;AACA,YAAY,OAAO,IAAI,CAAC,0BAA0B;AAClD,yCAAyC,QAAQ;AACjD,gBAAgB,IAAI;AACpB,gBAAgB,QAAQ;AACxB,gBAAgB,KAAK;AACrB,cAAa;AACb,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE;AACpC,QAAQ,KAAK,MAAM,EAAE,IAAI,EAAE,IAAI,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,EAAE;AAC1E,YAAY,MAAM,GAAG,GAAG,mBAAmB;AAC3C,8CAA8C,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;AACjE,cAAa;AACb,YAAY,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE;AACpD,gBAAgB,QAAQ;AACxB,aAAa;AACb;AACA,YAAY,MAAM,YAAY,GAAG,QAAQ,CAAC,GAAG,EAAC;AAC9C,YAAY,MAAM,IAAI,GAAG,CAAC,GAAG,EAAC;AAC9B;AACA,YAAY,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE;AACpC,gBAAgB,MAAM;AACtB,oBAAoB,IAAI;AACxB,oBAAoB,IAAI;AACxB,oBAAoB,IAAI,EAAE,IAAI;AAC9B,oBAAoB,IAAI,EAAE,YAAY,CAAC,IAAI,CAAC;AAC5C,kBAAiB;AACjB,aAAa;AACb,YAAY,OAAO,IAAI,CAAC,0BAA0B;AAClD,+CAA+C,IAAI;AACnD,gBAAgB,IAAI;AACpB,gBAAgB,YAAY;AAC5B,cAAa;AACb,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE;AACpC,QAAQ,MAAM,WAAW,2BAA2B,IAAI,CAAC,WAAW,CAAC,KAAK,EAAC;AAC3E;AACA,QAAQ,KAAK,MAAM,IAAI,IAAI,WAAW,CAAC,IAAI,EAAE;AAC7C,YAAY,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;AACpC,gBAAgB,QAAQ;AACxB,aAAa;AACb,YAAY,MAAM,QAAQ,0BAA0B,IAAI,CAAC,MAAM,CAAC,KAAK,EAAC;AACtE;AACA,YAAY,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE;AAC1C,gBAAgB,QAAQ;AACxB,aAAa;AACb,YAAY,MAAM,YAAY,GAAG,QAAQ,CAAC,QAAQ,EAAC;AACnD,YAAY,MAAM,IAAI,GAAG,CAAC,QAAQ,EAAC;AACnC;AACA,YAAY,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE;AACpC,gBAAgB,MAAM;AACtB;AACA,oBAAoB,IAAI,2BAA2B,IAAI,CAAC;AACxD,oBAAoB,IAAI;AACxB,oBAAoB,IAAI,EAAE,IAAI;AAC9B,oBAAoB,IAAI,EAAE,YAAY,CAAC,IAAI,CAAC;AAC5C,kBAAiB;AACjB,aAAa;AACb;AACA,YAAY,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAsB,EAAE;AACtD,gBAAgB,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE;AAC7D,oBAAoB,MAAM,cAAc,GAAG,YAAY,CAAC,GAAG,EAAC;AAC5D,oBAAoB,IAAI,cAAc,CAAC,IAAI,CAAC,EAAE;AAC9C,wBAAwB,MAAM;AAC9B;AACA,4BAA4B,IAAI,2BAA2B,IAAI,CAAC;AAChE,4BAA4B,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;AAClD,4BAA4B,IAAI,EAAE,IAAI;AACtC,4BAA4B,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC;AACtD,0BAAyB;AACzB,qBAAqB;AACrB,iBAAiB;AACjB,aAAa,MAAM;AACnB,gBAAgB,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE;AACzD,oBAAoB,MAAM,GAAG,GAAG,GAAG,CAAC,YAAY,EAAE,GAAG,EAAC;AACtD,oBAAoB,MAAM,EAAE,GAAG,IAAI,CAAC,wBAAwB;AAC5D,wBAAwB,SAAS;AACjC,wBAAwB,IAAI;AAC5B,wBAAwB,GAAG;AAC3B,8BAA8B,YAAY;AAC1C,8BAA8B,IAAI,CAAC,IAAI,KAAK,QAAQ;AACpD,8BAA8B,EAAE,OAAO,EAAE,YAAY,EAAE,GAAG,YAAY,EAAE;AACxE,8BAA8B,EAAE,OAAO,EAAE,YAAY,EAAE;AACvD,sBAAqB;AACrB;AACA,oBAAoB,IAAI,GAAG,EAAE;AAC7B,wBAAwB,OAAO,GAAE;AACjC,qBAAqB,MAAM;AAC3B,wBAAwB,KAAK,MAAM,MAAM,IAAI,EAAE,EAAE;AACjD,4BAA4B,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,EAAC;AAC3E,4BAA4B;AAC5B,gCAAgC,MAAM,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC;AACvD,gCAAgC,MAAM,CAAC,IAAI,KAAK,IAAI;AACpD,8BAA8B;AAC9B,gCAAgC,MAAM,OAAM;AAC5C,6BAA6B;AAC7B,yBAAyB;AACzB,qBAAqB;AACrB,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC,yBAAyB,CAAC,IAAI,EAAE,QAAQ,EAAE;AAC/C,QAAQ,OAAO,IAAI,CAAC,0BAA0B,CAAC,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAC;AAClE,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC,0BAA0B,CAAC,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,YAAY,EAAE;AACxE,QAAQ,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;AACnD,YAAY,MAAM;AAClB,SAAS;AACT,QAAQ,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAC;AACzC,QAAQ,IAAI;AACZ,YAAY,KAAK,MAAM,SAAS,IAAI,QAAQ,CAAC,UAAU,EAAE;AACzD,gBAAgB,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE;AACzC,oBAAoB,QAAQ;AAC5B,iBAAiB;AACjB,gBAAgB,MAAM,IAAI;AAC1B,oBAAoB,SAAS,CAAC,UAAU;AACxC,kBAAiB;AACjB;AACA,gBAAgB,IAAI,YAAY,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE;AACpD,oBAAoB,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,GAAE;AAC1E,iBAAiB;AACjB,gBAAgB,OAAO,IAAI,CAAC,0BAA0B,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAC;AAC5E,aAAa;AACb,SAAS,SAAS;AAClB,YAAY,IAAI,CAAC,aAAa,CAAC,GAAG,GAAE;AACpC,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC,0BAA0B,CAAC,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE;AAC1D,QAAQ,IAAI,IAAI,GAAG,SAAQ;AAC3B,QAAQ,OAAO,aAAa,CAAC,IAAI,CAAC,EAAE;AACpC,YAAY,IAAI,GAAG,IAAI,CAAC,OAAM;AAC9B,SAAS;AACT;AACA,QAAQ,MAAM,MAAM,2BAA2B,CAAC,IAAI,EAAE,OAAM;AAC5D,QAAQ,IAAI,MAAM,CAAC,IAAI,KAAK,kBAAkB,EAAE;AAChD,YAAY,IAAI,MAAM,CAAC,MAAM,KAAK,IAAI,EAAE;AACxC,gBAAgB,MAAM,GAAG,GAAG,eAAe,CAAC,MAAM,EAAC;AACnD,gBAAgB,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE;AACxD,oBAAoB,MAAM;AAC1B,iBAAiB;AACjB;AACA,gBAAgB,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,EAAC;AACvC,gBAAgB,MAAM,YAAY,GAAG,QAAQ,CAAC,GAAG,EAAC;AAClD,gBAAgB,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE;AACxC,oBAAoB,MAAM;AAC1B,wBAAwB,IAAI,EAAE,MAAM;AACpC,wBAAwB,IAAI;AAC5B,wBAAwB,IAAI,EAAE,IAAI;AAClC,wBAAwB,IAAI,EAAE,YAAY,CAAC,IAAI,CAAC;AAChD,sBAAqB;AACrB,iBAAiB;AACjB,gBAAgB,OAAO,IAAI,CAAC,0BAA0B;AACtD,oBAAoB,MAAM;AAC1B,oBAAoB,IAAI;AACxB,oBAAoB,YAAY;AAChC,kBAAiB;AACjB,aAAa;AACb,YAAY,MAAM;AAClB,SAAS;AACT,QAAQ,IAAI,MAAM,CAAC,IAAI,KAAK,gBAAgB,EAAE;AAC9C,YAAY,IAAI,MAAM,CAAC,MAAM,KAAK,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE;AAC1D,gBAAgB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,GAAE;AAC9E,aAAa;AACb,YAAY,MAAM;AAClB,SAAS;AACT,QAAQ,IAAI,MAAM,CAAC,IAAI,KAAK,eAAe,EAAE;AAC7C,YAAY,IAAI,MAAM,CAAC,MAAM,KAAK,IAAI,IAAI,QAAQ,CAAC,SAAS,CAAC,EAAE;AAC/D,gBAAgB,MAAM;AACtB,oBAAoB,IAAI,EAAE,MAAM;AAChC,oBAAoB,IAAI;AACxB,oBAAoB,IAAI,EAAE,SAAS;AACnC,oBAAoB,IAAI,EAAE,QAAQ,CAAC,SAAS,CAAC;AAC7C,kBAAiB;AACjB,aAAa;AACb,YAAY,MAAM;AAClB,SAAS;AACT,QAAQ,IAAI,MAAM,CAAC,IAAI,KAAK,sBAAsB,EAAE;AACpD,YAAY,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,EAAE;AACvC,gBAAgB,OAAO,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAC;AAC9E,gBAAgB,OAAO,IAAI,CAAC,0BAA0B,CAAC,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAC;AAC9E,aAAa;AACb,YAAY,MAAM;AAClB,SAAS;AACT,QAAQ,IAAI,MAAM,CAAC,IAAI,KAAK,mBAAmB,EAAE;AACjD,YAAY,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,EAAE;AACvC,gBAAgB,OAAO,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAC;AAC9E,aAAa;AACb,YAAY,MAAM;AAClB,SAAS;AACT,QAAQ,IAAI,MAAM,CAAC,IAAI,KAAK,oBAAoB,EAAE;AAClD,YAAY,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,EAAE;AACtC,gBAAgB,OAAO,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAC;AAC5E,aAAa;AACb,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,IAAI,EAAE,QAAQ,EAAE;AACxD,QAAQ,IAAI,WAAW,CAAC,IAAI,KAAK,YAAY,EAAE;AAC/C,YAAY,MAAM,QAAQ,GAAG,YAAY,CAAC,IAAI,CAAC,WAAW,EAAE,WAAW,EAAC;AACxE,YAAY,IAAI,QAAQ,IAAI,IAAI,EAAE;AAClC,gBAAgB,OAAO,IAAI,CAAC,0BAA0B;AACtD,oBAAoB,QAAQ;AAC5B,oBAAoB,IAAI;AACxB,oBAAoB,QAAQ;AAC5B,oBAAoB,KAAK;AACzB,kBAAiB;AACjB,aAAa;AACb,YAAY,MAAM;AAClB,SAAS;AACT,QAAQ,IAAI,WAAW,CAAC,IAAI,KAAK,eAAe,EAAE;AAClD,YAAY,KAAK,MAAM,QAAQ,IAAI,WAAW,CAAC,UAAU,EAAE;AAC3D,gBAAgB,MAAM,GAAG,GAAG,eAAe;AAC3C,uDAAuD,QAAQ;AAC/D,kBAAiB;AACjB;AACA,gBAAgB,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE;AACxD,oBAAoB,QAAQ;AAC5B,iBAAiB;AACjB;AACA,gBAAgB,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,EAAC;AACjD,gBAAgB,MAAM,YAAY,GAAG,QAAQ,CAAC,GAAG,EAAC;AAClD,gBAAgB,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE;AACxC,oBAAoB,MAAM;AAC1B,wBAAwB,IAAI,2BAA2B,QAAQ,CAAC;AAChE,wBAAwB,IAAI,EAAE,QAAQ;AACtC,wBAAwB,IAAI,EAAE,IAAI;AAClC,wBAAwB,IAAI,EAAE,YAAY,CAAC,IAAI,CAAC;AAChD,sBAAqB;AACrB,iBAAiB;AACjB,gBAAgB,OAAO,IAAI,CAAC,qBAAqB;AACjD,sDAAsD,CAAC,QAAQ,EAAE,KAAK;AACtE,oBAAoB,QAAQ;AAC5B,oBAAoB,YAAY;AAChC,kBAAiB;AACjB,aAAa;AACb,YAAY,MAAM;AAClB,SAAS;AACT,QAAQ,IAAI,WAAW,CAAC,IAAI,KAAK,mBAAmB,EAAE;AACtD,YAAY,OAAO,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAC;AAC/E,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC,wBAAwB,CAAC,aAAa,EAAE,IAAI,EAAE,QAAQ,EAAE;AAC7D,QAAQ,MAAM,IAAI,GAAG,aAAa,CAAC,KAAI;AACvC;AACA,QAAQ,IAAI,IAAI,KAAK,iBAAiB,IAAI,IAAI,KAAK,wBAAwB,EAAE;AAC7E,YAAY,MAAM,GAAG;AACrB,gBAAgB,IAAI,KAAK,wBAAwB;AACjD,sBAAsB,SAAS;AAC/B,sBAAsB,aAAa,CAAC,QAAQ,CAAC,IAAI,KAAK,YAAY;AAClE,sBAAsB,aAAa,CAAC,QAAQ,CAAC,IAAI;AACjD,sBAAsB,aAAa,CAAC,QAAQ,CAAC,MAAK;AAClD,YAAY,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE;AACrC,gBAAgB,MAAM;AACtB,aAAa;AACb;AACA,YAAY,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,EAAC;AACnC,YAAY,MAAM,YAAY,GAAG,QAAQ,CAAC,GAAG,EAAC;AAC9C,YAAY,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE;AACpC,gBAAgB,MAAM;AACtB,oBAAoB,IAAI,2BAA2B,aAAa,CAAC;AACjE,oBAAoB,IAAI;AACxB,oBAAoB,IAAI,EAAE,IAAI;AAC9B,oBAAoB,IAAI,EAAE,YAAY,CAAC,IAAI,CAAC;AAC5C,kBAAiB;AACjB,aAAa;AACb,YAAY,OAAO,IAAI,CAAC,0BAA0B;AAClD;AACA,oBAAoB,YAAY,CAAC,IAAI,CAAC,WAAW,EAAE,aAAa,CAAC,KAAK,CAAC;AACvE;AACA,gBAAgB,IAAI;AACpB,gBAAgB,YAAY;AAC5B,gBAAgB,KAAK;AACrB,cAAa;AACb;AACA,YAAY,MAAM;AAClB,SAAS;AACT;AACA,QAAQ,IAAI,IAAI,KAAK,0BAA0B,EAAE;AACjD,YAAY,OAAO,IAAI,CAAC,0BAA0B;AAClD;AACA,oBAAoB,YAAY,CAAC,IAAI,CAAC,WAAW,EAAE,aAAa,CAAC,KAAK,CAAC;AACvE;AACA,gBAAgB,IAAI;AACpB,gBAAgB,QAAQ;AACxB,gBAAgB,KAAK;AACrB,cAAa;AACb,YAAY,MAAM;AAClB,SAAS;AACT;AACA,QAAQ,IAAI,IAAI,KAAK,iBAAiB,EAAE;AACxC,YAAY,MAAM,GAAG;AACrB,gBAAgB,aAAa,CAAC,KAAK,CAAC,IAAI,KAAK,YAAY;AACzD,sBAAsB,aAAa,CAAC,KAAK,CAAC,IAAI;AAC9C,sBAAsB,aAAa,CAAC,KAAK,CAAC,MAAK;AAC/C,YAAY,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE;AACrC,gBAAgB,MAAM;AACtB,aAAa;AACb;AACA,YAAY,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,EAAC;AACnC,YAAY,MAAM,YAAY,GAAG,QAAQ,CAAC,GAAG,EAAC;AAC9C,YAAY,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE;AACpC,gBAAgB,MAAM;AACtB,oBAAoB,IAAI,2BAA2B,aAAa,CAAC;AACjE,oBAAoB,IAAI;AACxB,oBAAoB,IAAI,EAAE,IAAI;AAC9B,oBAAoB,IAAI,EAAE,YAAY,CAAC,IAAI,CAAC;AAC5C,kBAAiB;AACjB,aAAa;AACb,SAAS;AACT,KAAK;AACL,CAAC;AACD;AACA,gBAAgB,CAAC,IAAI,GAAG,KAAI;AAC5B,gBAAgB,CAAC,IAAI,GAAG,KAAI;AAC5B,gBAAgB,CAAC,SAAS,GAAG,UAAS;AACtC,gBAAgB,CAAC,GAAG,GAAG,IAAG;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,aAAa,CAAC,IAAI,EAAE,KAAK,EAAE;AACpC,IAAI,OAAO,EAAE,KAAK,KAAK,CAAC,IAAI,IAAI,KAAK,SAAS,CAAC;AAC/C;;ACljBA;AAiEA;AACA,YAAe;AACf,IAAI,IAAI;AACR,IAAI,SAAS;AACb,IAAI,GAAG;AACP,IAAI,YAAY;AAChB,IAAI,uBAAuB;AAC3B,IAAI,uBAAuB;AAC3B,IAAI,iBAAiB;AACrB,IAAI,eAAe;AACnB,IAAI,cAAc;AAClB,IAAI,mBAAmB;AACvB,IAAI,aAAa;AACjB,IAAI,YAAY;AAChB,IAAI,mBAAmB;AACvB,IAAI,qBAAqB;AACzB,IAAI,mBAAmB;AACvB,IAAI,YAAY;AAChB,IAAI,YAAY;AAChB,IAAI,cAAc;AAClB,IAAI,eAAe;AACnB,IAAI,sBAAsB;AAC1B,IAAI,wBAAwB;AAC5B,IAAI,sBAAsB;AAC1B,IAAI,eAAe;AACnB,IAAI,eAAe;AACnB,IAAI,iBAAiB;AACrB,IAAI,sBAAsB;AAC1B,IAAI,wBAAwB;AAC5B,IAAI,sBAAsB;AAC1B,IAAI,mBAAmB;AACvB,IAAI,mBAAmB;AACvB,IAAI,qBAAqB;AACzB,IAAI,mBAAmB;AACvB,IAAI,eAAe;AACnB,IAAI,gBAAgB;AACpB,IAAI,cAAc;AAClB,IAAI,IAAI;AACR,IAAI,gBAAgB;AACpB;;;;"}