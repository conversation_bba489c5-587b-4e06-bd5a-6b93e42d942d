"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, "default"), secondTarget && __copyProps(secondTarget, mod, "default"));
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var get_exports = {};
__export(get_exports, {
  getMeta: () => getMeta,
  getServerConfig: () => getServerConfig,
  getTargetDir: () => getTargetDir
});
module.exports = __toCommonJS(get_exports);
var import_path = __toESM(require("path"));
var import_constants = require("../constants");
var import_fs = require("../fs");
__reExport(get_exports, require("./data"), module.exports);
__reExport(get_exports, require("./config"), module.exports);
const getServerConfig = async (appDirectory, configFile) => {
  const configFilePath = (0, import_fs.findExists)(import_constants.CONFIG_FILE_EXTENSIONS.map((extension) => import_path.default.resolve(appDirectory, `${configFile}${extension}`)));
  return configFilePath;
};
const getMeta = (metaName = "modern-js") => {
  const meta = metaName.toLowerCase().split("-")[0];
  return meta;
};
const getTargetDir = (from, baseDir, targetBaseDir) => {
  const relativePath = import_path.default.relative(baseDir, from);
  return import_path.default.resolve(targetBaseDir, relativePath);
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  getMeta,
  getServerConfig,
  getTargetDir,
  ...require("./data"),
  ...require("./config")
});
