(()=>{var i={823:function(i){
/**
 * filesize
 *
 * @copyright 2022 <PERSON> <<EMAIL>>
 * @license BSD-3-Clause
 * @version 8.0.7
 */
(function(e,t){true?i.exports=t():0})(this,(function(){"use strict";var i=/^(b|B)$/,e={iec:{bits:["bit","Kibit","Mibit","Gibit","Tibit","Pibit","Eibit","Zibit","Yibit"],bytes:["B","KiB","MiB","GiB","TiB","PiB","EiB","ZiB","YiB"]},jedec:{bits:["bit","Kbit","Mbit","Gbit","Tbit","Pbit","Ebit","Zbit","Ybit"],bytes:["B","KB","MB","GB","TB","PB","EB","<PERSON><PERSON>","Y<PERSON>"]}},t={iec:["","kibi","mebi","gibi","tebi","pebi","exbi","zebi","yobi"],jedec:["","kilo","mega","giga","tera","peta","exa","zetta","yotta"]},r={floor:Math.floor,ceil:Math.ceil};function filesize(o){var a=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};var n=[],s=0,f,b,l,u,c,p,d,_,B,v,h,g,x,y,M,m,w,k,N,z,E;if(isNaN(o)){throw new TypeError("Invalid number")}l=a.bits===true;M=a.unix===true;g=a.pad===true;b=a.base||10;x=a.round!==void 0?a.round:M?1:2;d=a.locale!==void 0?a.locale:"";_=a.localeOptions||{};m=a.separator!==void 0?a.separator:"";w=a.spacer!==void 0?a.spacer:M?"":" ";N=a.symbols||{};k=b===2?a.standard||"iec":"jedec";h=a.output||"string";c=a.fullform===true;p=a.fullforms instanceof Array?a.fullforms:[];f=a.exponent!==void 0?a.exponent:-1;z=r[a.roundingMethod]||Math.round;v=Number(o);B=v<0;u=b>2?1e3:1024;E=isNaN(a.precision)===false?parseInt(a.precision,10):0;if(B){v=-v}if(f===-1||isNaN(f)){f=Math.floor(Math.log(v)/Math.log(u));if(f<0){f=0}}if(f>8){if(E>0){E+=8-f}f=8}if(h==="exponent"){return f}if(v===0){n[0]=0;y=n[1]=M?"":e[k][l?"bits":"bytes"][f]}else{s=v/(b===2?Math.pow(2,f*10):Math.pow(1e3,f));if(l){s=s*8;if(s>=u&&f<8){s=s/u;f++}}var j=Math.pow(10,f>0?x:0);n[0]=z(s*j)/j;if(n[0]===u&&f<8&&a.exponent===void 0){n[0]=1;f++}y=n[1]=b===10&&f===1?l?"kbit":"kB":e[k][l?"bits":"bytes"][f];if(M){n[1]=n[1].charAt(0);if(i.test(n[1])){n[0]=Math.floor(n[0]);n[1]=""}}}if(B){n[0]=-n[0]}if(E>0){n[0]=n[0].toPrecision(E)}n[1]=N[n[1]]||n[1];if(d===true){n[0]=n[0].toLocaleString()}else if(d.length>0){n[0]=n[0].toLocaleString(d,_)}else if(m.length>0){n[0]=n[0].toString().replace(".",m)}if(g&&Number.isInteger(n[0])===false&&x>0){var q=m||".",P=n[0].toString().split(q),T=P[1]||"",G=T.length,K=x-G;n[0]="".concat(P[0]).concat(q).concat(T.padEnd(G+K,"0"))}if(c){n[1]=p[f]?p[f]:t[k][f]+(l?"bit":"byte")+(n[0]===1?"":"s")}return h==="array"?n:h==="object"?{value:n[0],symbol:n[1],exponent:f,unit:y}:n.join(w)}filesize.partial=function(i){return function(e){return filesize(e,i)}};return filesize}))}};var e={};function __nccwpck_require__(t){var r=e[t];if(r!==undefined){return r.exports}var o=e[t]={exports:{}};var a=true;try{i[t].call(o.exports,o,o.exports,__nccwpck_require__);a=false}finally{if(a)delete e[t]}return o.exports}if(typeof __nccwpck_require__!=="undefined")__nccwpck_require__.ab=__dirname+"/";var t=__nccwpck_require__(823);module.exports=t})();