(()=>{var e={695:(e,t,r)=>{"use strict";const{Buffer:i}=r(300);const n=Symbol.for("BufferList");function BufferList(e){if(!(this instanceof BufferList)){return new BufferList(e)}BufferList._init.call(this,e)}BufferList._init=function _init(e){Object.defineProperty(this,n,{value:true});this._bufs=[];this.length=0;if(e){this.append(e)}};BufferList.prototype._new=function _new(e){return new BufferList(e)};BufferList.prototype._offset=function _offset(e){if(e===0){return[0,0]}let t=0;for(let r=0;r<this._bufs.length;r++){const i=t+this._bufs[r].length;if(e<i||r===this._bufs.length-1){return[r,e-t]}t=i}};BufferList.prototype._reverseOffset=function(e){const t=e[0];let r=e[1];for(let e=0;e<t;e++){r+=this._bufs[e].length}return r};BufferList.prototype.get=function get(e){if(e>this.length||e<0){return undefined}const t=this._offset(e);return this._bufs[t[0]][t[1]]};BufferList.prototype.slice=function slice(e,t){if(typeof e==="number"&&e<0){e+=this.length}if(typeof t==="number"&&t<0){t+=this.length}return this.copy(null,0,e,t)};BufferList.prototype.copy=function copy(e,t,r,n){if(typeof r!=="number"||r<0){r=0}if(typeof n!=="number"||n>this.length){n=this.length}if(r>=this.length){return e||i.alloc(0)}if(n<=0){return e||i.alloc(0)}const copy=!!e;const s=this._offset(r);const a=n-r;let o=a;let f=copy&&t||0;let l=s[1];if(r===0&&n===this.length){if(!copy){return this._bufs.length===1?this._bufs[0]:i.concat(this._bufs,this.length)}for(let t=0;t<this._bufs.length;t++){this._bufs[t].copy(e,f);f+=this._bufs[t].length}return e}if(o<=this._bufs[s[0]].length-l){return copy?this._bufs[s[0]].copy(e,t,l,l+o):this._bufs[s[0]].slice(l,l+o)}if(!copy){e=i.allocUnsafe(a)}for(let t=s[0];t<this._bufs.length;t++){const r=this._bufs[t].length-l;if(o>r){this._bufs[t].copy(e,f,l);f+=r}else{this._bufs[t].copy(e,f,l,l+o);f+=r;break}o-=r;if(l){l=0}}if(e.length>f)return e.slice(0,f);return e};BufferList.prototype.shallowSlice=function shallowSlice(e,t){e=e||0;t=typeof t!=="number"?this.length:t;if(e<0){e+=this.length}if(t<0){t+=this.length}if(e===t){return this._new()}const r=this._offset(e);const i=this._offset(t);const n=this._bufs.slice(r[0],i[0]+1);if(i[1]===0){n.pop()}else{n[n.length-1]=n[n.length-1].slice(0,i[1])}if(r[1]!==0){n[0]=n[0].slice(r[1])}return this._new(n)};BufferList.prototype.toString=function toString(e,t,r){return this.slice(t,r).toString(e)};BufferList.prototype.consume=function consume(e){e=Math.trunc(e);if(Number.isNaN(e)||e<=0)return this;while(this._bufs.length){if(e>=this._bufs[0].length){e-=this._bufs[0].length;this.length-=this._bufs[0].length;this._bufs.shift()}else{this._bufs[0]=this._bufs[0].slice(e);this.length-=e;break}}return this};BufferList.prototype.duplicate=function duplicate(){const e=this._new();for(let t=0;t<this._bufs.length;t++){e.append(this._bufs[t])}return e};BufferList.prototype.append=function append(e){if(e==null){return this}if(e.buffer){this._appendBuffer(i.from(e.buffer,e.byteOffset,e.byteLength))}else if(Array.isArray(e)){for(let t=0;t<e.length;t++){this.append(e[t])}}else if(this._isBufferList(e)){for(let t=0;t<e._bufs.length;t++){this.append(e._bufs[t])}}else{if(typeof e==="number"){e=e.toString()}this._appendBuffer(i.from(e))}return this};BufferList.prototype._appendBuffer=function appendBuffer(e){this._bufs.push(e);this.length+=e.length};BufferList.prototype.indexOf=function(e,t,r){if(r===undefined&&typeof t==="string"){r=t;t=undefined}if(typeof e==="function"||Array.isArray(e)){throw new TypeError('The "value" argument must be one of type string, Buffer, BufferList, or Uint8Array.')}else if(typeof e==="number"){e=i.from([e])}else if(typeof e==="string"){e=i.from(e,r)}else if(this._isBufferList(e)){e=e.slice()}else if(Array.isArray(e.buffer)){e=i.from(e.buffer,e.byteOffset,e.byteLength)}else if(!i.isBuffer(e)){e=i.from(e)}t=Number(t||0);if(isNaN(t)){t=0}if(t<0){t=this.length+t}if(t<0){t=0}if(e.length===0){return t>this.length?this.length:t}const n=this._offset(t);let s=n[0];let a=n[1];for(;s<this._bufs.length;s++){const t=this._bufs[s];while(a<t.length){const r=t.length-a;if(r>=e.length){const r=t.indexOf(e,a);if(r!==-1){return this._reverseOffset([s,r])}a=t.length-e.length+1}else{const t=this._reverseOffset([s,a]);if(this._match(t,e)){return t}a++}}a=0}return-1};BufferList.prototype._match=function(e,t){if(this.length-e<t.length){return false}for(let r=0;r<t.length;r++){if(this.get(e+r)!==t[r]){return false}}return true};(function(){const e={readDoubleBE:8,readDoubleLE:8,readFloatBE:4,readFloatLE:4,readInt32BE:4,readInt32LE:4,readUInt32BE:4,readUInt32LE:4,readInt16BE:2,readInt16LE:2,readUInt16BE:2,readUInt16LE:2,readInt8:1,readUInt8:1,readIntBE:null,readIntLE:null,readUIntBE:null,readUIntLE:null};for(const t in e){(function(t){if(e[t]===null){BufferList.prototype[t]=function(e,r){return this.slice(e,e+r)[t](0,r)}}else{BufferList.prototype[t]=function(r=0){return this.slice(r,r+e[t])[t](0)}}})(t)}})();BufferList.prototype._isBufferList=function _isBufferList(e){return e instanceof BufferList||BufferList.isBufferList(e)};BufferList.isBufferList=function isBufferList(e){return e!=null&&e[n]};e.exports=BufferList},49:(e,t,r)=>{"use strict";const i=r(505).Duplex;const n=r(315);const s=r(695);function BufferListStream(e){if(!(this instanceof BufferListStream)){return new BufferListStream(e)}if(typeof e==="function"){this._callback=e;const t=function piper(e){if(this._callback){this._callback(e);this._callback=null}}.bind(this);this.on("pipe",(function onPipe(e){e.on("error",t)}));this.on("unpipe",(function onUnpipe(e){e.removeListener("error",t)}));e=null}s._init.call(this,e);i.call(this)}n(BufferListStream,i);Object.assign(BufferListStream.prototype,s.prototype);BufferListStream.prototype._new=function _new(e){return new BufferListStream(e)};BufferListStream.prototype._write=function _write(e,t,r){this._appendBuffer(e);if(typeof r==="function"){r()}};BufferListStream.prototype._read=function _read(e){if(!this.length){return this.push(null)}e=Math.min(e,this.length);this.push(this.slice(0,e));this.consume(e)};BufferListStream.prototype.end=function end(e){i.prototype.end.call(this,e);if(this._callback){this._callback(null,this.slice());this._callback=null}};BufferListStream.prototype._destroy=function _destroy(e,t){this._bufs.length=0;this.length=0;t(e)};BufferListStream.prototype._isBufferList=function _isBufferList(e){return e instanceof BufferListStream||e instanceof s||BufferListStream.isBufferList(e)};BufferListStream.isBufferList=s.isBufferList;e.exports=BufferListStream;e.exports.BufferListStream=BufferListStream;e.exports.BufferList=s},805:(e,t,r)=>{"use strict";const i=r(598);let n=false;t.show=(e=process.stderr)=>{if(!e.isTTY){return}n=false;e.write("[?25h")};t.hide=(e=process.stderr)=>{if(!e.isTTY){return}i();n=true;e.write("[?25l")};t.toggle=(e,r)=>{if(e!==undefined){n=e}if(n){t.show(r)}else{t.hide(r)}}},414:(e,t,r)=>{"use strict";const i=Object.assign({},r(32));const n=Object.keys(i);Object.defineProperty(i,"random",{get(){const e=Math.floor(Math.random()*n.length);const t=n[e];return i[t]}});e.exports=i},332:e=>{var t=function(){"use strict";function clone(e,t,r,i){var n;if(typeof t==="object"){r=t.depth;i=t.prototype;n=t.filter;t=t.circular}var s=[];var a=[];var o=typeof Buffer!="undefined";if(typeof t=="undefined")t=true;if(typeof r=="undefined")r=Infinity;function _clone(e,r){if(e===null)return null;if(r==0)return e;var n;var f;if(typeof e!="object"){return e}if(clone.__isArray(e)){n=[]}else if(clone.__isRegExp(e)){n=new RegExp(e.source,__getRegExpFlags(e));if(e.lastIndex)n.lastIndex=e.lastIndex}else if(clone.__isDate(e)){n=new Date(e.getTime())}else if(o&&Buffer.isBuffer(e)){if(Buffer.allocUnsafe){n=Buffer.allocUnsafe(e.length)}else{n=new Buffer(e.length)}e.copy(n);return n}else{if(typeof i=="undefined"){f=Object.getPrototypeOf(e);n=Object.create(f)}else{n=Object.create(i);f=i}}if(t){var l=s.indexOf(e);if(l!=-1){return a[l]}s.push(e);a.push(n)}for(var u in e){var h;if(f){h=Object.getOwnPropertyDescriptor(f,u)}if(h&&h.set==null){continue}n[u]=_clone(e[u],r-1)}return n}return _clone(e,r)}clone.clonePrototype=function clonePrototype(e){if(e===null)return null;var c=function(){};c.prototype=e;return new c};function __objToStr(e){return Object.prototype.toString.call(e)}clone.__objToStr=__objToStr;function __isDate(e){return typeof e==="object"&&__objToStr(e)==="[object Date]"}clone.__isDate=__isDate;function __isArray(e){return typeof e==="object"&&__objToStr(e)==="[object Array]"}clone.__isArray=__isArray;function __isRegExp(e){return typeof e==="object"&&__objToStr(e)==="[object RegExp]"}clone.__isRegExp=__isRegExp;function __getRegExpFlags(e){var t="";if(e.global)t+="g";if(e.ignoreCase)t+="i";if(e.multiline)t+="m";return t}clone.__getRegExpFlags=__getRegExpFlags;return clone}();if(true&&e.exports){e.exports=t}},906:(e,t,r)=>{var i=r(332);e.exports=function(e,t){e=e||{};Object.keys(t).forEach((function(r){if(typeof e[r]==="undefined"){e[r]=i(t[r])}}));return e}},315:(e,t,r)=>{try{var i=r(837);if(typeof i.inherits!=="function")throw"";e.exports=i.inherits}catch(t){e.exports=r(494)}},494:e=>{if(typeof Object.create==="function"){e.exports=function inherits(e,t){if(t){e.super_=t;e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}})}}}else{e.exports=function inherits(e,t){if(t){e.super_=t;var TempCtor=function(){};TempCtor.prototype=t.prototype;e.prototype=new TempCtor;e.prototype.constructor=e}}}},520:e=>{"use strict";e.exports=({stream:e=process.stdout}={})=>Boolean(e&&e.isTTY&&process.env.TERM!=="dumb"&&!("CI"in process.env))},457:e=>{"use strict";e.exports=()=>{if(process.platform!=="win32"){return true}return Boolean(process.env.CI)||Boolean(process.env.WT_SESSION)||process.env.TERM_PROGRAM==="vscode"||process.env.TERM==="xterm-256color"||process.env.TERM==="alacritty"}},59:(e,t,r)=>{"use strict";const i=r(234);const n=r(457);const s={info:i.blue("ℹ"),success:i.green("✔"),warning:i.yellow("⚠"),error:i.red("✖")};const a={info:i.blue("i"),success:i.green("√"),warning:i.yellow("‼"),error:i.red("×")};e.exports=n()?s:a},693:e=>{"use strict";const mimicFn=(e,t)=>{for(const r of Reflect.ownKeys(t)){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}return e};e.exports=mimicFn;e.exports["default"]=mimicFn},591:(e,t,r)=>{"use strict";const i=r(693);const n=new WeakMap;const onetime=(e,t={})=>{if(typeof e!=="function"){throw new TypeError("Expected a function")}let r;let s=0;const a=e.displayName||e.name||"<anonymous>";const onetime=function(...i){n.set(onetime,++s);if(s===1){r=e.apply(this,i);e=null}else if(t.throw===true){throw new Error(`Function \`${a}\` can only be called once`)}return r};i(onetime,e);n.set(onetime,s);return onetime};e.exports=onetime;e.exports["default"]=onetime;e.exports.callCount=e=>{if(!n.has(e)){throw new Error(`The given function \`${e.name}\` is not wrapped by the \`onetime\` package`)}return n.get(e)}},942:(e,t,r)=>{"use strict";const i=r(521);const n=r(234);const s=r(805);const a=r(414);const o=r(59);const f=r(286);const l=r(74);const u=r(520);const h=r(457);const{BufferListStream:d}=r(49);const p=Symbol("text");const _=Symbol("prefixText");const b=3;class StdinDiscarder{constructor(){this.requests=0;this.mutedStream=new d;this.mutedStream.pipe(process.stdout);const e=this;this.ourEmit=function(t,r,...i){const{stdin:n}=process;if(e.requests>0||n.emit===e.ourEmit){if(t==="keypress"){return}if(t==="data"&&r.includes(b)){process.emit("SIGINT")}Reflect.apply(e.oldEmit,this,[t,r,...i])}else{Reflect.apply(process.stdin.emit,this,[t,r,...i])}}}start(){this.requests++;if(this.requests===1){this.realStart()}}stop(){if(this.requests<=0){throw new Error("`stop` called more times than `start`")}this.requests--;if(this.requests===0){this.realStop()}}realStart(){if(process.platform==="win32"){return}this.rl=i.createInterface({input:process.stdin,output:this.mutedStream});this.rl.on("SIGINT",(()=>{if(process.listenerCount("SIGINT")===0){process.emit("SIGINT")}else{this.rl.close();process.kill(process.pid,"SIGINT")}}))}realStop(){if(process.platform==="win32"){return}this.rl.close();this.rl=undefined}}let g;class Ora{constructor(e){if(!g){g=new StdinDiscarder}if(typeof e==="string"){e={text:e}}this.options={text:"",color:"cyan",stream:process.stderr,discardStdin:true,...e};this.spinner=this.options.spinner;this.color=this.options.color;this.hideCursor=this.options.hideCursor!==false;this.interval=this.options.interval||this.spinner.interval||100;this.stream=this.options.stream;this.id=undefined;this.isEnabled=typeof this.options.isEnabled==="boolean"?this.options.isEnabled:u({stream:this.stream});this.isSilent=typeof this.options.isSilent==="boolean"?this.options.isSilent:false;this.text=this.options.text;this.prefixText=this.options.prefixText;this.linesToClear=0;this.indent=this.options.indent;this.discardStdin=this.options.discardStdin;this.isDiscardingStdin=false}get indent(){return this._indent}set indent(e=0){if(!(e>=0&&Number.isInteger(e))){throw new Error("The `indent` option must be an integer from 0 and up")}this._indent=e}_updateInterval(e){if(e!==undefined){this.interval=e}}get spinner(){return this._spinner}set spinner(e){this.frameIndex=0;if(typeof e==="object"){if(e.frames===undefined){throw new Error("The given spinner must have a `frames` property")}this._spinner=e}else if(!h()){this._spinner=a.line}else if(e===undefined){this._spinner=a.dots}else if(e!=="default"&&a[e]){this._spinner=a[e]}else{throw new Error(`There is no built-in spinner named '${e}'. See https://github.com/sindresorhus/cli-spinners/blob/main/spinners.json for a full list.`)}this._updateInterval(this._spinner.interval)}get text(){return this[p]}set text(e){this[p]=e;this.updateLineCount()}get prefixText(){return this[_]}set prefixText(e){this[_]=e;this.updateLineCount()}get isSpinning(){return this.id!==undefined}getFullPrefixText(e=this[_],t=" "){if(typeof e==="string"){return e+t}if(typeof e==="function"){return e()+t}return""}updateLineCount(){const e=this.stream.columns||80;const t=this.getFullPrefixText(this.prefixText,"-");this.lineCount=0;for(const r of f(t+"--"+this[p]).split("\n")){this.lineCount+=Math.max(1,Math.ceil(l(r)/e))}}get isEnabled(){return this._isEnabled&&!this.isSilent}set isEnabled(e){if(typeof e!=="boolean"){throw new TypeError("The `isEnabled` option must be a boolean")}this._isEnabled=e}get isSilent(){return this._isSilent}set isSilent(e){if(typeof e!=="boolean"){throw new TypeError("The `isSilent` option must be a boolean")}this._isSilent=e}frame(){const{frames:e}=this.spinner;let t=e[this.frameIndex];if(this.color){t=n[this.color](t)}this.frameIndex=++this.frameIndex%e.length;const r=typeof this.prefixText==="string"&&this.prefixText!==""?this.prefixText+" ":"";const i=typeof this.text==="string"?" "+this.text:"";return r+t+i}clear(){if(!this.isEnabled||!this.stream.isTTY){return this}for(let e=0;e<this.linesToClear;e++){if(e>0){this.stream.moveCursor(0,-1)}this.stream.clearLine();this.stream.cursorTo(this.indent)}this.linesToClear=0;return this}render(){if(this.isSilent){return this}this.clear();this.stream.write(this.frame());this.linesToClear=this.lineCount;return this}start(e){if(e){this.text=e}if(this.isSilent){return this}if(!this.isEnabled){if(this.text){this.stream.write(`- ${this.text}\n`)}return this}if(this.isSpinning){return this}if(this.hideCursor){s.hide(this.stream)}if(this.discardStdin&&process.stdin.isTTY){this.isDiscardingStdin=true;g.start()}this.render();this.id=setInterval(this.render.bind(this),this.interval);return this}stop(){if(!this.isEnabled){return this}clearInterval(this.id);this.id=undefined;this.frameIndex=0;this.clear();if(this.hideCursor){s.show(this.stream)}if(this.discardStdin&&process.stdin.isTTY&&this.isDiscardingStdin){g.stop();this.isDiscardingStdin=false}return this}succeed(e){return this.stopAndPersist({symbol:o.success,text:e})}fail(e){return this.stopAndPersist({symbol:o.error,text:e})}warn(e){return this.stopAndPersist({symbol:o.warning,text:e})}info(e){return this.stopAndPersist({symbol:o.info,text:e})}stopAndPersist(e={}){if(this.isSilent){return this}const t=e.prefixText||this.prefixText;const r=e.text||this.text;const i=typeof r==="string"?" "+r:"";this.stop();this.stream.write(`${this.getFullPrefixText(t," ")}${e.symbol||" "}${i}\n`);return this}}const oraFactory=function(e){return new Ora(e)};e.exports=oraFactory;e.exports.promise=(e,t)=>{if(typeof e.then!=="function"){throw new TypeError("Parameter `action` must be a Promise")}const r=new Ora(t);r.start();(async()=>{try{await e;r.succeed()}catch{r.fail()}})();return r}},959:e=>{"use strict";const t={};function createErrorType(e,r,i){if(!i){i=Error}function getMessage(e,t,i){if(typeof r==="string"){return r}else{return r(e,t,i)}}class NodeError extends i{constructor(e,t,r){super(getMessage(e,t,r))}}NodeError.prototype.name=i.name;NodeError.prototype.code=e;t[e]=NodeError}function oneOf(e,t){if(Array.isArray(e)){const r=e.length;e=e.map((e=>String(e)));if(r>2){return`one of ${t} ${e.slice(0,r-1).join(", ")}, or `+e[r-1]}else if(r===2){return`one of ${t} ${e[0]} or ${e[1]}`}else{return`of ${t} ${e[0]}`}}else{return`of ${t} ${String(e)}`}}function startsWith(e,t,r){return e.substr(!r||r<0?0:+r,t.length)===t}function endsWith(e,t,r){if(r===undefined||r>e.length){r=e.length}return e.substring(r-t.length,r)===t}function includes(e,t,r){if(typeof r!=="number"){r=0}if(r+t.length>e.length){return false}else{return e.indexOf(t,r)!==-1}}createErrorType("ERR_INVALID_OPT_VALUE",(function(e,t){return'The value "'+t+'" is invalid for option "'+e+'"'}),TypeError);createErrorType("ERR_INVALID_ARG_TYPE",(function(e,t,r){let i;if(typeof t==="string"&&startsWith(t,"not ")){i="must not be";t=t.replace(/^not /,"")}else{i="must be"}let n;if(endsWith(e," argument")){n=`The ${e} ${i} ${oneOf(t,"type")}`}else{const r=includes(e,".")?"property":"argument";n=`The "${e}" ${r} ${i} ${oneOf(t,"type")}`}n+=`. Received type ${typeof r}`;return n}),TypeError);createErrorType("ERR_STREAM_PUSH_AFTER_EOF","stream.push() after EOF");createErrorType("ERR_METHOD_NOT_IMPLEMENTED",(function(e){return"The "+e+" method is not implemented"}));createErrorType("ERR_STREAM_PREMATURE_CLOSE","Premature close");createErrorType("ERR_STREAM_DESTROYED",(function(e){return"Cannot call "+e+" after a stream was destroyed"}));createErrorType("ERR_MULTIPLE_CALLBACK","Callback called multiple times");createErrorType("ERR_STREAM_CANNOT_PIPE","Cannot pipe, not readable");createErrorType("ERR_STREAM_WRITE_AFTER_END","write after end");createErrorType("ERR_STREAM_NULL_VALUES","May not write null values to stream",TypeError);createErrorType("ERR_UNKNOWN_ENCODING",(function(e){return"Unknown encoding: "+e}),TypeError);createErrorType("ERR_STREAM_UNSHIFT_AFTER_END_EVENT","stream.unshift() after end event");e.exports.q=t},548:(e,t,r)=>{"use strict";var i=Object.keys||function(e){var t=[];for(var r in e){t.push(r)}return t};e.exports=Duplex;var n=r(852);var s=r(379);r(315)(Duplex,n);{var a=i(s.prototype);for(var o=0;o<a.length;o++){var f=a[o];if(!Duplex.prototype[f])Duplex.prototype[f]=s.prototype[f]}}function Duplex(e){if(!(this instanceof Duplex))return new Duplex(e);n.call(this,e);s.call(this,e);this.allowHalfOpen=true;if(e){if(e.readable===false)this.readable=false;if(e.writable===false)this.writable=false;if(e.allowHalfOpen===false){this.allowHalfOpen=false;this.once("end",onend)}}}Object.defineProperty(Duplex.prototype,"writableHighWaterMark",{enumerable:false,get:function get(){return this._writableState.highWaterMark}});Object.defineProperty(Duplex.prototype,"writableBuffer",{enumerable:false,get:function get(){return this._writableState&&this._writableState.getBuffer()}});Object.defineProperty(Duplex.prototype,"writableLength",{enumerable:false,get:function get(){return this._writableState.length}});function onend(){if(this._writableState.ended)return;process.nextTick(onEndNT,this)}function onEndNT(e){e.end()}Object.defineProperty(Duplex.prototype,"destroyed",{enumerable:false,get:function get(){if(this._readableState===undefined||this._writableState===undefined){return false}return this._readableState.destroyed&&this._writableState.destroyed},set:function set(e){if(this._readableState===undefined||this._writableState===undefined){return}this._readableState.destroyed=e;this._writableState.destroyed=e}})},184:(e,t,r)=>{"use strict";e.exports=PassThrough;var i=r(770);r(315)(PassThrough,i);function PassThrough(e){if(!(this instanceof PassThrough))return new PassThrough(e);i.call(this,e)}PassThrough.prototype._transform=function(e,t,r){r(null,e)}},852:(e,t,r)=>{"use strict";e.exports=Readable;var i;Readable.ReadableState=ReadableState;var n=r(361).EventEmitter;var s=function EElistenerCount(e,t){return e.listeners(t).length};var a=r(604);var o=r(300).Buffer;var f=global.Uint8Array||function(){};function _uint8ArrayToBuffer(e){return o.from(e)}function _isUint8Array(e){return o.isBuffer(e)||e instanceof f}var l=r(837);var u;if(l&&l.debuglog){u=l.debuglog("stream")}else{u=function debug(){}}var h=r(706);var d=r(97);var p=r(841),_=p.getHighWaterMark;var b=r(959).q,g=b.ERR_INVALID_ARG_TYPE,y=b.ERR_STREAM_PUSH_AFTER_EOF,m=b.ERR_METHOD_NOT_IMPLEMENTED,v=b.ERR_STREAM_UNSHIFT_AFTER_END_EVENT;var w;var S;var R;r(315)(Readable,a);var E=d.errorOrDestroy;var x=["error","close","destroy","pause","resume"];function prependListener(e,t,r){if(typeof e.prependListener==="function")return e.prependListener(t,r);if(!e._events||!e._events[t])e.on(t,r);else if(Array.isArray(e._events[t]))e._events[t].unshift(r);else e._events[t]=[r,e._events[t]]}function ReadableState(e,t,n){i=i||r(548);e=e||{};if(typeof n!=="boolean")n=t instanceof i;this.objectMode=!!e.objectMode;if(n)this.objectMode=this.objectMode||!!e.readableObjectMode;this.highWaterMark=_(this,e,"readableHighWaterMark",n);this.buffer=new h;this.length=0;this.pipes=null;this.pipesCount=0;this.flowing=null;this.ended=false;this.endEmitted=false;this.reading=false;this.sync=true;this.needReadable=false;this.emittedReadable=false;this.readableListening=false;this.resumeScheduled=false;this.paused=true;this.emitClose=e.emitClose!==false;this.autoDestroy=!!e.autoDestroy;this.destroyed=false;this.defaultEncoding=e.defaultEncoding||"utf8";this.awaitDrain=0;this.readingMore=false;this.decoder=null;this.encoding=null;if(e.encoding){if(!w)w=r(259).s;this.decoder=new w(e.encoding);this.encoding=e.encoding}}function Readable(e){i=i||r(548);if(!(this instanceof Readable))return new Readable(e);var t=this instanceof i;this._readableState=new ReadableState(e,this,t);this.readable=true;if(e){if(typeof e.read==="function")this._read=e.read;if(typeof e.destroy==="function")this._destroy=e.destroy}a.call(this)}Object.defineProperty(Readable.prototype,"destroyed",{enumerable:false,get:function get(){if(this._readableState===undefined){return false}return this._readableState.destroyed},set:function set(e){if(!this._readableState){return}this._readableState.destroyed=e}});Readable.prototype.destroy=d.destroy;Readable.prototype._undestroy=d.undestroy;Readable.prototype._destroy=function(e,t){t(e)};Readable.prototype.push=function(e,t){var r=this._readableState;var i;if(!r.objectMode){if(typeof e==="string"){t=t||r.defaultEncoding;if(t!==r.encoding){e=o.from(e,t);t=""}i=true}}else{i=true}return readableAddChunk(this,e,t,false,i)};Readable.prototype.unshift=function(e){return readableAddChunk(this,e,null,true,false)};function readableAddChunk(e,t,r,i,n){u("readableAddChunk",t);var s=e._readableState;if(t===null){s.reading=false;onEofChunk(e,s)}else{var a;if(!n)a=chunkInvalid(s,t);if(a){E(e,a)}else if(s.objectMode||t&&t.length>0){if(typeof t!=="string"&&!s.objectMode&&Object.getPrototypeOf(t)!==o.prototype){t=_uint8ArrayToBuffer(t)}if(i){if(s.endEmitted)E(e,new v);else addChunk(e,s,t,true)}else if(s.ended){E(e,new y)}else if(s.destroyed){return false}else{s.reading=false;if(s.decoder&&!r){t=s.decoder.write(t);if(s.objectMode||t.length!==0)addChunk(e,s,t,false);else maybeReadMore(e,s)}else{addChunk(e,s,t,false)}}}else if(!i){s.reading=false;maybeReadMore(e,s)}}return!s.ended&&(s.length<s.highWaterMark||s.length===0)}function addChunk(e,t,r,i){if(t.flowing&&t.length===0&&!t.sync){t.awaitDrain=0;e.emit("data",r)}else{t.length+=t.objectMode?1:r.length;if(i)t.buffer.unshift(r);else t.buffer.push(r);if(t.needReadable)emitReadable(e)}maybeReadMore(e,t)}function chunkInvalid(e,t){var r;if(!_isUint8Array(t)&&typeof t!=="string"&&t!==undefined&&!e.objectMode){r=new g("chunk",["string","Buffer","Uint8Array"],t)}return r}Readable.prototype.isPaused=function(){return this._readableState.flowing===false};Readable.prototype.setEncoding=function(e){if(!w)w=r(259).s;var t=new w(e);this._readableState.decoder=t;this._readableState.encoding=this._readableState.decoder.encoding;var i=this._readableState.buffer.head;var n="";while(i!==null){n+=t.write(i.data);i=i.next}this._readableState.buffer.clear();if(n!=="")this._readableState.buffer.push(n);this._readableState.length=n.length;return this};var T=1073741824;function computeNewHighWaterMark(e){if(e>=T){e=T}else{e--;e|=e>>>1;e|=e>>>2;e|=e>>>4;e|=e>>>8;e|=e>>>16;e++}return e}function howMuchToRead(e,t){if(e<=0||t.length===0&&t.ended)return 0;if(t.objectMode)return 1;if(e!==e){if(t.flowing&&t.length)return t.buffer.head.data.length;else return t.length}if(e>t.highWaterMark)t.highWaterMark=computeNewHighWaterMark(e);if(e<=t.length)return e;if(!t.ended){t.needReadable=true;return 0}return t.length}Readable.prototype.read=function(e){u("read",e);e=parseInt(e,10);var t=this._readableState;var r=e;if(e!==0)t.emittedReadable=false;if(e===0&&t.needReadable&&((t.highWaterMark!==0?t.length>=t.highWaterMark:t.length>0)||t.ended)){u("read: emitReadable",t.length,t.ended);if(t.length===0&&t.ended)endReadable(this);else emitReadable(this);return null}e=howMuchToRead(e,t);if(e===0&&t.ended){if(t.length===0)endReadable(this);return null}var i=t.needReadable;u("need readable",i);if(t.length===0||t.length-e<t.highWaterMark){i=true;u("length less than watermark",i)}if(t.ended||t.reading){i=false;u("reading or ended",i)}else if(i){u("do read");t.reading=true;t.sync=true;if(t.length===0)t.needReadable=true;this._read(t.highWaterMark);t.sync=false;if(!t.reading)e=howMuchToRead(r,t)}var n;if(e>0)n=fromList(e,t);else n=null;if(n===null){t.needReadable=t.length<=t.highWaterMark;e=0}else{t.length-=e;t.awaitDrain=0}if(t.length===0){if(!t.ended)t.needReadable=true;if(r!==e&&t.ended)endReadable(this)}if(n!==null)this.emit("data",n);return n};function onEofChunk(e,t){u("onEofChunk");if(t.ended)return;if(t.decoder){var r=t.decoder.end();if(r&&r.length){t.buffer.push(r);t.length+=t.objectMode?1:r.length}}t.ended=true;if(t.sync){emitReadable(e)}else{t.needReadable=false;if(!t.emittedReadable){t.emittedReadable=true;emitReadable_(e)}}}function emitReadable(e){var t=e._readableState;u("emitReadable",t.needReadable,t.emittedReadable);t.needReadable=false;if(!t.emittedReadable){u("emitReadable",t.flowing);t.emittedReadable=true;process.nextTick(emitReadable_,e)}}function emitReadable_(e){var t=e._readableState;u("emitReadable_",t.destroyed,t.length,t.ended);if(!t.destroyed&&(t.length||t.ended)){e.emit("readable");t.emittedReadable=false}t.needReadable=!t.flowing&&!t.ended&&t.length<=t.highWaterMark;flow(e)}function maybeReadMore(e,t){if(!t.readingMore){t.readingMore=true;process.nextTick(maybeReadMore_,e,t)}}function maybeReadMore_(e,t){while(!t.reading&&!t.ended&&(t.length<t.highWaterMark||t.flowing&&t.length===0)){var r=t.length;u("maybeReadMore read 0");e.read(0);if(r===t.length)break}t.readingMore=false}Readable.prototype._read=function(e){E(this,new m("_read()"))};Readable.prototype.pipe=function(e,t){var r=this;var i=this._readableState;switch(i.pipesCount){case 0:i.pipes=e;break;case 1:i.pipes=[i.pipes,e];break;default:i.pipes.push(e);break}i.pipesCount+=1;u("pipe count=%d opts=%j",i.pipesCount,t);var n=(!t||t.end!==false)&&e!==process.stdout&&e!==process.stderr;var a=n?onend:unpipe;if(i.endEmitted)process.nextTick(a);else r.once("end",a);e.on("unpipe",onunpipe);function onunpipe(e,t){u("onunpipe");if(e===r){if(t&&t.hasUnpiped===false){t.hasUnpiped=true;cleanup()}}}function onend(){u("onend");e.end()}var o=pipeOnDrain(r);e.on("drain",o);var f=false;function cleanup(){u("cleanup");e.removeListener("close",onclose);e.removeListener("finish",onfinish);e.removeListener("drain",o);e.removeListener("error",onerror);e.removeListener("unpipe",onunpipe);r.removeListener("end",onend);r.removeListener("end",unpipe);r.removeListener("data",ondata);f=true;if(i.awaitDrain&&(!e._writableState||e._writableState.needDrain))o()}r.on("data",ondata);function ondata(t){u("ondata");var n=e.write(t);u("dest.write",n);if(n===false){if((i.pipesCount===1&&i.pipes===e||i.pipesCount>1&&indexOf(i.pipes,e)!==-1)&&!f){u("false write response, pause",i.awaitDrain);i.awaitDrain++}r.pause()}}function onerror(t){u("onerror",t);unpipe();e.removeListener("error",onerror);if(s(e,"error")===0)E(e,t)}prependListener(e,"error",onerror);function onclose(){e.removeListener("finish",onfinish);unpipe()}e.once("close",onclose);function onfinish(){u("onfinish");e.removeListener("close",onclose);unpipe()}e.once("finish",onfinish);function unpipe(){u("unpipe");r.unpipe(e)}e.emit("pipe",r);if(!i.flowing){u("pipe resume");r.resume()}return e};function pipeOnDrain(e){return function pipeOnDrainFunctionResult(){var t=e._readableState;u("pipeOnDrain",t.awaitDrain);if(t.awaitDrain)t.awaitDrain--;if(t.awaitDrain===0&&s(e,"data")){t.flowing=true;flow(e)}}}Readable.prototype.unpipe=function(e){var t=this._readableState;var r={hasUnpiped:false};if(t.pipesCount===0)return this;if(t.pipesCount===1){if(e&&e!==t.pipes)return this;if(!e)e=t.pipes;t.pipes=null;t.pipesCount=0;t.flowing=false;if(e)e.emit("unpipe",this,r);return this}if(!e){var i=t.pipes;var n=t.pipesCount;t.pipes=null;t.pipesCount=0;t.flowing=false;for(var s=0;s<n;s++){i[s].emit("unpipe",this,{hasUnpiped:false})}return this}var a=indexOf(t.pipes,e);if(a===-1)return this;t.pipes.splice(a,1);t.pipesCount-=1;if(t.pipesCount===1)t.pipes=t.pipes[0];e.emit("unpipe",this,r);return this};Readable.prototype.on=function(e,t){var r=a.prototype.on.call(this,e,t);var i=this._readableState;if(e==="data"){i.readableListening=this.listenerCount("readable")>0;if(i.flowing!==false)this.resume()}else if(e==="readable"){if(!i.endEmitted&&!i.readableListening){i.readableListening=i.needReadable=true;i.flowing=false;i.emittedReadable=false;u("on readable",i.length,i.reading);if(i.length){emitReadable(this)}else if(!i.reading){process.nextTick(nReadingNextTick,this)}}}return r};Readable.prototype.addListener=Readable.prototype.on;Readable.prototype.removeListener=function(e,t){var r=a.prototype.removeListener.call(this,e,t);if(e==="readable"){process.nextTick(updateReadableListening,this)}return r};Readable.prototype.removeAllListeners=function(e){var t=a.prototype.removeAllListeners.apply(this,arguments);if(e==="readable"||e===undefined){process.nextTick(updateReadableListening,this)}return t};function updateReadableListening(e){var t=e._readableState;t.readableListening=e.listenerCount("readable")>0;if(t.resumeScheduled&&!t.paused){t.flowing=true}else if(e.listenerCount("data")>0){e.resume()}}function nReadingNextTick(e){u("readable nexttick read 0");e.read(0)}Readable.prototype.resume=function(){var e=this._readableState;if(!e.flowing){u("resume");e.flowing=!e.readableListening;resume(this,e)}e.paused=false;return this};function resume(e,t){if(!t.resumeScheduled){t.resumeScheduled=true;process.nextTick(resume_,e,t)}}function resume_(e,t){u("resume",t.reading);if(!t.reading){e.read(0)}t.resumeScheduled=false;e.emit("resume");flow(e);if(t.flowing&&!t.reading)e.read(0)}Readable.prototype.pause=function(){u("call pause flowing=%j",this._readableState.flowing);if(this._readableState.flowing!==false){u("pause");this._readableState.flowing=false;this.emit("pause")}this._readableState.paused=true;return this};function flow(e){var t=e._readableState;u("flow",t.flowing);while(t.flowing&&e.read()!==null){}}Readable.prototype.wrap=function(e){var t=this;var r=this._readableState;var i=false;e.on("end",(function(){u("wrapped end");if(r.decoder&&!r.ended){var e=r.decoder.end();if(e&&e.length)t.push(e)}t.push(null)}));e.on("data",(function(n){u("wrapped data");if(r.decoder)n=r.decoder.write(n);if(r.objectMode&&(n===null||n===undefined))return;else if(!r.objectMode&&(!n||!n.length))return;var s=t.push(n);if(!s){i=true;e.pause()}}));for(var n in e){if(this[n]===undefined&&typeof e[n]==="function"){this[n]=function methodWrap(t){return function methodWrapReturnFunction(){return e[t].apply(e,arguments)}}(n)}}for(var s=0;s<x.length;s++){e.on(x[s],this.emit.bind(this,x[s]))}this._read=function(t){u("wrapped _read",t);if(i){i=false;e.resume()}};return this};if(typeof Symbol==="function"){Readable.prototype[Symbol.asyncIterator]=function(){if(S===undefined){S=r(87)}return S(this)}}Object.defineProperty(Readable.prototype,"readableHighWaterMark",{enumerable:false,get:function get(){return this._readableState.highWaterMark}});Object.defineProperty(Readable.prototype,"readableBuffer",{enumerable:false,get:function get(){return this._readableState&&this._readableState.buffer}});Object.defineProperty(Readable.prototype,"readableFlowing",{enumerable:false,get:function get(){return this._readableState.flowing},set:function set(e){if(this._readableState){this._readableState.flowing=e}}});Readable._fromList=fromList;Object.defineProperty(Readable.prototype,"readableLength",{enumerable:false,get:function get(){return this._readableState.length}});function fromList(e,t){if(t.length===0)return null;var r;if(t.objectMode)r=t.buffer.shift();else if(!e||e>=t.length){if(t.decoder)r=t.buffer.join("");else if(t.buffer.length===1)r=t.buffer.first();else r=t.buffer.concat(t.length);t.buffer.clear()}else{r=t.buffer.consume(e,t.decoder)}return r}function endReadable(e){var t=e._readableState;u("endReadable",t.endEmitted);if(!t.endEmitted){t.ended=true;process.nextTick(endReadableNT,t,e)}}function endReadableNT(e,t){u("endReadableNT",e.endEmitted,e.length);if(!e.endEmitted&&e.length===0){e.endEmitted=true;t.readable=false;t.emit("end");if(e.autoDestroy){var r=t._writableState;if(!r||r.autoDestroy&&r.finished){t.destroy()}}}}if(typeof Symbol==="function"){Readable.from=function(e,t){if(R===undefined){R=r(668)}return R(Readable,e,t)}}function indexOf(e,t){for(var r=0,i=e.length;r<i;r++){if(e[r]===t)return r}return-1}},770:(e,t,r)=>{"use strict";e.exports=Transform;var i=r(959).q,n=i.ERR_METHOD_NOT_IMPLEMENTED,s=i.ERR_MULTIPLE_CALLBACK,a=i.ERR_TRANSFORM_ALREADY_TRANSFORMING,o=i.ERR_TRANSFORM_WITH_LENGTH_0;var f=r(548);r(315)(Transform,f);function afterTransform(e,t){var r=this._transformState;r.transforming=false;var i=r.writecb;if(i===null){return this.emit("error",new s)}r.writechunk=null;r.writecb=null;if(t!=null)this.push(t);i(e);var n=this._readableState;n.reading=false;if(n.needReadable||n.length<n.highWaterMark){this._read(n.highWaterMark)}}function Transform(e){if(!(this instanceof Transform))return new Transform(e);f.call(this,e);this._transformState={afterTransform:afterTransform.bind(this),needTransform:false,transforming:false,writecb:null,writechunk:null,writeencoding:null};this._readableState.needReadable=true;this._readableState.sync=false;if(e){if(typeof e.transform==="function")this._transform=e.transform;if(typeof e.flush==="function")this._flush=e.flush}this.on("prefinish",prefinish)}function prefinish(){var e=this;if(typeof this._flush==="function"&&!this._readableState.destroyed){this._flush((function(t,r){done(e,t,r)}))}else{done(this,null,null)}}Transform.prototype.push=function(e,t){this._transformState.needTransform=false;return f.prototype.push.call(this,e,t)};Transform.prototype._transform=function(e,t,r){r(new n("_transform()"))};Transform.prototype._write=function(e,t,r){var i=this._transformState;i.writecb=r;i.writechunk=e;i.writeencoding=t;if(!i.transforming){var n=this._readableState;if(i.needTransform||n.needReadable||n.length<n.highWaterMark)this._read(n.highWaterMark)}};Transform.prototype._read=function(e){var t=this._transformState;if(t.writechunk!==null&&!t.transforming){t.transforming=true;this._transform(t.writechunk,t.writeencoding,t.afterTransform)}else{t.needTransform=true}};Transform.prototype._destroy=function(e,t){f.prototype._destroy.call(this,e,(function(e){t(e)}))};function done(e,t,r){if(t)return e.emit("error",t);if(r!=null)e.push(r);if(e._writableState.length)throw new o;if(e._transformState.transforming)throw new a;return e.push(null)}},379:(e,t,r)=>{"use strict";e.exports=Writable;function WriteReq(e,t,r){this.chunk=e;this.encoding=t;this.callback=r;this.next=null}function CorkedRequest(e){var t=this;this.next=null;this.entry=null;this.finish=function(){onCorkedFinish(t,e)}}var i;Writable.WritableState=WritableState;var n={deprecate:r(125)};var s=r(604);var a=r(300).Buffer;var o=global.Uint8Array||function(){};function _uint8ArrayToBuffer(e){return a.from(e)}function _isUint8Array(e){return a.isBuffer(e)||e instanceof o}var f=r(97);var l=r(841),u=l.getHighWaterMark;var h=r(959).q,d=h.ERR_INVALID_ARG_TYPE,p=h.ERR_METHOD_NOT_IMPLEMENTED,_=h.ERR_MULTIPLE_CALLBACK,b=h.ERR_STREAM_CANNOT_PIPE,g=h.ERR_STREAM_DESTROYED,y=h.ERR_STREAM_NULL_VALUES,m=h.ERR_STREAM_WRITE_AFTER_END,v=h.ERR_UNKNOWN_ENCODING;var w=f.errorOrDestroy;r(315)(Writable,s);function nop(){}function WritableState(e,t,n){i=i||r(548);e=e||{};if(typeof n!=="boolean")n=t instanceof i;this.objectMode=!!e.objectMode;if(n)this.objectMode=this.objectMode||!!e.writableObjectMode;this.highWaterMark=u(this,e,"writableHighWaterMark",n);this.finalCalled=false;this.needDrain=false;this.ending=false;this.ended=false;this.finished=false;this.destroyed=false;var s=e.decodeStrings===false;this.decodeStrings=!s;this.defaultEncoding=e.defaultEncoding||"utf8";this.length=0;this.writing=false;this.corked=0;this.sync=true;this.bufferProcessing=false;this.onwrite=function(e){onwrite(t,e)};this.writecb=null;this.writelen=0;this.bufferedRequest=null;this.lastBufferedRequest=null;this.pendingcb=0;this.prefinished=false;this.errorEmitted=false;this.emitClose=e.emitClose!==false;this.autoDestroy=!!e.autoDestroy;this.bufferedRequestCount=0;this.corkedRequestsFree=new CorkedRequest(this)}WritableState.prototype.getBuffer=function getBuffer(){var e=this.bufferedRequest;var t=[];while(e){t.push(e);e=e.next}return t};(function(){try{Object.defineProperty(WritableState.prototype,"buffer",{get:n.deprecate((function writableStateBufferGetter(){return this.getBuffer()}),"_writableState.buffer is deprecated. Use _writableState.getBuffer "+"instead.","DEP0003")})}catch(e){}})();var S;if(typeof Symbol==="function"&&Symbol.hasInstance&&typeof Function.prototype[Symbol.hasInstance]==="function"){S=Function.prototype[Symbol.hasInstance];Object.defineProperty(Writable,Symbol.hasInstance,{value:function value(e){if(S.call(this,e))return true;if(this!==Writable)return false;return e&&e._writableState instanceof WritableState}})}else{S=function realHasInstance(e){return e instanceof this}}function Writable(e){i=i||r(548);var t=this instanceof i;if(!t&&!S.call(Writable,this))return new Writable(e);this._writableState=new WritableState(e,this,t);this.writable=true;if(e){if(typeof e.write==="function")this._write=e.write;if(typeof e.writev==="function")this._writev=e.writev;if(typeof e.destroy==="function")this._destroy=e.destroy;if(typeof e.final==="function")this._final=e.final}s.call(this)}Writable.prototype.pipe=function(){w(this,new b)};function writeAfterEnd(e,t){var r=new m;w(e,r);process.nextTick(t,r)}function validChunk(e,t,r,i){var n;if(r===null){n=new y}else if(typeof r!=="string"&&!t.objectMode){n=new d("chunk",["string","Buffer"],r)}if(n){w(e,n);process.nextTick(i,n);return false}return true}Writable.prototype.write=function(e,t,r){var i=this._writableState;var n=false;var s=!i.objectMode&&_isUint8Array(e);if(s&&!a.isBuffer(e)){e=_uint8ArrayToBuffer(e)}if(typeof t==="function"){r=t;t=null}if(s)t="buffer";else if(!t)t=i.defaultEncoding;if(typeof r!=="function")r=nop;if(i.ending)writeAfterEnd(this,r);else if(s||validChunk(this,i,e,r)){i.pendingcb++;n=writeOrBuffer(this,i,s,e,t,r)}return n};Writable.prototype.cork=function(){this._writableState.corked++};Writable.prototype.uncork=function(){var e=this._writableState;if(e.corked){e.corked--;if(!e.writing&&!e.corked&&!e.bufferProcessing&&e.bufferedRequest)clearBuffer(this,e)}};Writable.prototype.setDefaultEncoding=function setDefaultEncoding(e){if(typeof e==="string")e=e.toLowerCase();if(!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((e+"").toLowerCase())>-1))throw new v(e);this._writableState.defaultEncoding=e;return this};Object.defineProperty(Writable.prototype,"writableBuffer",{enumerable:false,get:function get(){return this._writableState&&this._writableState.getBuffer()}});function decodeChunk(e,t,r){if(!e.objectMode&&e.decodeStrings!==false&&typeof t==="string"){t=a.from(t,r)}return t}Object.defineProperty(Writable.prototype,"writableHighWaterMark",{enumerable:false,get:function get(){return this._writableState.highWaterMark}});function writeOrBuffer(e,t,r,i,n,s){if(!r){var a=decodeChunk(t,i,n);if(i!==a){r=true;n="buffer";i=a}}var o=t.objectMode?1:i.length;t.length+=o;var f=t.length<t.highWaterMark;if(!f)t.needDrain=true;if(t.writing||t.corked){var l=t.lastBufferedRequest;t.lastBufferedRequest={chunk:i,encoding:n,isBuf:r,callback:s,next:null};if(l){l.next=t.lastBufferedRequest}else{t.bufferedRequest=t.lastBufferedRequest}t.bufferedRequestCount+=1}else{doWrite(e,t,false,o,i,n,s)}return f}function doWrite(e,t,r,i,n,s,a){t.writelen=i;t.writecb=a;t.writing=true;t.sync=true;if(t.destroyed)t.onwrite(new g("write"));else if(r)e._writev(n,t.onwrite);else e._write(n,s,t.onwrite);t.sync=false}function onwriteError(e,t,r,i,n){--t.pendingcb;if(r){process.nextTick(n,i);process.nextTick(finishMaybe,e,t);e._writableState.errorEmitted=true;w(e,i)}else{n(i);e._writableState.errorEmitted=true;w(e,i);finishMaybe(e,t)}}function onwriteStateUpdate(e){e.writing=false;e.writecb=null;e.length-=e.writelen;e.writelen=0}function onwrite(e,t){var r=e._writableState;var i=r.sync;var n=r.writecb;if(typeof n!=="function")throw new _;onwriteStateUpdate(r);if(t)onwriteError(e,r,i,t,n);else{var s=needFinish(r)||e.destroyed;if(!s&&!r.corked&&!r.bufferProcessing&&r.bufferedRequest){clearBuffer(e,r)}if(i){process.nextTick(afterWrite,e,r,s,n)}else{afterWrite(e,r,s,n)}}}function afterWrite(e,t,r,i){if(!r)onwriteDrain(e,t);t.pendingcb--;i();finishMaybe(e,t)}function onwriteDrain(e,t){if(t.length===0&&t.needDrain){t.needDrain=false;e.emit("drain")}}function clearBuffer(e,t){t.bufferProcessing=true;var r=t.bufferedRequest;if(e._writev&&r&&r.next){var i=t.bufferedRequestCount;var n=new Array(i);var s=t.corkedRequestsFree;s.entry=r;var a=0;var o=true;while(r){n[a]=r;if(!r.isBuf)o=false;r=r.next;a+=1}n.allBuffers=o;doWrite(e,t,true,t.length,n,"",s.finish);t.pendingcb++;t.lastBufferedRequest=null;if(s.next){t.corkedRequestsFree=s.next;s.next=null}else{t.corkedRequestsFree=new CorkedRequest(t)}t.bufferedRequestCount=0}else{while(r){var f=r.chunk;var l=r.encoding;var u=r.callback;var h=t.objectMode?1:f.length;doWrite(e,t,false,h,f,l,u);r=r.next;t.bufferedRequestCount--;if(t.writing){break}}if(r===null)t.lastBufferedRequest=null}t.bufferedRequest=r;t.bufferProcessing=false}Writable.prototype._write=function(e,t,r){r(new p("_write()"))};Writable.prototype._writev=null;Writable.prototype.end=function(e,t,r){var i=this._writableState;if(typeof e==="function"){r=e;e=null;t=null}else if(typeof t==="function"){r=t;t=null}if(e!==null&&e!==undefined)this.write(e,t);if(i.corked){i.corked=1;this.uncork()}if(!i.ending)endWritable(this,i,r);return this};Object.defineProperty(Writable.prototype,"writableLength",{enumerable:false,get:function get(){return this._writableState.length}});function needFinish(e){return e.ending&&e.length===0&&e.bufferedRequest===null&&!e.finished&&!e.writing}function callFinal(e,t){e._final((function(r){t.pendingcb--;if(r){w(e,r)}t.prefinished=true;e.emit("prefinish");finishMaybe(e,t)}))}function prefinish(e,t){if(!t.prefinished&&!t.finalCalled){if(typeof e._final==="function"&&!t.destroyed){t.pendingcb++;t.finalCalled=true;process.nextTick(callFinal,e,t)}else{t.prefinished=true;e.emit("prefinish")}}}function finishMaybe(e,t){var r=needFinish(t);if(r){prefinish(e,t);if(t.pendingcb===0){t.finished=true;e.emit("finish");if(t.autoDestroy){var i=e._readableState;if(!i||i.autoDestroy&&i.endEmitted){e.destroy()}}}}return r}function endWritable(e,t,r){t.ending=true;finishMaybe(e,t);if(r){if(t.finished)process.nextTick(r);else e.once("finish",r)}t.ended=true;e.writable=false}function onCorkedFinish(e,t,r){var i=e.entry;e.entry=null;while(i){var n=i.callback;t.pendingcb--;n(r);i=i.next}t.corkedRequestsFree.next=e}Object.defineProperty(Writable.prototype,"destroyed",{enumerable:false,get:function get(){if(this._writableState===undefined){return false}return this._writableState.destroyed},set:function set(e){if(!this._writableState){return}this._writableState.destroyed=e}});Writable.prototype.destroy=f.destroy;Writable.prototype._undestroy=f.undestroy;Writable.prototype._destroy=function(e,t){t(e)}},87:(e,t,r)=>{"use strict";var i;function _defineProperty(e,t,r){if(t in e){Object.defineProperty(e,t,{value:r,enumerable:true,configurable:true,writable:true})}else{e[t]=r}return e}var n=r(641);var s=Symbol("lastResolve");var a=Symbol("lastReject");var o=Symbol("error");var f=Symbol("ended");var l=Symbol("lastPromise");var u=Symbol("handlePromise");var h=Symbol("stream");function createIterResult(e,t){return{value:e,done:t}}function readAndResolve(e){var t=e[s];if(t!==null){var r=e[h].read();if(r!==null){e[l]=null;e[s]=null;e[a]=null;t(createIterResult(r,false))}}}function onReadable(e){process.nextTick(readAndResolve,e)}function wrapForNext(e,t){return function(r,i){e.then((function(){if(t[f]){r(createIterResult(undefined,true));return}t[u](r,i)}),i)}}var d=Object.getPrototypeOf((function(){}));var p=Object.setPrototypeOf((i={get stream(){return this[h]},next:function next(){var e=this;var t=this[o];if(t!==null){return Promise.reject(t)}if(this[f]){return Promise.resolve(createIterResult(undefined,true))}if(this[h].destroyed){return new Promise((function(t,r){process.nextTick((function(){if(e[o]){r(e[o])}else{t(createIterResult(undefined,true))}}))}))}var r=this[l];var i;if(r){i=new Promise(wrapForNext(r,this))}else{var n=this[h].read();if(n!==null){return Promise.resolve(createIterResult(n,false))}i=new Promise(this[u])}this[l]=i;return i}},_defineProperty(i,Symbol.asyncIterator,(function(){return this})),_defineProperty(i,"return",(function _return(){var e=this;return new Promise((function(t,r){e[h].destroy(null,(function(e){if(e){r(e);return}t(createIterResult(undefined,true))}))}))})),i),d);var _=function createReadableStreamAsyncIterator(e){var t;var r=Object.create(p,(t={},_defineProperty(t,h,{value:e,writable:true}),_defineProperty(t,s,{value:null,writable:true}),_defineProperty(t,a,{value:null,writable:true}),_defineProperty(t,o,{value:null,writable:true}),_defineProperty(t,f,{value:e._readableState.endEmitted,writable:true}),_defineProperty(t,u,{value:function value(e,t){var i=r[h].read();if(i){r[l]=null;r[s]=null;r[a]=null;e(createIterResult(i,false))}else{r[s]=e;r[a]=t}},writable:true}),t));r[l]=null;n(e,(function(e){if(e&&e.code!=="ERR_STREAM_PREMATURE_CLOSE"){var t=r[a];if(t!==null){r[l]=null;r[s]=null;r[a]=null;t(e)}r[o]=e;return}var i=r[s];if(i!==null){r[l]=null;r[s]=null;r[a]=null;i(createIterResult(undefined,true))}r[f]=true}));e.on("readable",onReadable.bind(null,r));return r};e.exports=_},706:(e,t,r)=>{"use strict";function ownKeys(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);if(t)i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}));r.push.apply(r,i)}return r}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};if(t%2){ownKeys(Object(r),true).forEach((function(t){_defineProperty(e,t,r[t])}))}else if(Object.getOwnPropertyDescriptors){Object.defineProperties(e,Object.getOwnPropertyDescriptors(r))}else{ownKeys(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}}return e}function _defineProperty(e,t,r){if(t in e){Object.defineProperty(e,t,{value:r,enumerable:true,configurable:true,writable:true})}else{e[t]=r}return e}function _classCallCheck(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}function _defineProperties(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||false;i.configurable=true;if("value"in i)i.writable=true;Object.defineProperty(e,i.key,i)}}function _createClass(e,t,r){if(t)_defineProperties(e.prototype,t);if(r)_defineProperties(e,r);return e}var i=r(300),n=i.Buffer;var s=r(837),a=s.inspect;var o=a&&a.custom||"inspect";function copyBuffer(e,t,r){n.prototype.copy.call(e,t,r)}e.exports=function(){function BufferList(){_classCallCheck(this,BufferList);this.head=null;this.tail=null;this.length=0}_createClass(BufferList,[{key:"push",value:function push(e){var t={data:e,next:null};if(this.length>0)this.tail.next=t;else this.head=t;this.tail=t;++this.length}},{key:"unshift",value:function unshift(e){var t={data:e,next:this.head};if(this.length===0)this.tail=t;this.head=t;++this.length}},{key:"shift",value:function shift(){if(this.length===0)return;var e=this.head.data;if(this.length===1)this.head=this.tail=null;else this.head=this.head.next;--this.length;return e}},{key:"clear",value:function clear(){this.head=this.tail=null;this.length=0}},{key:"join",value:function join(e){if(this.length===0)return"";var t=this.head;var r=""+t.data;while(t=t.next){r+=e+t.data}return r}},{key:"concat",value:function concat(e){if(this.length===0)return n.alloc(0);var t=n.allocUnsafe(e>>>0);var r=this.head;var i=0;while(r){copyBuffer(r.data,t,i);i+=r.data.length;r=r.next}return t}},{key:"consume",value:function consume(e,t){var r;if(e<this.head.data.length){r=this.head.data.slice(0,e);this.head.data=this.head.data.slice(e)}else if(e===this.head.data.length){r=this.shift()}else{r=t?this._getString(e):this._getBuffer(e)}return r}},{key:"first",value:function first(){return this.head.data}},{key:"_getString",value:function _getString(e){var t=this.head;var r=1;var i=t.data;e-=i.length;while(t=t.next){var n=t.data;var s=e>n.length?n.length:e;if(s===n.length)i+=n;else i+=n.slice(0,e);e-=s;if(e===0){if(s===n.length){++r;if(t.next)this.head=t.next;else this.head=this.tail=null}else{this.head=t;t.data=n.slice(s)}break}++r}this.length-=r;return i}},{key:"_getBuffer",value:function _getBuffer(e){var t=n.allocUnsafe(e);var r=this.head;var i=1;r.data.copy(t);e-=r.data.length;while(r=r.next){var s=r.data;var a=e>s.length?s.length:e;s.copy(t,t.length-e,0,a);e-=a;if(e===0){if(a===s.length){++i;if(r.next)this.head=r.next;else this.head=this.tail=null}else{this.head=r;r.data=s.slice(a)}break}++i}this.length-=i;return t}},{key:o,value:function value(e,t){return a(this,_objectSpread({},t,{depth:0,customInspect:false}))}}]);return BufferList}()},97:e=>{"use strict";function destroy(e,t){var r=this;var i=this._readableState&&this._readableState.destroyed;var n=this._writableState&&this._writableState.destroyed;if(i||n){if(t){t(e)}else if(e){if(!this._writableState){process.nextTick(emitErrorNT,this,e)}else if(!this._writableState.errorEmitted){this._writableState.errorEmitted=true;process.nextTick(emitErrorNT,this,e)}}return this}if(this._readableState){this._readableState.destroyed=true}if(this._writableState){this._writableState.destroyed=true}this._destroy(e||null,(function(e){if(!t&&e){if(!r._writableState){process.nextTick(emitErrorAndCloseNT,r,e)}else if(!r._writableState.errorEmitted){r._writableState.errorEmitted=true;process.nextTick(emitErrorAndCloseNT,r,e)}else{process.nextTick(emitCloseNT,r)}}else if(t){process.nextTick(emitCloseNT,r);t(e)}else{process.nextTick(emitCloseNT,r)}}));return this}function emitErrorAndCloseNT(e,t){emitErrorNT(e,t);emitCloseNT(e)}function emitCloseNT(e){if(e._writableState&&!e._writableState.emitClose)return;if(e._readableState&&!e._readableState.emitClose)return;e.emit("close")}function undestroy(){if(this._readableState){this._readableState.destroyed=false;this._readableState.reading=false;this._readableState.ended=false;this._readableState.endEmitted=false}if(this._writableState){this._writableState.destroyed=false;this._writableState.ended=false;this._writableState.ending=false;this._writableState.finalCalled=false;this._writableState.prefinished=false;this._writableState.finished=false;this._writableState.errorEmitted=false}}function emitErrorNT(e,t){e.emit("error",t)}function errorOrDestroy(e,t){var r=e._readableState;var i=e._writableState;if(r&&r.autoDestroy||i&&i.autoDestroy)e.destroy(t);else e.emit("error",t)}e.exports={destroy:destroy,undestroy:undestroy,errorOrDestroy:errorOrDestroy}},641:(e,t,r)=>{"use strict";var i=r(959).q.ERR_STREAM_PREMATURE_CLOSE;function once(e){var t=false;return function(){if(t)return;t=true;for(var r=arguments.length,i=new Array(r),n=0;n<r;n++){i[n]=arguments[n]}e.apply(this,i)}}function noop(){}function isRequest(e){return e.setHeader&&typeof e.abort==="function"}function eos(e,t,r){if(typeof t==="function")return eos(e,null,t);if(!t)t={};r=once(r||noop);var n=t.readable||t.readable!==false&&e.readable;var s=t.writable||t.writable!==false&&e.writable;var a=function onlegacyfinish(){if(!e.writable)f()};var o=e._writableState&&e._writableState.finished;var f=function onfinish(){s=false;o=true;if(!n)r.call(e)};var l=e._readableState&&e._readableState.endEmitted;var u=function onend(){n=false;l=true;if(!s)r.call(e)};var h=function onerror(t){r.call(e,t)};var d=function onclose(){var t;if(n&&!l){if(!e._readableState||!e._readableState.ended)t=new i;return r.call(e,t)}if(s&&!o){if(!e._writableState||!e._writableState.ended)t=new i;return r.call(e,t)}};var p=function onrequest(){e.req.on("finish",f)};if(isRequest(e)){e.on("complete",f);e.on("abort",d);if(e.req)p();else e.on("request",p)}else if(s&&!e._writableState){e.on("end",a);e.on("close",a)}e.on("end",u);e.on("finish",f);if(t.error!==false)e.on("error",h);e.on("close",d);return function(){e.removeListener("complete",f);e.removeListener("abort",d);e.removeListener("request",p);if(e.req)e.req.removeListener("finish",f);e.removeListener("end",a);e.removeListener("close",a);e.removeListener("finish",f);e.removeListener("end",u);e.removeListener("error",h);e.removeListener("close",d)}}e.exports=eos},668:(e,t,r)=>{"use strict";function asyncGeneratorStep(e,t,r,i,n,s,a){try{var o=e[s](a);var f=o.value}catch(e){r(e);return}if(o.done){t(f)}else{Promise.resolve(f).then(i,n)}}function _asyncToGenerator(e){return function(){var t=this,r=arguments;return new Promise((function(i,n){var s=e.apply(t,r);function _next(e){asyncGeneratorStep(s,i,n,_next,_throw,"next",e)}function _throw(e){asyncGeneratorStep(s,i,n,_next,_throw,"throw",e)}_next(undefined)}))}}function ownKeys(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);if(t)i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}));r.push.apply(r,i)}return r}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};if(t%2){ownKeys(Object(r),true).forEach((function(t){_defineProperty(e,t,r[t])}))}else if(Object.getOwnPropertyDescriptors){Object.defineProperties(e,Object.getOwnPropertyDescriptors(r))}else{ownKeys(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}}return e}function _defineProperty(e,t,r){if(t in e){Object.defineProperty(e,t,{value:r,enumerable:true,configurable:true,writable:true})}else{e[t]=r}return e}var i=r(959).q.ERR_INVALID_ARG_TYPE;function from(e,t,r){var n;if(t&&typeof t.next==="function"){n=t}else if(t&&t[Symbol.asyncIterator])n=t[Symbol.asyncIterator]();else if(t&&t[Symbol.iterator])n=t[Symbol.iterator]();else throw new i("iterable",["Iterable"],t);var s=new e(_objectSpread({objectMode:true},r));var a=false;s._read=function(){if(!a){a=true;next()}};function next(){return _next2.apply(this,arguments)}function _next2(){_next2=_asyncToGenerator((function*(){try{var e=yield n.next(),t=e.value,r=e.done;if(r){s.push(null)}else if(s.push(yield t)){next()}else{a=false}}catch(e){s.destroy(e)}}));return _next2.apply(this,arguments)}return s}e.exports=from},899:(e,t,r)=>{"use strict";var i;function once(e){var t=false;return function(){if(t)return;t=true;e.apply(void 0,arguments)}}var n=r(959).q,s=n.ERR_MISSING_ARGS,a=n.ERR_STREAM_DESTROYED;function noop(e){if(e)throw e}function isRequest(e){return e.setHeader&&typeof e.abort==="function"}function destroyer(e,t,n,s){s=once(s);var o=false;e.on("close",(function(){o=true}));if(i===undefined)i=r(641);i(e,{readable:t,writable:n},(function(e){if(e)return s(e);o=true;s()}));var f=false;return function(t){if(o)return;if(f)return;f=true;if(isRequest(e))return e.abort();if(typeof e.destroy==="function")return e.destroy();s(t||new a("pipe"))}}function call(e){e()}function pipe(e,t){return e.pipe(t)}function popCallback(e){if(!e.length)return noop;if(typeof e[e.length-1]!=="function")return noop;return e.pop()}function pipeline(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++){t[r]=arguments[r]}var i=popCallback(t);if(Array.isArray(t[0]))t=t[0];if(t.length<2){throw new s("streams")}var n;var a=t.map((function(e,r){var s=r<t.length-1;var o=r>0;return destroyer(e,s,o,(function(e){if(!n)n=e;if(e)a.forEach(call);if(s)return;a.forEach(call);i(n)}))}));return t.reduce(pipe)}e.exports=pipeline},841:(e,t,r)=>{"use strict";var i=r(959).q.ERR_INVALID_OPT_VALUE;function highWaterMarkFrom(e,t,r){return e.highWaterMark!=null?e.highWaterMark:t?e[r]:null}function getHighWaterMark(e,t,r,n){var s=highWaterMarkFrom(t,n,r);if(s!=null){if(!(isFinite(s)&&Math.floor(s)===s)||s<0){var a=n?r:"highWaterMark";throw new i(a,s)}return Math.floor(s)}return e.objectMode?16:16*1024}e.exports={getHighWaterMark:getHighWaterMark}},604:(e,t,r)=>{e.exports=r(781)},505:(e,t,r)=>{var i=r(781);if(process.env.READABLE_STREAM==="disable"&&i){e.exports=i.Readable;Object.assign(e.exports,i);e.exports.Stream=i}else{t=e.exports=r(852);t.Stream=i||t;t.Readable=t;t.Writable=r(379);t.Duplex=r(548);t.Transform=r(770);t.PassThrough=r(184);t.finished=r(641);t.pipeline=r(899)}},598:(e,t,r)=>{"use strict";const i=r(591);const n=r(580);e.exports=i((()=>{n((()=>{process.stderr.write("[?25h")}),{alwaysLast:true})}))},105:(e,t,r)=>{
/*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */
var i=r(300);var n=i.Buffer;function copyProps(e,t){for(var r in e){t[r]=e[r]}}if(n.from&&n.alloc&&n.allocUnsafe&&n.allocUnsafeSlow){e.exports=i}else{copyProps(i,t);t.Buffer=SafeBuffer}function SafeBuffer(e,t,r){return n(e,t,r)}SafeBuffer.prototype=Object.create(n.prototype);copyProps(n,SafeBuffer);SafeBuffer.from=function(e,t,r){if(typeof e==="number"){throw new TypeError("Argument must not be a number")}return n(e,t,r)};SafeBuffer.alloc=function(e,t,r){if(typeof e!=="number"){throw new TypeError("Argument must be a number")}var i=n(e);if(t!==undefined){if(typeof r==="string"){i.fill(t,r)}else{i.fill(t)}}else{i.fill(0)}return i};SafeBuffer.allocUnsafe=function(e){if(typeof e!=="number"){throw new TypeError("Argument must be a number")}return n(e)};SafeBuffer.allocUnsafeSlow=function(e){if(typeof e!=="number"){throw new TypeError("Argument must be a number")}return i.SlowBuffer(e)}},580:(e,t,r)=>{var i=global.process;const processOk=function(e){return e&&typeof e==="object"&&typeof e.removeListener==="function"&&typeof e.emit==="function"&&typeof e.reallyExit==="function"&&typeof e.listeners==="function"&&typeof e.kill==="function"&&typeof e.pid==="number"&&typeof e.on==="function"};if(!processOk(i)){e.exports=function(){return function(){}}}else{var n=r(491);var s=r(357);var a=/^win/i.test(i.platform);var o=r(361);if(typeof o!=="function"){o=o.EventEmitter}var f;if(i.__signal_exit_emitter__){f=i.__signal_exit_emitter__}else{f=i.__signal_exit_emitter__=new o;f.count=0;f.emitted={}}if(!f.infinite){f.setMaxListeners(Infinity);f.infinite=true}e.exports=function(e,t){if(!processOk(global.process)){return function(){}}n.equal(typeof e,"function","a callback must be provided for exit handler");if(d===false){p()}var r="exit";if(t&&t.alwaysLast){r="afterexit"}var remove=function(){f.removeListener(r,e);if(f.listeners("exit").length===0&&f.listeners("afterexit").length===0){l()}};f.on(r,e);return remove};var l=function unload(){if(!d||!processOk(global.process)){return}d=false;s.forEach((function(e){try{i.removeListener(e,h[e])}catch(e){}}));i.emit=g;i.reallyExit=_;f.count-=1};e.exports.unload=l;var u=function emit(e,t,r){if(f.emitted[e]){return}f.emitted[e]=true;f.emit(e,t,r)};var h={};s.forEach((function(e){h[e]=function listener(){if(!processOk(global.process)){return}var t=i.listeners(e);if(t.length===f.count){l();u("exit",null,e);u("afterexit",null,e);if(a&&e==="SIGHUP"){e="SIGINT"}i.kill(i.pid,e)}}}));e.exports.signals=function(){return s};var d=false;var p=function load(){if(d||!processOk(global.process)){return}d=true;f.count+=1;s=s.filter((function(e){try{i.on(e,h[e]);return true}catch(e){return false}}));i.emit=y;i.reallyExit=b};e.exports.load=p;var _=i.reallyExit;var b=function processReallyExit(e){if(!processOk(global.process)){return}i.exitCode=e||0;u("exit",i.exitCode,null);u("afterexit",i.exitCode,null);_.call(i,i.exitCode)};var g=i.emit;var y=function processEmit(e,t){if(e==="exit"&&processOk(global.process)){if(t!==undefined){i.exitCode=t}var r=g.apply(this,arguments);u("exit",i.exitCode,null);u("afterexit",i.exitCode,null);return r}else{return g.apply(this,arguments)}}}},357:e=>{e.exports=["SIGABRT","SIGALRM","SIGHUP","SIGINT","SIGTERM"];if(process.platform!=="win32"){e.exports.push("SIGVTALRM","SIGXCPU","SIGXFSZ","SIGUSR2","SIGTRAP","SIGSYS","SIGQUIT","SIGIOT")}if(process.platform==="linux"){e.exports.push("SIGIO","SIGPOLL","SIGPWR","SIGSTKFLT","SIGUNUSED")}},259:(e,t,r)=>{"use strict";var i=r(105).Buffer;var n=i.isEncoding||function(e){e=""+e;switch(e&&e.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return true;default:return false}};function _normalizeEncoding(e){if(!e)return"utf8";var t;while(true){switch(e){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return e;default:if(t)return;e=(""+e).toLowerCase();t=true}}}function normalizeEncoding(e){var t=_normalizeEncoding(e);if(typeof t!=="string"&&(i.isEncoding===n||!n(e)))throw new Error("Unknown encoding: "+e);return t||e}t.s=StringDecoder;function StringDecoder(e){this.encoding=normalizeEncoding(e);var t;switch(this.encoding){case"utf16le":this.text=utf16Text;this.end=utf16End;t=4;break;case"utf8":this.fillLast=utf8FillLast;t=4;break;case"base64":this.text=base64Text;this.end=base64End;t=3;break;default:this.write=simpleWrite;this.end=simpleEnd;return}this.lastNeed=0;this.lastTotal=0;this.lastChar=i.allocUnsafe(t)}StringDecoder.prototype.write=function(e){if(e.length===0)return"";var t;var r;if(this.lastNeed){t=this.fillLast(e);if(t===undefined)return"";r=this.lastNeed;this.lastNeed=0}else{r=0}if(r<e.length)return t?t+this.text(e,r):this.text(e,r);return t||""};StringDecoder.prototype.end=utf8End;StringDecoder.prototype.text=utf8Text;StringDecoder.prototype.fillLast=function(e){if(this.lastNeed<=e.length){e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed);return this.lastChar.toString(this.encoding,0,this.lastTotal)}e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,e.length);this.lastNeed-=e.length};function utf8CheckByte(e){if(e<=127)return 0;else if(e>>5===6)return 2;else if(e>>4===14)return 3;else if(e>>3===30)return 4;return e>>6===2?-1:-2}function utf8CheckIncomplete(e,t,r){var i=t.length-1;if(i<r)return 0;var n=utf8CheckByte(t[i]);if(n>=0){if(n>0)e.lastNeed=n-1;return n}if(--i<r||n===-2)return 0;n=utf8CheckByte(t[i]);if(n>=0){if(n>0)e.lastNeed=n-2;return n}if(--i<r||n===-2)return 0;n=utf8CheckByte(t[i]);if(n>=0){if(n>0){if(n===2)n=0;else e.lastNeed=n-3}return n}return 0}function utf8CheckExtraBytes(e,t,r){if((t[0]&192)!==128){e.lastNeed=0;return"�"}if(e.lastNeed>1&&t.length>1){if((t[1]&192)!==128){e.lastNeed=1;return"�"}if(e.lastNeed>2&&t.length>2){if((t[2]&192)!==128){e.lastNeed=2;return"�"}}}}function utf8FillLast(e){var t=this.lastTotal-this.lastNeed;var r=utf8CheckExtraBytes(this,e,t);if(r!==undefined)return r;if(this.lastNeed<=e.length){e.copy(this.lastChar,t,0,this.lastNeed);return this.lastChar.toString(this.encoding,0,this.lastTotal)}e.copy(this.lastChar,t,0,e.length);this.lastNeed-=e.length}function utf8Text(e,t){var r=utf8CheckIncomplete(this,e,t);if(!this.lastNeed)return e.toString("utf8",t);this.lastTotal=r;var i=e.length-(r-this.lastNeed);e.copy(this.lastChar,0,i);return e.toString("utf8",t,i)}function utf8End(e){var t=e&&e.length?this.write(e):"";if(this.lastNeed)return t+"�";return t}function utf16Text(e,t){if((e.length-t)%2===0){var r=e.toString("utf16le",t);if(r){var i=r.charCodeAt(r.length-1);if(i>=55296&&i<=56319){this.lastNeed=2;this.lastTotal=4;this.lastChar[0]=e[e.length-2];this.lastChar[1]=e[e.length-1];return r.slice(0,-1)}}return r}this.lastNeed=1;this.lastTotal=2;this.lastChar[0]=e[e.length-1];return e.toString("utf16le",t,e.length-1)}function utf16End(e){var t=e&&e.length?this.write(e):"";if(this.lastNeed){var r=this.lastTotal-this.lastNeed;return t+this.lastChar.toString("utf16le",0,r)}return t}function base64Text(e,t){var r=(e.length-t)%3;if(r===0)return e.toString("base64",t);this.lastNeed=3-r;this.lastTotal=3;if(r===1){this.lastChar[0]=e[e.length-1]}else{this.lastChar[0]=e[e.length-2];this.lastChar[1]=e[e.length-1]}return e.toString("base64",t,e.length-r)}function base64End(e){var t=e&&e.length?this.write(e):"";if(this.lastNeed)return t+this.lastChar.toString("base64",0,3-this.lastNeed);return t}function simpleWrite(e){return e.toString(this.encoding)}function simpleEnd(e){return e&&e.length?this.write(e):""}},125:(e,t,r)=>{e.exports=r(837).deprecate},70:e=>{e.exports=[[768,879],[1155,1158],[1160,1161],[1425,1469],[1471,1471],[1473,1474],[1476,1477],[1479,1479],[1536,1539],[1552,1557],[1611,1630],[1648,1648],[1750,1764],[1767,1768],[1770,1773],[1807,1807],[1809,1809],[1840,1866],[1958,1968],[2027,2035],[2305,2306],[2364,2364],[2369,2376],[2381,2381],[2385,2388],[2402,2403],[2433,2433],[2492,2492],[2497,2500],[2509,2509],[2530,2531],[2561,2562],[2620,2620],[2625,2626],[2631,2632],[2635,2637],[2672,2673],[2689,2690],[2748,2748],[2753,2757],[2759,2760],[2765,2765],[2786,2787],[2817,2817],[2876,2876],[2879,2879],[2881,2883],[2893,2893],[2902,2902],[2946,2946],[3008,3008],[3021,3021],[3134,3136],[3142,3144],[3146,3149],[3157,3158],[3260,3260],[3263,3263],[3270,3270],[3276,3277],[3298,3299],[3393,3395],[3405,3405],[3530,3530],[3538,3540],[3542,3542],[3633,3633],[3636,3642],[3655,3662],[3761,3761],[3764,3769],[3771,3772],[3784,3789],[3864,3865],[3893,3893],[3895,3895],[3897,3897],[3953,3966],[3968,3972],[3974,3975],[3984,3991],[3993,4028],[4038,4038],[4141,4144],[4146,4146],[4150,4151],[4153,4153],[4184,4185],[4448,4607],[4959,4959],[5906,5908],[5938,5940],[5970,5971],[6002,6003],[6068,6069],[6071,6077],[6086,6086],[6089,6099],[6109,6109],[6155,6157],[6313,6313],[6432,6434],[6439,6440],[6450,6450],[6457,6459],[6679,6680],[6912,6915],[6964,6964],[6966,6970],[6972,6972],[6978,6978],[7019,7027],[7616,7626],[7678,7679],[8203,8207],[8234,8238],[8288,8291],[8298,8303],[8400,8431],[12330,12335],[12441,12442],[43014,43014],[43019,43019],[43045,43046],[64286,64286],[65024,65039],[65056,65059],[65279,65279],[65529,65531],[68097,68099],[68101,68102],[68108,68111],[68152,68154],[68159,68159],[119143,119145],[119155,119170],[119173,119179],[119210,119213],[119362,119364],[917505,917505],[917536,917631],[917760,917999]]},74:(e,t,r)=>{"use strict";var i=r(906);var n=r(70);var s={nul:0,control:0};e.exports=function wcwidth(e){return wcswidth(e,s)};e.exports.config=function(e){e=i(e||{},s);return function wcwidth(t){return wcswidth(t,e)}};function wcswidth(e,t){if(typeof e!=="string")return wcwidth(e,t);var r=0;for(var i=0;i<e.length;i++){var n=wcwidth(e.charCodeAt(i),t);if(n<0)return-1;r+=n}return r}function wcwidth(e,t){if(e===0)return t.nul;if(e<32||e>=127&&e<160)return t.control;if(bisearch(e))return 0;return 1+(e>=4352&&(e<=4447||e==9001||e==9002||e>=11904&&e<=42191&&e!=12351||e>=44032&&e<=55203||e>=63744&&e<=64255||e>=65040&&e<=65049||e>=65072&&e<=65135||e>=65280&&e<=65376||e>=65504&&e<=65510||e>=131072&&e<=196605||e>=196608&&e<=262141))}function bisearch(e){var t=0;var r=n.length-1;var i;if(e<n[0][0]||e>n[r][1])return false;while(r>=t){i=Math.floor((t+r)/2);if(e>n[i][1])t=i+1;else if(e<n[i][0])r=i-1;else return true}return false}},234:e=>{"use strict";e.exports=require("../chalk")},286:e=>{"use strict";e.exports=require("../strip-ansi")},491:e=>{"use strict";e.exports=require("assert")},300:e=>{"use strict";e.exports=require("buffer")},361:e=>{"use strict";e.exports=require("events")},521:e=>{"use strict";e.exports=require("readline")},781:e=>{"use strict";e.exports=require("stream")},837:e=>{"use strict";e.exports=require("util")},32:e=>{"use strict";e.exports=JSON.parse('{"dots":{"interval":80,"frames":["⠋","⠙","⠹","⠸","⠼","⠴","⠦","⠧","⠇","⠏"]},"dots2":{"interval":80,"frames":["⣾","⣽","⣻","⢿","⡿","⣟","⣯","⣷"]},"dots3":{"interval":80,"frames":["⠋","⠙","⠚","⠞","⠖","⠦","⠴","⠲","⠳","⠓"]},"dots4":{"interval":80,"frames":["⠄","⠆","⠇","⠋","⠙","⠸","⠰","⠠","⠰","⠸","⠙","⠋","⠇","⠆"]},"dots5":{"interval":80,"frames":["⠋","⠙","⠚","⠒","⠂","⠂","⠒","⠲","⠴","⠦","⠖","⠒","⠐","⠐","⠒","⠓","⠋"]},"dots6":{"interval":80,"frames":["⠁","⠉","⠙","⠚","⠒","⠂","⠂","⠒","⠲","⠴","⠤","⠄","⠄","⠤","⠴","⠲","⠒","⠂","⠂","⠒","⠚","⠙","⠉","⠁"]},"dots7":{"interval":80,"frames":["⠈","⠉","⠋","⠓","⠒","⠐","⠐","⠒","⠖","⠦","⠤","⠠","⠠","⠤","⠦","⠖","⠒","⠐","⠐","⠒","⠓","⠋","⠉","⠈"]},"dots8":{"interval":80,"frames":["⠁","⠁","⠉","⠙","⠚","⠒","⠂","⠂","⠒","⠲","⠴","⠤","⠄","⠄","⠤","⠠","⠠","⠤","⠦","⠖","⠒","⠐","⠐","⠒","⠓","⠋","⠉","⠈","⠈"]},"dots9":{"interval":80,"frames":["⢹","⢺","⢼","⣸","⣇","⡧","⡗","⡏"]},"dots10":{"interval":80,"frames":["⢄","⢂","⢁","⡁","⡈","⡐","⡠"]},"dots11":{"interval":100,"frames":["⠁","⠂","⠄","⡀","⢀","⠠","⠐","⠈"]},"dots12":{"interval":80,"frames":["⢀⠀","⡀⠀","⠄⠀","⢂⠀","⡂⠀","⠅⠀","⢃⠀","⡃⠀","⠍⠀","⢋⠀","⡋⠀","⠍⠁","⢋⠁","⡋⠁","⠍⠉","⠋⠉","⠋⠉","⠉⠙","⠉⠙","⠉⠩","⠈⢙","⠈⡙","⢈⠩","⡀⢙","⠄⡙","⢂⠩","⡂⢘","⠅⡘","⢃⠨","⡃⢐","⠍⡐","⢋⠠","⡋⢀","⠍⡁","⢋⠁","⡋⠁","⠍⠉","⠋⠉","⠋⠉","⠉⠙","⠉⠙","⠉⠩","⠈⢙","⠈⡙","⠈⠩","⠀⢙","⠀⡙","⠀⠩","⠀⢘","⠀⡘","⠀⠨","⠀⢐","⠀⡐","⠀⠠","⠀⢀","⠀⡀"]},"dots8Bit":{"interval":80,"frames":["⠀","⠁","⠂","⠃","⠄","⠅","⠆","⠇","⡀","⡁","⡂","⡃","⡄","⡅","⡆","⡇","⠈","⠉","⠊","⠋","⠌","⠍","⠎","⠏","⡈","⡉","⡊","⡋","⡌","⡍","⡎","⡏","⠐","⠑","⠒","⠓","⠔","⠕","⠖","⠗","⡐","⡑","⡒","⡓","⡔","⡕","⡖","⡗","⠘","⠙","⠚","⠛","⠜","⠝","⠞","⠟","⡘","⡙","⡚","⡛","⡜","⡝","⡞","⡟","⠠","⠡","⠢","⠣","⠤","⠥","⠦","⠧","⡠","⡡","⡢","⡣","⡤","⡥","⡦","⡧","⠨","⠩","⠪","⠫","⠬","⠭","⠮","⠯","⡨","⡩","⡪","⡫","⡬","⡭","⡮","⡯","⠰","⠱","⠲","⠳","⠴","⠵","⠶","⠷","⡰","⡱","⡲","⡳","⡴","⡵","⡶","⡷","⠸","⠹","⠺","⠻","⠼","⠽","⠾","⠿","⡸","⡹","⡺","⡻","⡼","⡽","⡾","⡿","⢀","⢁","⢂","⢃","⢄","⢅","⢆","⢇","⣀","⣁","⣂","⣃","⣄","⣅","⣆","⣇","⢈","⢉","⢊","⢋","⢌","⢍","⢎","⢏","⣈","⣉","⣊","⣋","⣌","⣍","⣎","⣏","⢐","⢑","⢒","⢓","⢔","⢕","⢖","⢗","⣐","⣑","⣒","⣓","⣔","⣕","⣖","⣗","⢘","⢙","⢚","⢛","⢜","⢝","⢞","⢟","⣘","⣙","⣚","⣛","⣜","⣝","⣞","⣟","⢠","⢡","⢢","⢣","⢤","⢥","⢦","⢧","⣠","⣡","⣢","⣣","⣤","⣥","⣦","⣧","⢨","⢩","⢪","⢫","⢬","⢭","⢮","⢯","⣨","⣩","⣪","⣫","⣬","⣭","⣮","⣯","⢰","⢱","⢲","⢳","⢴","⢵","⢶","⢷","⣰","⣱","⣲","⣳","⣴","⣵","⣶","⣷","⢸","⢹","⢺","⢻","⢼","⢽","⢾","⢿","⣸","⣹","⣺","⣻","⣼","⣽","⣾","⣿"]},"line":{"interval":130,"frames":["-","\\\\","|","/"]},"line2":{"interval":100,"frames":["⠂","-","–","—","–","-"]},"pipe":{"interval":100,"frames":["┤","┘","┴","└","├","┌","┬","┐"]},"simpleDots":{"interval":400,"frames":[".  ",".. ","...","   "]},"simpleDotsScrolling":{"interval":200,"frames":[".  ",".. ","..."," ..","  .","   "]},"star":{"interval":70,"frames":["✶","✸","✹","✺","✹","✷"]},"star2":{"interval":80,"frames":["+","x","*"]},"flip":{"interval":70,"frames":["_","_","_","-","`","`","\'","´","-","_","_","_"]},"hamburger":{"interval":100,"frames":["☱","☲","☴"]},"growVertical":{"interval":120,"frames":["▁","▃","▄","▅","▆","▇","▆","▅","▄","▃"]},"growHorizontal":{"interval":120,"frames":["▏","▎","▍","▌","▋","▊","▉","▊","▋","▌","▍","▎"]},"balloon":{"interval":140,"frames":[" ",".","o","O","@","*"," "]},"balloon2":{"interval":120,"frames":[".","o","O","°","O","o","."]},"noise":{"interval":100,"frames":["▓","▒","░"]},"bounce":{"interval":120,"frames":["⠁","⠂","⠄","⠂"]},"boxBounce":{"interval":120,"frames":["▖","▘","▝","▗"]},"boxBounce2":{"interval":100,"frames":["▌","▀","▐","▄"]},"triangle":{"interval":50,"frames":["◢","◣","◤","◥"]},"arc":{"interval":100,"frames":["◜","◠","◝","◞","◡","◟"]},"circle":{"interval":120,"frames":["◡","⊙","◠"]},"squareCorners":{"interval":180,"frames":["◰","◳","◲","◱"]},"circleQuarters":{"interval":120,"frames":["◴","◷","◶","◵"]},"circleHalves":{"interval":50,"frames":["◐","◓","◑","◒"]},"squish":{"interval":100,"frames":["╫","╪"]},"toggle":{"interval":250,"frames":["⊶","⊷"]},"toggle2":{"interval":80,"frames":["▫","▪"]},"toggle3":{"interval":120,"frames":["□","■"]},"toggle4":{"interval":100,"frames":["■","□","▪","▫"]},"toggle5":{"interval":100,"frames":["▮","▯"]},"toggle6":{"interval":300,"frames":["ဝ","၀"]},"toggle7":{"interval":80,"frames":["⦾","⦿"]},"toggle8":{"interval":100,"frames":["◍","◌"]},"toggle9":{"interval":100,"frames":["◉","◎"]},"toggle10":{"interval":100,"frames":["㊂","㊀","㊁"]},"toggle11":{"interval":50,"frames":["⧇","⧆"]},"toggle12":{"interval":120,"frames":["☗","☖"]},"toggle13":{"interval":80,"frames":["=","*","-"]},"arrow":{"interval":100,"frames":["←","↖","↑","↗","→","↘","↓","↙"]},"arrow2":{"interval":80,"frames":["⬆️ ","↗️ ","➡️ ","↘️ ","⬇️ ","↙️ ","⬅️ ","↖️ "]},"arrow3":{"interval":120,"frames":["▹▹▹▹▹","▸▹▹▹▹","▹▸▹▹▹","▹▹▸▹▹","▹▹▹▸▹","▹▹▹▹▸"]},"bouncingBar":{"interval":80,"frames":["[    ]","[=   ]","[==  ]","[=== ]","[ ===]","[  ==]","[   =]","[    ]","[   =]","[  ==]","[ ===]","[====]","[=== ]","[==  ]","[=   ]"]},"bouncingBall":{"interval":80,"frames":["( ●    )","(  ●   )","(   ●  )","(    ● )","(     ●)","(    ● )","(   ●  )","(  ●   )","( ●    )","(●     )"]},"smiley":{"interval":200,"frames":["😄 ","😝 "]},"monkey":{"interval":300,"frames":["🙈 ","🙈 ","🙉 ","🙊 "]},"hearts":{"interval":100,"frames":["💛 ","💙 ","💜 ","💚 ","❤️ "]},"clock":{"interval":100,"frames":["🕛 ","🕐 ","🕑 ","🕒 ","🕓 ","🕔 ","🕕 ","🕖 ","🕗 ","🕘 ","🕙 ","🕚 "]},"earth":{"interval":180,"frames":["🌍 ","🌎 ","🌏 "]},"material":{"interval":17,"frames":["█▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁","██▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁","███▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁","████▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁","██████▁▁▁▁▁▁▁▁▁▁▁▁▁▁","██████▁▁▁▁▁▁▁▁▁▁▁▁▁▁","███████▁▁▁▁▁▁▁▁▁▁▁▁▁","████████▁▁▁▁▁▁▁▁▁▁▁▁","█████████▁▁▁▁▁▁▁▁▁▁▁","█████████▁▁▁▁▁▁▁▁▁▁▁","██████████▁▁▁▁▁▁▁▁▁▁","███████████▁▁▁▁▁▁▁▁▁","█████████████▁▁▁▁▁▁▁","██████████████▁▁▁▁▁▁","██████████████▁▁▁▁▁▁","▁██████████████▁▁▁▁▁","▁██████████████▁▁▁▁▁","▁██████████████▁▁▁▁▁","▁▁██████████████▁▁▁▁","▁▁▁██████████████▁▁▁","▁▁▁▁█████████████▁▁▁","▁▁▁▁██████████████▁▁","▁▁▁▁██████████████▁▁","▁▁▁▁▁██████████████▁","▁▁▁▁▁██████████████▁","▁▁▁▁▁██████████████▁","▁▁▁▁▁▁██████████████","▁▁▁▁▁▁██████████████","▁▁▁▁▁▁▁█████████████","▁▁▁▁▁▁▁█████████████","▁▁▁▁▁▁▁▁████████████","▁▁▁▁▁▁▁▁████████████","▁▁▁▁▁▁▁▁▁███████████","▁▁▁▁▁▁▁▁▁███████████","▁▁▁▁▁▁▁▁▁▁██████████","▁▁▁▁▁▁▁▁▁▁██████████","▁▁▁▁▁▁▁▁▁▁▁▁████████","▁▁▁▁▁▁▁▁▁▁▁▁▁███████","▁▁▁▁▁▁▁▁▁▁▁▁▁▁██████","▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁█████","▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁█████","█▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁████","██▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁███","██▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁███","███▁▁▁▁▁▁▁▁▁▁▁▁▁▁███","████▁▁▁▁▁▁▁▁▁▁▁▁▁▁██","█████▁▁▁▁▁▁▁▁▁▁▁▁▁▁█","█████▁▁▁▁▁▁▁▁▁▁▁▁▁▁█","██████▁▁▁▁▁▁▁▁▁▁▁▁▁█","████████▁▁▁▁▁▁▁▁▁▁▁▁","█████████▁▁▁▁▁▁▁▁▁▁▁","█████████▁▁▁▁▁▁▁▁▁▁▁","█████████▁▁▁▁▁▁▁▁▁▁▁","█████████▁▁▁▁▁▁▁▁▁▁▁","███████████▁▁▁▁▁▁▁▁▁","████████████▁▁▁▁▁▁▁▁","████████████▁▁▁▁▁▁▁▁","██████████████▁▁▁▁▁▁","██████████████▁▁▁▁▁▁","▁██████████████▁▁▁▁▁","▁██████████████▁▁▁▁▁","▁▁▁█████████████▁▁▁▁","▁▁▁▁▁████████████▁▁▁","▁▁▁▁▁████████████▁▁▁","▁▁▁▁▁▁███████████▁▁▁","▁▁▁▁▁▁▁▁█████████▁▁▁","▁▁▁▁▁▁▁▁█████████▁▁▁","▁▁▁▁▁▁▁▁▁█████████▁▁","▁▁▁▁▁▁▁▁▁█████████▁▁","▁▁▁▁▁▁▁▁▁▁█████████▁","▁▁▁▁▁▁▁▁▁▁▁████████▁","▁▁▁▁▁▁▁▁▁▁▁████████▁","▁▁▁▁▁▁▁▁▁▁▁▁███████▁","▁▁▁▁▁▁▁▁▁▁▁▁███████▁","▁▁▁▁▁▁▁▁▁▁▁▁▁███████","▁▁▁▁▁▁▁▁▁▁▁▁▁███████","▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁█████","▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁████","▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁████","▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁████","▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁███","▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁███","▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁██","▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁██","▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁██","▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁█","▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁█","▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁█","▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁","▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁","▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁","▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁"]},"moon":{"interval":80,"frames":["🌑 ","🌒 ","🌓 ","🌔 ","🌕 ","🌖 ","🌗 ","🌘 "]},"runner":{"interval":140,"frames":["🚶 ","🏃 "]},"pong":{"interval":80,"frames":["▐⠂       ▌","▐⠈       ▌","▐ ⠂      ▌","▐ ⠠      ▌","▐  ⡀     ▌","▐  ⠠     ▌","▐   ⠂    ▌","▐   ⠈    ▌","▐    ⠂   ▌","▐    ⠠   ▌","▐     ⡀  ▌","▐     ⠠  ▌","▐      ⠂ ▌","▐      ⠈ ▌","▐       ⠂▌","▐       ⠠▌","▐       ⡀▌","▐      ⠠ ▌","▐      ⠂ ▌","▐     ⠈  ▌","▐     ⠂  ▌","▐    ⠠   ▌","▐    ⡀   ▌","▐   ⠠    ▌","▐   ⠂    ▌","▐  ⠈     ▌","▐  ⠂     ▌","▐ ⠠      ▌","▐ ⡀      ▌","▐⠠       ▌"]},"shark":{"interval":120,"frames":["▐|\\\\____________▌","▐_|\\\\___________▌","▐__|\\\\__________▌","▐___|\\\\_________▌","▐____|\\\\________▌","▐_____|\\\\_______▌","▐______|\\\\______▌","▐_______|\\\\_____▌","▐________|\\\\____▌","▐_________|\\\\___▌","▐__________|\\\\__▌","▐___________|\\\\_▌","▐____________|\\\\▌","▐____________/|▌","▐___________/|_▌","▐__________/|__▌","▐_________/|___▌","▐________/|____▌","▐_______/|_____▌","▐______/|______▌","▐_____/|_______▌","▐____/|________▌","▐___/|_________▌","▐__/|__________▌","▐_/|___________▌","▐/|____________▌"]},"dqpb":{"interval":100,"frames":["d","q","p","b"]},"weather":{"interval":100,"frames":["☀️ ","☀️ ","☀️ ","🌤 ","⛅️ ","🌥 ","☁️ ","🌧 ","🌨 ","🌧 ","🌨 ","🌧 ","🌨 ","⛈ ","🌨 ","🌧 ","🌨 ","☁️ ","🌥 ","⛅️ ","🌤 ","☀️ ","☀️ "]},"christmas":{"interval":400,"frames":["🌲","🎄"]},"grenade":{"interval":80,"frames":["،  ","′  "," ´ "," ‾ ","  ⸌","  ⸊","  |","  ⁎","  ⁕"," ෴ ","  ⁓","   ","   ","   "]},"point":{"interval":125,"frames":["∙∙∙","●∙∙","∙●∙","∙∙●","∙∙∙"]},"layer":{"interval":150,"frames":["-","=","≡"]},"betaWave":{"interval":80,"frames":["ρββββββ","βρβββββ","ββρββββ","βββρβββ","ββββρββ","βββββρβ","ββββββρ"]},"fingerDance":{"interval":160,"frames":["🤘 ","🤟 ","🖖 ","✋ ","🤚 ","👆 "]},"fistBump":{"interval":80,"frames":["🤜　　　　🤛 ","🤜　　　　🤛 ","🤜　　　　🤛 ","　🤜　　🤛　 ","　　🤜🤛　　 ","　🤜✨🤛　　 ","🤜　✨　🤛　 "]},"soccerHeader":{"interval":80,"frames":[" 🧑⚽️       🧑 ","🧑  ⚽️      🧑 ","🧑   ⚽️     🧑 ","🧑    ⚽️    🧑 ","🧑     ⚽️   🧑 ","🧑      ⚽️  🧑 ","🧑       ⚽️🧑  ","🧑      ⚽️  🧑 ","🧑     ⚽️   🧑 ","🧑    ⚽️    🧑 ","🧑   ⚽️     🧑 ","🧑  ⚽️      🧑 "]},"mindblown":{"interval":160,"frames":["😐 ","😐 ","😮 ","😮 ","😦 ","😦 ","😧 ","😧 ","🤯 ","💥 ","✨ ","　 ","　 ","　 "]},"speaker":{"interval":160,"frames":["🔈 ","🔉 ","🔊 ","🔉 "]},"orangePulse":{"interval":100,"frames":["🔸 ","🔶 ","🟠 ","🟠 ","🔶 "]},"bluePulse":{"interval":100,"frames":["🔹 ","🔷 ","🔵 ","🔵 ","🔷 "]},"orangeBluePulse":{"interval":100,"frames":["🔸 ","🔶 ","🟠 ","🟠 ","🔶 ","🔹 ","🔷 ","🔵 ","🔵 ","🔷 "]},"timeTravel":{"interval":100,"frames":["🕛 ","🕚 ","🕙 ","🕘 ","🕗 ","🕖 ","🕕 ","🕔 ","🕓 ","🕒 ","🕑 ","🕐 "]},"aesthetic":{"interval":80,"frames":["▰▱▱▱▱▱▱","▰▰▱▱▱▱▱","▰▰▰▱▱▱▱","▰▰▰▰▱▱▱","▰▰▰▰▰▱▱","▰▰▰▰▰▰▱","▰▰▰▰▰▰▰","▰▱▱▱▱▱▱"]}}')}};var t={};function __nccwpck_require__(r){var i=t[r];if(i!==undefined){return i.exports}var n=t[r]={exports:{}};var s=true;try{e[r](n,n.exports,__nccwpck_require__);s=false}finally{if(s)delete t[r]}return n.exports}if(typeof __nccwpck_require__!=="undefined")__nccwpck_require__.ab=__dirname+"/";var r=__nccwpck_require__(942);module.exports=r})();