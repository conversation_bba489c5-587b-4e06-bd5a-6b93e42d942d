(()=>{var t={337:t=>{"use strict";t.exports=(...t)=>[...new Set([].concat(...t))]},352:(t,e,r)=>{"use strict";const n=r(17);const s=r(223);const getExtensions=t=>t.length>1?`{${t.join(",")}}`:t[0];const getPath=(t,e)=>{const r=t[0]==="!"?t.slice(1):t;return n.isAbsolute(r)?r:n.join(e,r)};const addExtensions=(t,e)=>{if(n.extname(t)){return`**/${t}`}return`**/${t}.${getExtensions(e)}`};const getGlob=(t,e)=>{if(e.files&&!Array.isArray(e.files)){throw new TypeError(`Expected \`files\` to be of type \`Array\` but received type \`${typeof e.files}\``)}if(e.extensions&&!Array.isArray(e.extensions)){throw new TypeError(`Expected \`extensions\` to be of type \`Array\` but received type \`${typeof e.extensions}\``)}if(e.files&&e.extensions){return e.files.map((r=>n.posix.join(t,addExtensions(r,e.extensions))))}if(e.files){return e.files.map((e=>n.posix.join(t,`**/${e}`)))}if(e.extensions){return[n.posix.join(t,`**/*.${getExtensions(e.extensions)}`)]}return[n.posix.join(t,"**")]};t.exports=async(t,e)=>{e={cwd:process.cwd(),...e};if(typeof e.cwd!=="string"){throw new TypeError(`Expected \`cwd\` to be of type \`string\` but received type \`${typeof e.cwd}\``)}const r=await Promise.all([].concat(t).map((async t=>{const r=await s.isDirectory(getPath(t,e.cwd));return r?getGlob(t,e):t})));return[].concat.apply([],r)};t.exports.sync=(t,e)=>{e={cwd:process.cwd(),...e};if(typeof e.cwd!=="string"){throw new TypeError(`Expected \`cwd\` to be of type \`string\` but received type \`${typeof e.cwd}\``)}const r=[].concat(t).map((t=>s.isDirectorySync(getPath(t,e.cwd))?getGlob(t,e):t));return[].concat.apply([],r)}},153:(t,e,r)=>{"use strict";const{promisify:n}=r(837);const s=r(147);const o=r(17);const i=r(592);const c=r(302);const a=r(398);const p=["**/node_modules/**","**/flow-typed/**","**/coverage/**","**/.git"];const u=n(s.readFile);const mapGitIgnorePatternTo=t=>e=>{if(e.startsWith("!")){return"!"+o.posix.join(t,e.slice(1))}return o.posix.join(t,e)};const parseGitIgnore=(t,e)=>{const r=a(o.relative(e.cwd,o.dirname(e.fileName)));return t.split(/\r?\n/).filter(Boolean).filter((t=>!t.startsWith("#"))).map(mapGitIgnorePatternTo(r))};const reduceIgnore=t=>{const e=c();for(const r of t){e.add(parseGitIgnore(r.content,{cwd:r.cwd,fileName:r.filePath}))}return e};const ensureAbsolutePathForCwd=(t,e)=>{t=a(t);if(o.isAbsolute(e)){if(a(e).startsWith(t)){return e}throw new Error(`Path ${e} is not in cwd ${t}`)}return o.join(t,e)};const getIsIgnoredPredecate=(t,e)=>r=>t.ignores(a(o.relative(e,ensureAbsolutePathForCwd(e,r.path||r))));const getFile=async(t,e)=>{const r=o.join(e,t);const n=await u(r,"utf8");return{cwd:e,filePath:r,content:n}};const getFileSync=(t,e)=>{const r=o.join(e,t);const n=s.readFileSync(r,"utf8");return{cwd:e,filePath:r,content:n}};const normalizeOptions=({ignore:t=[],cwd:e=a(process.cwd())}={})=>({ignore:t,cwd:e});t.exports=async t=>{t=normalizeOptions(t);const e=await i("**/.gitignore",{ignore:p.concat(t.ignore),cwd:t.cwd});const r=await Promise.all(e.map((e=>getFile(e,t.cwd))));const n=reduceIgnore(r);return getIsIgnoredPredecate(n,t.cwd)};t.exports.sync=t=>{t=normalizeOptions(t);const e=i.sync("**/.gitignore",{ignore:p.concat(t.ignore),cwd:t.cwd});const r=e.map((e=>getFileSync(e,t.cwd)));const n=reduceIgnore(r);return getIsIgnoredPredecate(n,t.cwd)}},718:(t,e,r)=>{"use strict";const n=r(147);const s=r(337);const o=r(382);const i=r(592);const c=r(352);const a=r(153);const{FilterStream:p,UniqueStream:u}=r(546);const DEFAULT_FILTER=()=>false;const isNegative=t=>t[0]==="!";const assertPatternsInput=t=>{if(!t.every((t=>typeof t==="string"))){throw new TypeError("Patterns must be a string or an array of strings")}};const checkCwdOption=(t={})=>{if(!t.cwd){return}let e;try{e=n.statSync(t.cwd)}catch{return}if(!e.isDirectory()){throw new Error("The `cwd` option must be a path to a directory")}};const getPathString=t=>t.stats instanceof n.Stats?t.path:t;const generateGlobTasks=(t,e)=>{t=s([].concat(t));assertPatternsInput(t);checkCwdOption(e);const r=[];e={ignore:[],expandDirectories:true,...e};for(const[n,s]of t.entries()){if(isNegative(s)){continue}const o=t.slice(n).filter((t=>isNegative(t))).map((t=>t.slice(1)));const i={...e,ignore:e.ignore.concat(o)};r.push({pattern:s,options:i})}return r};const globDirs=(t,e)=>{let r={};if(t.options.cwd){r.cwd=t.options.cwd}if(Array.isArray(t.options.expandDirectories)){r={...r,files:t.options.expandDirectories}}else if(typeof t.options.expandDirectories==="object"){r={...r,...t.options.expandDirectories}}return e(t.pattern,r)};const getPattern=(t,e)=>t.options.expandDirectories?globDirs(t,e):[t.pattern];const getFilterSync=t=>t&&t.gitignore?a.sync({cwd:t.cwd,ignore:t.ignore}):DEFAULT_FILTER;const globToTask=t=>e=>{const{options:r}=t;if(r.ignore&&Array.isArray(r.ignore)&&r.expandDirectories){r.ignore=c.sync(r.ignore)}return{pattern:e,options:r}};t.exports=async(t,e)=>{const r=generateGlobTasks(t,e);const getFilter=async()=>e&&e.gitignore?a({cwd:e.cwd,ignore:e.ignore}):DEFAULT_FILTER;const getTasks=async()=>{const t=await Promise.all(r.map((async t=>{const e=await getPattern(t,c);return Promise.all(e.map(globToTask(t)))})));return s(...t)};const[n,o]=await Promise.all([getFilter(),getTasks()]);const p=await Promise.all(o.map((t=>i(t.pattern,t.options))));return s(...p).filter((t=>!n(getPathString(t))))};t.exports.sync=(t,e)=>{const r=generateGlobTasks(t,e);const n=[];for(const t of r){const e=getPattern(t,c.sync).map(globToTask(t));n.push(...e)}const o=getFilterSync(e);let a=[];for(const t of n){a=s(a,i.sync(t.pattern,t.options))}return a.filter((t=>!o(t)))};t.exports.stream=(t,e)=>{const r=generateGlobTasks(t,e);const n=[];for(const t of r){const e=getPattern(t,c.sync).map(globToTask(t));n.push(...e)}const s=getFilterSync(e);const a=new p((t=>!s(t)));const l=new u;return o(n.map((t=>i.stream(t.pattern,t.options)))).pipe(a).pipe(l)};t.exports.generateGlobTasks=generateGlobTasks;t.exports.hasMagic=(t,e)=>[].concat(t).some((t=>i.isDynamicPattern(t,e)));t.exports.gitignore=a},546:(t,e,r)=>{"use strict";const{Transform:n}=r(781);class ObjectTransform extends n{constructor(){super({objectMode:true})}}class FilterStream extends ObjectTransform{constructor(t){super();this._filter=t}_transform(t,e,r){if(this._filter(t)){this.push(t)}r()}}class UniqueStream extends ObjectTransform{constructor(){super();this._pushed=new Set}_transform(t,e,r){if(!this._pushed.has(t)){this.push(t);this._pushed.add(t)}r()}}t.exports={FilterStream:FilterStream,UniqueStream:UniqueStream}},302:t=>{function makeArray(t){return Array.isArray(t)?t:[t]}const e="";const r=" ";const n="\\";const s=/^\s+$/;const o=/^\\!/;const i=/^\\#/;const c=/\r?\n/g;const a=/^\.*\/|^\.+$/;const p="/";const u=typeof Symbol!=="undefined"?Symbol.for("node-ignore"):"node-ignore";const define=(t,e,r)=>Object.defineProperty(t,e,{value:r});const l=/([0-z])-([0-z])/g;const RETURN_FALSE=()=>false;const sanitizeRange=t=>t.replace(l,((t,r,n)=>r.charCodeAt(0)<=n.charCodeAt(0)?t:e));const cleanRangeBackSlash=t=>{const{length:e}=t;return t.slice(0,e-e%2)};const f=[[/\\?\s+$/,t=>t.indexOf("\\")===0?r:e],[/\\\s/g,()=>r],[/[\\$.|*+(){^]/g,t=>`\\${t}`],[/(?!\\)\?/g,()=>"[^/]"],[/^\//,()=>"^"],[/\//g,()=>"\\/"],[/^\^*\\\*\\\*\\\//,()=>"^(?:.*\\/)?"],[/^(?=[^^])/,function startingReplacer(){return!/\/(?!$)/.test(this)?"(?:^|\\/)":"^"}],[/\\\/\\\*\\\*(?=\\\/|$)/g,(t,e,r)=>e+6<r.length?"(?:\\/[^\\/]+)*":"\\/.+"],[/(^|[^\\]+)\\\*(?=.+)/g,(t,e)=>`${e}[^\\/]*`],[/\\\\\\(?=[$.|*+(){^])/g,()=>n],[/\\\\/g,()=>n],[/(\\)?\[([^\]/]*?)(\\*)($|\])/g,(t,e,r,s,o)=>e===n?`\\[${r}${cleanRangeBackSlash(s)}${o}`:o==="]"?s.length%2===0?`[${sanitizeRange(r)}${s}]`:"[]":"[]"],[/(?:[^*])$/,t=>/\/$/.test(t)?`${t}$`:`${t}(?=$|\\/$)`],[/(\^|\\\/)?\\\*$/,(t,e)=>{const r=e?`${e}[^/]+`:"[^/]*";return`${r}(?=$|\\/$)`}]];const d=Object.create(null);const makeRegex=(t,e)=>{let r=d[t];if(!r){r=f.reduce(((e,r)=>e.replace(r[0],r[1].bind(t))),t);d[t]=r}return e?new RegExp(r,"i"):new RegExp(r)};const isString=t=>typeof t==="string";const checkPattern=t=>t&&isString(t)&&!s.test(t)&&t.indexOf("#")!==0;const splitPattern=t=>t.split(c);class IgnoreRule{constructor(t,e,r,n){this.origin=t;this.pattern=e;this.negative=r;this.regex=n}}const createRule=(t,e)=>{const r=t;let n=false;if(t.indexOf("!")===0){n=true;t=t.substr(1)}t=t.replace(o,"!").replace(i,"#");const s=makeRegex(t,e);return new IgnoreRule(r,t,n,s)};const throwError=(t,e)=>{throw new e(t)};const checkPath=(t,e,r)=>{if(!isString(t)){return r(`path must be a string, but got \`${e}\``,TypeError)}if(!t){return r(`path must not be empty`,TypeError)}if(checkPath.isNotRelative(t)){const t="`path.relative()`d";return r(`path should be a ${t} string, but got "${e}"`,RangeError)}return true};const isNotRelative=t=>a.test(t);checkPath.isNotRelative=isNotRelative;checkPath.convert=t=>t;class Ignore{constructor({ignorecase:t=true,ignoreCase:e=t,allowRelativePaths:r=false}={}){define(this,u,true);this._rules=[];this._ignoreCase=e;this._allowRelativePaths=r;this._initCache()}_initCache(){this._ignoreCache=Object.create(null);this._testCache=Object.create(null)}_addPattern(t){if(t&&t[u]){this._rules=this._rules.concat(t._rules);this._added=true;return}if(checkPattern(t)){const e=createRule(t,this._ignoreCase);this._added=true;this._rules.push(e)}}add(t){this._added=false;makeArray(isString(t)?splitPattern(t):t).forEach(this._addPattern,this);if(this._added){this._initCache()}return this}addPattern(t){return this.add(t)}_testOne(t,e){let r=false;let n=false;this._rules.forEach((s=>{const{negative:o}=s;if(n===o&&r!==n||o&&!r&&!n&&!e){return}const i=s.regex.test(t);if(i){r=!o;n=o}}));return{ignored:r,unignored:n}}_test(t,e,r,n){const s=t&&checkPath.convert(t);checkPath(s,t,this._allowRelativePaths?RETURN_FALSE:throwError);return this._t(s,e,r,n)}_t(t,e,r,n){if(t in e){return e[t]}if(!n){n=t.split(p)}n.pop();if(!n.length){return e[t]=this._testOne(t,r)}const s=this._t(n.join(p)+p,e,r,n);return e[t]=s.ignored?s:this._testOne(t,r)}ignores(t){return this._test(t,this._ignoreCache,false).ignored}createFilter(){return t=>!this.ignores(t)}filter(t){return makeArray(t).filter(this.createFilter())}test(t){return this._test(t,this._testCache,true)}}const factory=t=>new Ignore(t);const isPathValid=t=>checkPath(t&&checkPath.convert(t),t,RETURN_FALSE);factory.isPathValid=isPathValid;factory.default=factory;t.exports=factory;if(typeof process!=="undefined"&&(process.env&&process.env.IGNORE_TEST_WIN32||process.platform==="win32")){const makePosix=t=>/^\\\\\?\\/.test(t)||/["<>|\u0000-\u001F]+/u.test(t)?t:t.replace(/\\/g,"/");checkPath.convert=makePosix;const t=/^[a-z]:\//i;checkPath.isNotRelative=e=>t.test(e)||isNotRelative(e)}},382:(t,e,r)=>{"use strict";const n=r(781);const s=n.PassThrough;const o=Array.prototype.slice;t.exports=merge2;function merge2(){const t=[];const e=o.call(arguments);let r=false;let n=e[e.length-1];if(n&&!Array.isArray(n)&&n.pipe==null){e.pop()}else{n={}}const i=n.end!==false;const c=n.pipeError===true;if(n.objectMode==null){n.objectMode=true}if(n.highWaterMark==null){n.highWaterMark=64*1024}const a=s(n);function addStream(){for(let e=0,r=arguments.length;e<r;e++){t.push(pauseStreams(arguments[e],n))}mergeStream();return this}function mergeStream(){if(r){return}r=true;let e=t.shift();if(!e){process.nextTick(endStream);return}if(!Array.isArray(e)){e=[e]}let n=e.length+1;function next(){if(--n>0){return}r=false;mergeStream()}function pipe(t){function onend(){t.removeListener("merge2UnpipeEnd",onend);t.removeListener("end",onend);if(c){t.removeListener("error",onerror)}next()}function onerror(t){a.emit("error",t)}if(t._readableState.endEmitted){return next()}t.on("merge2UnpipeEnd",onend);t.on("end",onend);if(c){t.on("error",onerror)}t.pipe(a,{end:false});t.resume()}for(let t=0;t<e.length;t++){pipe(e[t])}next()}function endStream(){r=false;a.emit("queueDrain");if(i){a.end()}}a.setMaxListeners(0);a.add=addStream;a.on("unpipe",(function(t){t.emit("merge2UnpipeEnd")}));if(e.length){addStream.apply(null,e)}return a}function pauseStreams(t,e){if(!Array.isArray(t)){if(!t._readableState&&t.pipe){t=t.pipe(s(e))}if(!t._readableState||!t.pause||!t.pipe){throw new Error("Only readable stream can be merged.")}t.pause()}else{for(let r=0,n=t.length;r<n;r++){t[r]=pauseStreams(t[r],e)}}return t}},223:(t,e,r)=>{"use strict";const{promisify:n}=r(837);const s=r(147);async function isType(t,e,r){if(typeof r!=="string"){throw new TypeError(`Expected a string, got ${typeof r}`)}try{const o=await n(s[t])(r);return o[e]()}catch(t){if(t.code==="ENOENT"){return false}throw t}}function isTypeSync(t,e,r){if(typeof r!=="string"){throw new TypeError(`Expected a string, got ${typeof r}`)}try{return s[t](r)[e]()}catch(t){if(t.code==="ENOENT"){return false}throw t}}e.isFile=isType.bind(null,"stat","isFile");e.isDirectory=isType.bind(null,"stat","isDirectory");e.isSymlink=isType.bind(null,"lstat","isSymbolicLink");e.isFileSync=isTypeSync.bind(null,"statSync","isFile");e.isDirectorySync=isTypeSync.bind(null,"statSync","isDirectory");e.isSymlinkSync=isTypeSync.bind(null,"lstatSync","isSymbolicLink")},398:t=>{"use strict";t.exports=t=>{const e=/^\\\\\?\\/.test(t);const r=/[^\u0000-\u0080]+/.test(t);if(e||r){return t}return t.replace(/\\/g,"/")}},592:t=>{"use strict";t.exports=require("../fast-glob")},147:t=>{"use strict";t.exports=require("fs")},17:t=>{"use strict";t.exports=require("path")},781:t=>{"use strict";t.exports=require("stream")},837:t=>{"use strict";t.exports=require("util")}};var e={};function __nccwpck_require__(r){var n=e[r];if(n!==undefined){return n.exports}var s=e[r]={exports:{}};var o=true;try{t[r](s,s.exports,__nccwpck_require__);o=false}finally{if(o)delete e[r]}return s.exports}if(typeof __nccwpck_require__!=="undefined")__nccwpck_require__.ab=__dirname+"/";var r=__nccwpck_require__(718);module.exports=r})();