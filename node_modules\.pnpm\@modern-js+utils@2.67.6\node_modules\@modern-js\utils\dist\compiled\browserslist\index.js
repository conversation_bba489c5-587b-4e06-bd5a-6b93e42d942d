(()=>{var e={923:e=>{function BrowserslistError(e){this.name="BrowserslistError";this.message=e;this.browserslist=true;if(Error.captureStackTrace){Error.captureStackTrace(this,BrowserslistError)}}BrowserslistError.prototype=Error.prototype;e.exports=BrowserslistError},751:(e,s,r)=>{var n=r(878);var t=r(768).agents;var a=r(40);var i=r(17);var o=r(476);var l=r(923);var f=r(347);var u=365.259641*24*60*60*1e3;var d=37;var c=1;var v=2;function isVersionsMatch(e,s){return(e+".").indexOf(s+".")===0}function isEolReleased(e){var s=e.slice(1);return browserslist.nodeVersions.some((function(e){return isVersionsMatch(e,s)}))}function normalize(e){return e.filter((function(e){return typeof e==="string"}))}function normalizeElectron(e){var s=e;if(e.split(".").length===3){s=e.split(".").slice(0,-1).join(".")}return s}function nameMapper(e){return function mapName(s){return e+" "+s}}function getMajor(e){return parseInt(e.split(".")[0])}function getMajorVersions(e,s){if(e.length===0)return[];var r=uniq(e.map(getMajor));var n=r[r.length-s];if(!n){return e}var t=[];for(var a=e.length-1;a>=0;a--){if(n>getMajor(e[a]))break;t.unshift(e[a])}return t}function uniq(e){var s=[];for(var r=0;r<e.length;r++){if(s.indexOf(e[r])===-1)s.push(e[r])}return s}function fillUsage(e,s,r){for(var n in r){e[s+" "+n]=r[n]}}function generateFilter(e,s){s=parseFloat(s);if(e===">"){return function(e){return parseFloat(e)>s}}else if(e===">="){return function(e){return parseFloat(e)>=s}}else if(e==="<"){return function(e){return parseFloat(e)<s}}else{return function(e){return parseFloat(e)<=s}}}function generateSemverFilter(e,s){s=s.split(".").map(parseSimpleInt);s[1]=s[1]||0;s[2]=s[2]||0;if(e===">"){return function(e){e=e.split(".").map(parseSimpleInt);return compareSemver(e,s)>0}}else if(e===">="){return function(e){e=e.split(".").map(parseSimpleInt);return compareSemver(e,s)>=0}}else if(e==="<"){return function(e){e=e.split(".").map(parseSimpleInt);return compareSemver(s,e)>0}}else{return function(e){e=e.split(".").map(parseSimpleInt);return compareSemver(s,e)>=0}}}function parseSimpleInt(e){return parseInt(e)}function compare(e,s){if(e<s)return-1;if(e>s)return+1;return 0}function compareSemver(e,s){return compare(parseInt(e[0]),parseInt(s[0]))||compare(parseInt(e[1]||"0"),parseInt(s[1]||"0"))||compare(parseInt(e[2]||"0"),parseInt(s[2]||"0"))}function semverFilterLoose(e,s){s=s.split(".").map(parseSimpleInt);if(typeof s[1]==="undefined"){s[1]="x"}switch(e){case"<=":return function(e){e=e.split(".").map(parseSimpleInt);return compareSemverLoose(e,s)<=0};case">=":default:return function(e){e=e.split(".").map(parseSimpleInt);return compareSemverLoose(e,s)>=0}}}function compareSemverLoose(e,s){if(e[0]!==s[0]){return e[0]<s[0]?-1:+1}if(s[1]==="x"){return 0}if(e[1]!==s[1]){return e[1]<s[1]?-1:+1}return 0}function resolveVersion(e,s){if(e.versions.indexOf(s)!==-1){return s}else if(browserslist.versionAliases[e.name][s]){return browserslist.versionAliases[e.name][s]}else{return false}}function normalizeVersion(e,s){var r=resolveVersion(e,s);if(r){return r}else if(e.versions.length===1){return e.versions[0]}else{return false}}function filterByYear(e,s){e=e/1e3;return Object.keys(t).reduce((function(r,n){var t=byName(n,s);if(!t)return r;var a=Object.keys(t.releaseDate).filter((function(s){var r=t.releaseDate[s];return r!==null&&r>=e}));return r.concat(a.map(nameMapper(t.name)))}),[])}function cloneData(e){return{name:e.name,versions:e.versions,released:e.released,releaseDate:e.releaseDate}}function mapVersions(e,s){e.versions=e.versions.map((function(e){return s[e]||e}));e.released=e.versions.map((function(e){return s[e]||e}));var r={};for(var n in e.releaseDate){r[s[n]||n]=e.releaseDate[n]}e.releaseDate=r;return e}function byName(e,s){e=e.toLowerCase();e=browserslist.aliases[e]||e;if(s.mobileToDesktop&&browserslist.desktopNames[e]){var r=browserslist.data[browserslist.desktopNames[e]];if(e==="android"){return normalizeAndroidData(cloneData(browserslist.data[e]),r)}else{var n=cloneData(r);n.name=e;if(e==="op_mob"){n=mapVersions(n,{"10.0-10.1":"10"})}return n}}return browserslist.data[e]}function normalizeAndroidVersions(e,s){var r=d;var n=s[s.length-1];return e.filter((function(e){return/^(?:[2-4]\.|[34]$)/.test(e)})).concat(s.slice(r-n-1))}function normalizeAndroidData(e,s){e.released=normalizeAndroidVersions(e.released,s.released);e.versions=normalizeAndroidVersions(e.versions,s.versions);return e}function checkName(e,s){var r=byName(e,s);if(!r)throw new l("Unknown browser "+e);return r}function unknownQuery(e){return new l("Unknown browser query `"+e+"`. "+"Maybe you are using old Browserslist or made typo in query.")}function filterAndroid(e,s,r){if(r.mobileToDesktop)return e;var n=browserslist.data.android.released;var t=n[n.length-1];var a=t-d-s;if(a>0){return e.slice(-1)}else{return e.slice(a-1)}}function resolve(e,s){if(Array.isArray(e)){e=flatten(e.map(parse))}else{e=parse(e)}return e.reduce((function(e,r,n){var t=r.queryString;var a=t.indexOf("not ")===0;if(a){if(n===0){throw new l("Write any browsers query (for instance, `defaults`) "+"before `"+t+"`")}t=t.slice(4)}for(var i=0;i<p.length;i++){var o=p[i];var f=t.match(o.regexp);if(f){var u=[s].concat(f.slice(1));var d=o.select.apply(browserslist,u).map((function(e){var r=e.split(" ");if(r[1]==="0"){return r[0]+" "+byName(r[0],s).versions[0]}else{return e}}));switch(r.type){case v:if(a){return e.filter((function(e){return d.indexOf(e)===-1}))}else{return e.filter((function(e){return d.indexOf(e)!==-1}))}case c:default:if(a){var m={};d.forEach((function(e){m[e]=true}));return e.filter((function(e){return!m[e]}))}return e.concat(d)}}}throw unknownQuery(t)}),[])}var m={};function browserslist(e,s){if(typeof s==="undefined")s={};if(typeof s.path==="undefined"){s.path=i.resolve?i.resolve("."):"."}if(typeof e==="undefined"||e===null){var r=browserslist.loadConfig(s);if(r){e=r}else{e=browserslist.defaults}}if(!(typeof e==="string"||Array.isArray(e))){throw new l("Browser queries must be an array or string. Got "+typeof e+".")}var n={ignoreUnknownVersions:s.ignoreUnknownVersions,dangerousExtend:s.dangerousExtend,mobileToDesktop:s.mobileToDesktop,path:s.path,env:s.env};f.oldDataWarning(browserslist.data);var t=f.getStat(s,browserslist.data);if(t){n.customUsage={};for(var a in t){fillUsage(n.customUsage,a,t[a])}}var o=JSON.stringify([e,n]);if(m[o])return m[o];var u=uniq(resolve(e,n)).sort((function(e,s){e=e.split(" ");s=s.split(" ");if(e[0]===s[0]){var r=e[1].split("-")[0];var n=s[1].split("-")[0];return compareSemver(n.split("."),r.split("."))}else{return compare(e[0],s[0])}}));if(!process.env.BROWSERSLIST_DISABLE_CACHE){m[o]=u}return u}function parse(e){var s=[];do{e=doMatch(e,s)}while(e);return s}function doMatch(e,s){var r=/^(?:,\s*|\s+or\s+)(.*)/i;var n=/^\s+and\s+(.*)/i;return find(e,(function(e,t,a){if(n.test(e)){s.unshift({type:v,queryString:e.match(n)[1]});return true}else if(r.test(e)){s.unshift({type:c,queryString:e.match(r)[1]});return true}else if(t===a){s.unshift({type:c,queryString:e.trim()});return true}return false}))}function find(e,s){for(var r=1,n=e.length;r<=n;r++){var t=e.substr(-r,r);if(s(t,r,n)){return e.slice(0,-r)}}return""}function flatten(e){if(!Array.isArray(e))return[e];return e.reduce((function(e,s){return e.concat(flatten(s))}),[])}browserslist.cache={};browserslist.data={};browserslist.usage={global:{},custom:null};browserslist.defaults=["> 0.5%","last 2 versions","Firefox ESR","not dead"];browserslist.aliases={fx:"firefox",ff:"firefox",ios:"ios_saf",explorer:"ie",blackberry:"bb",explorermobile:"ie_mob",operamini:"op_mini",operamobile:"op_mob",chromeandroid:"and_chr",firefoxandroid:"and_ff",ucandroid:"and_uc",qqandroid:"and_qq"};browserslist.desktopNames={and_chr:"chrome",and_ff:"firefox",ie_mob:"ie",op_mob:"opera",android:"chrome"};browserslist.versionAliases={};browserslist.clearCaches=f.clearCaches;browserslist.parseConfig=f.parseConfig;browserslist.readConfig=f.readConfig;browserslist.findConfig=f.findConfig;browserslist.loadConfig=f.loadConfig;browserslist.coverage=function(e,s){var r;if(typeof s==="undefined"){r=browserslist.usage.global}else if(s==="my stats"){var n={};n.path=i.resolve?i.resolve("."):".";var t=f.getStat(n);if(!t){throw new l("Custom usage statistics was not provided")}r={};for(var a in t){fillUsage(r,a,t[a])}}else if(typeof s==="string"){if(s.length>2){s=s.toLowerCase()}else{s=s.toUpperCase()}f.loadCountry(browserslist.usage,s,browserslist.data);r=browserslist.usage[s]}else{if("dataByBrowser"in s){s=s.dataByBrowser}r={};for(var o in s){for(var u in s[o]){r[o+" "+u]=s[o][u]}}}return e.reduce((function(e,s){var n=r[s];if(n===undefined){n=r[s.replace(/ \S+$/," 0")]}return e+(n||0)}),0)};function nodeQuery(e,s){var r=browserslist.nodeVersions.filter((function(e){return isVersionsMatch(e,s)}));if(r.length===0){if(e.ignoreUnknownVersions){return[]}else{throw new l("Unknown version "+s+" of Node.js")}}return["node "+r[r.length-1]]}function sinceQuery(e,s,r,n){s=parseInt(s);r=parseInt(r||"01")-1;n=parseInt(n||"01");return filterByYear(Date.UTC(s,r,n,0,0,0),e)}function coverQuery(e,s,r){s=parseFloat(s);var n=browserslist.usage.global;if(r){if(r.match(/^my\s+stats$/i)){if(!e.customUsage){throw new l("Custom usage statistics was not provided")}n=e.customUsage}else{var t;if(r.length===2){t=r.toUpperCase()}else{t=r.toLowerCase()}f.loadCountry(browserslist.usage,t,browserslist.data);n=browserslist.usage[t]}}var a=Object.keys(n).sort((function(e,s){return n[s]-n[e]}));var i=0;var o=[];var u;for(var d=0;d<a.length;d++){u=a[d];if(n[u]===0)break;i+=n[u];o.push(u);if(i>=s)break}return o}var p=[{regexp:/^last\s+(\d+)\s+major\s+versions?$/i,select:function(e,s){return Object.keys(t).reduce((function(r,n){var t=byName(n,e);if(!t)return r;var a=getMajorVersions(t.released,s);a=a.map(nameMapper(t.name));if(t.name==="android"){a=filterAndroid(a,s,e)}return r.concat(a)}),[])}},{regexp:/^last\s+(\d+)\s+versions?$/i,select:function(e,s){return Object.keys(t).reduce((function(r,n){var t=byName(n,e);if(!t)return r;var a=t.released.slice(-s);a=a.map(nameMapper(t.name));if(t.name==="android"){a=filterAndroid(a,s,e)}return r.concat(a)}),[])}},{regexp:/^last\s+(\d+)\s+electron\s+major\s+versions?$/i,select:function(e,s){var r=getMajorVersions(Object.keys(o),s);return r.map((function(e){return"chrome "+o[e]}))}},{regexp:/^last\s+(\d+)\s+node\s+major\s+versions?$/i,select:function(e,s){return getMajorVersions(browserslist.nodeVersions,s).map((function(e){return"node "+e}))}},{regexp:/^last\s+(\d+)\s+(\w+)\s+major\s+versions?$/i,select:function(e,s,r){var n=checkName(r,e);var t=getMajorVersions(n.released,s);var a=t.map(nameMapper(n.name));if(n.name==="android"){a=filterAndroid(a,s,e)}return a}},{regexp:/^last\s+(\d+)\s+electron\s+versions?$/i,select:function(e,s){return Object.keys(o).slice(-s).map((function(e){return"chrome "+o[e]}))}},{regexp:/^last\s+(\d+)\s+node\s+versions?$/i,select:function(e,s){return browserslist.nodeVersions.slice(-s).map((function(e){return"node "+e}))}},{regexp:/^last\s+(\d+)\s+(\w+)\s+versions?$/i,select:function(e,s,r){var n=checkName(r,e);var t=n.released.slice(-s).map(nameMapper(n.name));if(n.name==="android"){t=filterAndroid(t,s,e)}return t}},{regexp:/^unreleased\s+versions$/i,select:function(e){return Object.keys(t).reduce((function(s,r){var n=byName(r,e);if(!n)return s;var t=n.versions.filter((function(e){return n.released.indexOf(e)===-1}));t=t.map(nameMapper(n.name));return s.concat(t)}),[])}},{regexp:/^unreleased\s+electron\s+versions?$/i,select:function(){return[]}},{regexp:/^unreleased\s+(\w+)\s+versions?$/i,select:function(e,s){var r=checkName(s,e);return r.versions.filter((function(e){return r.released.indexOf(e)===-1})).map(nameMapper(r.name))}},{regexp:/^last\s+(\d*.?\d+)\s+years?$/i,select:function(e,s){return filterByYear(Date.now()-u*s,e)}},{regexp:/^since (\d+)$/i,select:sinceQuery},{regexp:/^since (\d+)-(\d+)$/i,select:sinceQuery},{regexp:/^since (\d+)-(\d+)-(\d+)$/i,select:sinceQuery},{regexp:/^(>=?|<=?)\s*(\d+|\d+\.\d+|\.\d+)%$/,select:function(e,s,r){r=parseFloat(r);var n=browserslist.usage.global;return Object.keys(n).reduce((function(e,t){if(s===">"){if(n[t]>r){e.push(t)}}else if(s==="<"){if(n[t]<r){e.push(t)}}else if(s==="<="){if(n[t]<=r){e.push(t)}}else if(n[t]>=r){e.push(t)}return e}),[])}},{regexp:/^(>=?|<=?)\s*(\d+|\d+\.\d+|\.\d+)%\s+in\s+my\s+stats$/,select:function(e,s,r){r=parseFloat(r);if(!e.customUsage){throw new l("Custom usage statistics was not provided")}var n=e.customUsage;return Object.keys(n).reduce((function(e,t){var a=n[t];if(a==null){return e}if(s===">"){if(a>r){e.push(t)}}else if(s==="<"){if(a<r){e.push(t)}}else if(s==="<="){if(a<=r){e.push(t)}}else if(a>=r){e.push(t)}return e}),[])}},{regexp:/^(>=?|<=?)\s*(\d+|\d+\.\d+|\.\d+)%\s+in\s+(\S+)\s+stats$/,select:function(e,s,r,n){r=parseFloat(r);var t=f.loadStat(e,n,browserslist.data);if(t){e.customUsage={};for(var a in t){fillUsage(e.customUsage,a,t[a])}}if(!e.customUsage){throw new l("Custom usage statistics was not provided")}var i=e.customUsage;return Object.keys(i).reduce((function(e,n){var t=i[n];if(t==null){return e}if(s===">"){if(t>r){e.push(n)}}else if(s==="<"){if(t<r){e.push(n)}}else if(s==="<="){if(t<=r){e.push(n)}}else if(t>=r){e.push(n)}return e}),[])}},{regexp:/^(>=?|<=?)\s*(\d+|\d+\.\d+|\.\d+)%\s+in\s+((alt-)?\w\w)$/,select:function(e,s,r,n){r=parseFloat(r);if(n.length===2){n=n.toUpperCase()}else{n=n.toLowerCase()}f.loadCountry(browserslist.usage,n,browserslist.data);var t=browserslist.usage[n];return Object.keys(t).reduce((function(e,n){var a=t[n];if(a==null){return e}if(s===">"){if(a>r){e.push(n)}}else if(s==="<"){if(a<r){e.push(n)}}else if(s==="<="){if(a<=r){e.push(n)}}else if(a>=r){e.push(n)}return e}),[])}},{regexp:/^cover\s+(\d+|\d+\.\d+|\.\d+)%$/i,select:coverQuery},{regexp:/^cover\s+(\d+|\d+\.\d+|\.\d+)%\s+in\s+(my\s+stats|(alt-)?\w\w)$/i,select:coverQuery},{regexp:/^supports\s+([\w-]+)$/,select:function(e,s){f.loadFeature(browserslist.cache,s);var r=browserslist.cache[s];return Object.keys(r).reduce((function(e,s){var n=r[s];if(n.indexOf("y")>=0||n.indexOf("a")>=0){e.push(s)}return e}),[])}},{regexp:/^electron\s+([\d.]+)\s*-\s*([\d.]+)$/i,select:function(e,s,r){var n=normalizeElectron(s);var t=normalizeElectron(r);if(!o[n]){throw new l("Unknown version "+s+" of electron")}if(!o[t]){throw new l("Unknown version "+r+" of electron")}s=parseFloat(s);r=parseFloat(r);return Object.keys(o).filter((function(e){var n=parseFloat(e);return n>=s&&n<=r})).map((function(e){return"chrome "+o[e]}))}},{regexp:/^node\s+([\d.]+)\s*-\s*([\d.]+)$/i,select:function(e,s,r){return browserslist.nodeVersions.filter(semverFilterLoose(">=",s)).filter(semverFilterLoose("<=",r)).map((function(e){return"node "+e}))}},{regexp:/^(\w+)\s+([\d.]+)\s*-\s*([\d.]+)$/i,select:function(e,s,r,n){var t=checkName(s,e);r=parseFloat(normalizeVersion(t,r)||r);n=parseFloat(normalizeVersion(t,n)||n);function filter(e){var s=parseFloat(e);return s>=r&&s<=n}return t.released.filter(filter).map(nameMapper(t.name))}},{regexp:/^electron\s*(>=?|<=?)\s*([\d.]+)$/i,select:function(e,s,r){var n=normalizeElectron(r);return Object.keys(o).filter(generateFilter(s,n)).map((function(e){return"chrome "+o[e]}))}},{regexp:/^node\s*(>=?|<=?)\s*([\d.]+)$/i,select:function(e,s,r){return browserslist.nodeVersions.filter(generateSemverFilter(s,r)).map((function(e){return"node "+e}))}},{regexp:/^(\w+)\s*(>=?|<=?)\s*([\d.]+)$/,select:function(e,s,r,n){var t=checkName(s,e);var a=browserslist.versionAliases[t.name][n];if(a){n=a}return t.released.filter(generateFilter(r,n)).map((function(e){return t.name+" "+e}))}},{regexp:/^(firefox|ff|fx)\s+esr$/i,select:function(){return["firefox 91"]}},{regexp:/(operamini|op_mini)\s+all/i,select:function(){return["op_mini all"]}},{regexp:/^electron\s+([\d.]+)$/i,select:function(e,s){var r=normalizeElectron(s);var n=o[r];if(!n){throw new l("Unknown version "+s+" of electron")}return["chrome "+n]}},{regexp:/^node\s+(\d+)$/i,select:nodeQuery},{regexp:/^node\s+(\d+\.\d+)$/i,select:nodeQuery},{regexp:/^node\s+(\d+\.\d+\.\d+)$/i,select:nodeQuery},{regexp:/^current\s+node$/i,select:function(e){return[f.currentNode(resolve,e)]}},{regexp:/^maintained\s+node\s+versions$/i,select:function(e){var s=Date.now();var r=Object.keys(a).filter((function(e){return s<Date.parse(a[e].end)&&s>Date.parse(a[e].start)&&isEolReleased(e)})).map((function(e){return"node "+e.slice(1)}));return resolve(r,e)}},{regexp:/^phantomjs\s+1.9$/i,select:function(){return["safari 5"]}},{regexp:/^phantomjs\s+2.1$/i,select:function(){return["safari 6"]}},{regexp:/^(\w+)\s+(tp|[\d.]+)$/i,select:function(e,s,r){if(/^tp$/i.test(r))r="TP";var n=checkName(s,e);var t=normalizeVersion(n,r);if(t){r=t}else{if(r.indexOf(".")===-1){t=r+".0"}else{t=r.replace(/\.0$/,"")}t=normalizeVersion(n,t);if(t){r=t}else if(e.ignoreUnknownVersions){return[]}else{throw new l("Unknown version "+r+" of "+s)}}return[n.name+" "+r]}},{regexp:/^browserslist config$/i,select:function(e){return browserslist(undefined,e)}},{regexp:/^extends (.+)$/i,select:function(e,s){return resolve(f.loadQueries(e,s),e)}},{regexp:/^defaults$/i,select:function(e){return resolve(browserslist.defaults,e)}},{regexp:/^dead$/i,select:function(e){var s=["ie <= 10","ie_mob <= 11","bb <= 10","op_mob <= 12.1","samsung 4"];return resolve(s,e)}},{regexp:/^(\w+)$/i,select:function(e,s){if(byName(s,e)){throw new l("Specify versions in Browserslist query for browser "+s)}else{throw unknownQuery(s)}}}];(function(){for(var e in t){var s=t[e];browserslist.data[e]={name:e,versions:normalize(t[e].versions),released:normalize(t[e].versions.slice(0,-3)),releaseDate:t[e].release_date};fillUsage(browserslist.usage.global,e,s.usage_global);browserslist.versionAliases[e]={};for(var r=0;r<s.versions.length;r++){var a=s.versions[r];if(!a)continue;if(a.indexOf("-")!==-1){var i=a.split("-");for(var o=0;o<i.length;o++){browserslist.versionAliases[e][i[o]]=a}}}}browserslist.versionAliases.op_mob["59"]="58";browserslist.nodeVersions=n.map((function(e){return e.version}))})();e.exports=browserslist},347:(e,s,r)=>{var n=r(711)["default"];var t=r(225)["default"];var a=r(17);var i=r(147);var o=r(923);var l=/^\s*\[(.+)]\s*$/;var f=/^browserslist-config-/;var u=/@[^/]+\/browserslist-config(-|$|\/)/;var d=6*30*24*60*60*1e3;var c="Browserslist config should be a string or an array "+"of strings with browser queries";var v=false;var m={};var p={};function checkExtend(e){var s=" Use `dangerousExtend` option to disable.";if(!f.test(e)&&!u.test(e)){throw new o("Browserslist config needs `browserslist-config-` prefix. "+s)}if(e.replace(/^@[^/]+\//,"").indexOf(".")!==-1){throw new o("`.` not allowed in Browserslist config name. "+s)}if(e.indexOf("node_modules")!==-1){throw new o("`node_modules` not allowed in Browserslist config."+s)}}function isFile(e){if(e in m){return m[e]}var s=i.existsSync(e)&&i.statSync(e).isFile();if(!process.env.BROWSERSLIST_DISABLE_CACHE){m[e]=s}return s}function eachParent(e,s){var r=isFile(e)?a.dirname(e):e;var n=a.resolve(r);do{var t=s(n);if(typeof t!=="undefined")return t}while(n!==(n=a.dirname(n)));return undefined}function check(e){if(Array.isArray(e)){for(var s=0;s<e.length;s++){if(typeof e[s]!=="string"){throw new o(c)}}}else if(typeof e!=="string"){throw new o(c)}}function pickEnv(e,s){if(typeof e!=="object")return e;var r;if(typeof s.env==="string"){r=s.env}else if(process.env.BROWSERSLIST_ENV){r=process.env.BROWSERSLIST_ENV}else if(process.env.NODE_ENV){r=process.env.NODE_ENV}else{r="production"}if(s.throwOnMissing){if(r&&r!=="defaults"&&!e[r]){throw new o("Missing config for Browserslist environment `"+r+"`")}}return e[r]||e.defaults}function parsePackage(e){var s=JSON.parse(i.readFileSync(e));if(s.browserlist&&!s.browserslist){throw new o("`browserlist` key instead of `browserslist` in "+e)}var r=s.browserslist;if(Array.isArray(r)||typeof r==="string"){r={defaults:r}}for(var n in r){check(r[n])}return r}function latestReleaseTime(e){var s=0;for(var r in e){var n=e[r].releaseDate||{};for(var t in n){if(s<n[t]){s=n[t]}}}return s*1e3}function normalizeStats(e,s){if(!e){e={}}if(s&&"dataByBrowser"in s){s=s.dataByBrowser}if(typeof s!=="object")return undefined;var r={};for(var n in s){var t=Object.keys(s[n]);if(t.length===1&&e[n]&&e[n].versions.length===1){var a=e[n].versions[0];r[n]={};r[n][a]=s[n][t[0]]}else{r[n]=s[n]}}return r}function normalizeUsageData(e,s){for(var r in e){var n=e[r];if("0"in n){var t=s[r].versions;n[t[t.length-1]]=n[0];delete n[0]}}}e.exports={loadQueries:function loadQueries(e,s){if(!e.dangerousExtend&&!process.env.BROWSERSLIST_DANGEROUS_EXTEND){checkExtend(s)}var n=require(r(174).resolve(s,{paths:[".",e.path]}));if(n){if(Array.isArray(n)){return n}else if(typeof n==="object"){if(!n.defaults)n.defaults=[];return pickEnv(n,e,s)}}throw new o("`"+s+"` config exports not an array of queries"+" or an object of envs")},loadStat:function loadStat(e,s,n){if(!e.dangerousExtend&&!process.env.BROWSERSLIST_DANGEROUS_EXTEND){checkExtend(s)}var t=require(r(174).resolve(a.join(s,"browserslist-stats.json"),{paths:["."]}));return normalizeStats(n,t)},getStat:function getStat(e,s){var r;if(e.stats){r=e.stats}else if(process.env.BROWSERSLIST_STATS){r=process.env.BROWSERSLIST_STATS}else if(e.path&&a.resolve&&i.existsSync){r=eachParent(e.path,(function(e){var s=a.join(e,"browserslist-stats.json");return isFile(s)?s:undefined}))}if(typeof r==="string"){try{r=JSON.parse(i.readFileSync(r))}catch(e){throw new o("Can't read "+r)}}return normalizeStats(s,r)},loadConfig:function loadConfig(s){if(process.env.BROWSERSLIST){return process.env.BROWSERSLIST}else if(s.config||process.env.BROWSERSLIST_CONFIG){var r=s.config||process.env.BROWSERSLIST_CONFIG;if(a.basename(r)==="package.json"){return pickEnv(parsePackage(r),s)}else{return pickEnv(e.exports.readConfig(r),s)}}else if(s.path){return pickEnv(e.exports.findConfig(s.path),s)}else{return undefined}},loadCountry:function loadCountry(e,s,r){var n=s.replace(/[^\w-]/g,"");if(!e[n]){var a=require("caniuse-lite/data/regions/"+n+".js");var i=t(a);normalizeUsageData(i,r);e[s]={};for(var o in i){for(var l in i[o]){e[s][o+" "+l]=i[o][l]}}}},loadFeature:function loadFeature(e,s){s=s.replace(/[^\w-]/g,"");if(e[s])return;var r=require("caniuse-lite/data/features/"+s+".js");var t=n(r).stats;e[s]={};for(var a in t){for(var i in t[a]){e[s][a+" "+i]=t[a][i]}}},parseConfig:function parseConfig(e){var s={defaults:[]};var r=["defaults"];e.toString().replace(/#[^\n]*/g,"").split(/\n|,/).map((function(e){return e.trim()})).filter((function(e){return e!==""})).forEach((function(e){if(l.test(e)){r=e.match(l)[1].trim().split(" ");r.forEach((function(e){if(s[e]){throw new o("Duplicate section "+e+" in Browserslist config")}s[e]=[]}))}else{r.forEach((function(r){s[r].push(e)}))}}));return s},readConfig:function readConfig(s){if(!isFile(s)){throw new o("Can't read "+s+" config")}return e.exports.parseConfig(i.readFileSync(s))},findConfig:function findConfig(s){s=a.resolve(s);var r=[];var n=eachParent(s,(function(s){if(s in p){return p[s]}r.push(s);var n=a.join(s,"browserslist");var t=a.join(s,"package.json");var i=a.join(s,".browserslistrc");var l;if(isFile(t)){try{l=parsePackage(t)}catch(e){if(e.name==="BrowserslistError")throw e;console.warn("[Browserslist] Could not parse "+t+". Ignoring it.")}}if(isFile(n)&&l){throw new o(s+" contains both browserslist and package.json with browsers")}else if(isFile(i)&&l){throw new o(s+" contains both .browserslistrc and package.json with browsers")}else if(isFile(n)&&isFile(i)){throw new o(s+" contains both .browserslistrc and browserslist")}else if(isFile(n)){return e.exports.readConfig(n)}else if(isFile(i)){return e.exports.readConfig(i)}else{return l}}));if(!process.env.BROWSERSLIST_DISABLE_CACHE){r.forEach((function(e){p[e]=n}))}return n},clearCaches:function clearCaches(){v=false;m={};p={};this.cache={}},oldDataWarning:function oldDataWarning(e){if(v)return;v=true;if(process.env.BROWSERSLIST_IGNORE_OLD_DATA)return;var s=latestReleaseTime(e);var r=Date.now()-d;if(s!==0&&s<r){console.warn("Browserslist: caniuse-lite is outdated. Please run:\n"+"  npx browserslist@latest --update-db\n"+"  Why you should do it regularly: "+"https://github.com/browserslist/browserslist#browsers-data-updating")}},currentNode:function currentNode(){return"node "+process.versions.node}}},476:e=>{e.exports={"0.20":"39",.21:"41",.22:"41",.23:"41",.24:"41",.25:"42",.26:"42",.27:"43",.28:"43",.29:"43","0.30":"44",.31:"45",.32:"45",.33:"45",.34:"45",.35:"45",.36:"47",.37:"49","1.0":"49",1.1:"50",1.2:"51",1.3:"52",1.4:"53",1.5:"54",1.6:"56",1.7:"58",1.8:"59","2.0":"61",2.1:"61","3.0":"66",3.1:"66","4.0":"69",4.1:"69",4.2:"69","5.0":"73","6.0":"76",6.1:"76","7.0":"78",7.1:"78",7.2:"78",7.3:"78","8.0":"80",8.1:"80",8.2:"80",8.3:"80",8.4:"80",8.5:"80","9.0":"83",9.1:"83",9.2:"83",9.3:"83",9.4:"83","10.0":"85",10.1:"85",10.2:"85",10.3:"85",10.4:"85","11.0":"87",11.1:"87",11.2:"87",11.3:"87",11.4:"87",11.5:"87","12.0":"89",12.1:"89",12.2:"89","13.0":"91",13.1:"91",13.2:"91",13.3:"91",13.4:"91",13.5:"91",13.6:"91","14.0":"93",14.1:"93",14.2:"93","15.0":"94",15.1:"94",15.2:"94",15.3:"94",15.4:"94",15.5:"94","16.0":"96",16.1:"96",16.2:"96","17.0":"98",17.1:"98",17.2:"98",17.3:"98",17.4:"98","18.0":"100",18.1:"100",18.2:"100",18.3:"100","19.0":"102",19.1:"102","20.0":"104",20.1:"104",20.2:"104",20.3:"104","21.0":"106",21.1:"106","22.0":"108"}},174:e=>{function webpackEmptyContext(e){var s=new Error("Cannot find module '"+e+"'");s.code="MODULE_NOT_FOUND";throw s}webpackEmptyContext.keys=()=>[];webpackEmptyContext.resolve=webpackEmptyContext;webpackEmptyContext.id=174;e.exports=webpackEmptyContext},768:e=>{"use strict";e.exports=require("caniuse-lite/dist/unpacker/agents")},711:e=>{"use strict";e.exports=require("caniuse-lite/dist/unpacker/feature")},225:e=>{"use strict";e.exports=require("caniuse-lite/dist/unpacker/region")},147:e=>{"use strict";e.exports=require("fs")},17:e=>{"use strict";e.exports=require("path")},878:e=>{"use strict";e.exports=JSON.parse('[{"name":"nodejs","version":"0.2.0","date":"2011-08-26","lts":false,"security":false},{"name":"nodejs","version":"0.3.0","date":"2011-08-26","lts":false,"security":false},{"name":"nodejs","version":"0.4.0","date":"2011-08-26","lts":false,"security":false},{"name":"nodejs","version":"0.5.0","date":"2011-08-26","lts":false,"security":false},{"name":"nodejs","version":"0.6.0","date":"2011-11-04","lts":false,"security":false},{"name":"nodejs","version":"0.7.0","date":"2012-01-17","lts":false,"security":false},{"name":"nodejs","version":"0.8.0","date":"2012-06-22","lts":false,"security":false},{"name":"nodejs","version":"0.9.0","date":"2012-07-20","lts":false,"security":false},{"name":"nodejs","version":"0.10.0","date":"2013-03-11","lts":false,"security":false},{"name":"nodejs","version":"0.11.0","date":"2013-03-28","lts":false,"security":false},{"name":"nodejs","version":"0.12.0","date":"2015-02-06","lts":false,"security":false},{"name":"nodejs","version":"4.0.0","date":"2015-09-08","lts":false,"security":false},{"name":"nodejs","version":"4.1.0","date":"2015-09-17","lts":false,"security":false},{"name":"nodejs","version":"4.2.0","date":"2015-10-12","lts":"Argon","security":false},{"name":"nodejs","version":"4.3.0","date":"2016-02-09","lts":"Argon","security":false},{"name":"nodejs","version":"4.4.0","date":"2016-03-08","lts":"Argon","security":false},{"name":"nodejs","version":"4.5.0","date":"2016-08-16","lts":"Argon","security":false},{"name":"nodejs","version":"4.6.0","date":"2016-09-27","lts":"Argon","security":true},{"name":"nodejs","version":"4.7.0","date":"2016-12-06","lts":"Argon","security":false},{"name":"nodejs","version":"4.8.0","date":"2017-02-21","lts":"Argon","security":false},{"name":"nodejs","version":"4.9.0","date":"2018-03-28","lts":"Argon","security":true},{"name":"nodejs","version":"5.0.0","date":"2015-10-29","lts":false,"security":false},{"name":"nodejs","version":"5.1.0","date":"2015-11-17","lts":false,"security":false},{"name":"nodejs","version":"5.2.0","date":"2015-12-09","lts":false,"security":false},{"name":"nodejs","version":"5.3.0","date":"2015-12-15","lts":false,"security":false},{"name":"nodejs","version":"5.4.0","date":"2016-01-06","lts":false,"security":false},{"name":"nodejs","version":"5.5.0","date":"2016-01-21","lts":false,"security":false},{"name":"nodejs","version":"5.6.0","date":"2016-02-09","lts":false,"security":false},{"name":"nodejs","version":"5.7.0","date":"2016-02-23","lts":false,"security":false},{"name":"nodejs","version":"5.8.0","date":"2016-03-09","lts":false,"security":false},{"name":"nodejs","version":"5.9.0","date":"2016-03-16","lts":false,"security":false},{"name":"nodejs","version":"5.10.0","date":"2016-04-01","lts":false,"security":false},{"name":"nodejs","version":"5.11.0","date":"2016-04-21","lts":false,"security":false},{"name":"nodejs","version":"5.12.0","date":"2016-06-23","lts":false,"security":false},{"name":"nodejs","version":"6.0.0","date":"2016-04-26","lts":false,"security":false},{"name":"nodejs","version":"6.1.0","date":"2016-05-05","lts":false,"security":false},{"name":"nodejs","version":"6.2.0","date":"2016-05-17","lts":false,"security":false},{"name":"nodejs","version":"6.3.0","date":"2016-07-06","lts":false,"security":false},{"name":"nodejs","version":"6.4.0","date":"2016-08-12","lts":false,"security":false},{"name":"nodejs","version":"6.5.0","date":"2016-08-26","lts":false,"security":false},{"name":"nodejs","version":"6.6.0","date":"2016-09-14","lts":false,"security":false},{"name":"nodejs","version":"6.7.0","date":"2016-09-27","lts":false,"security":true},{"name":"nodejs","version":"6.8.0","date":"2016-10-12","lts":false,"security":false},{"name":"nodejs","version":"6.9.0","date":"2016-10-18","lts":"Boron","security":false},{"name":"nodejs","version":"6.10.0","date":"2017-02-21","lts":"Boron","security":false},{"name":"nodejs","version":"6.11.0","date":"2017-06-06","lts":"Boron","security":false},{"name":"nodejs","version":"6.12.0","date":"2017-11-06","lts":"Boron","security":false},{"name":"nodejs","version":"6.13.0","date":"2018-02-10","lts":"Boron","security":false},{"name":"nodejs","version":"6.14.0","date":"2018-03-28","lts":"Boron","security":true},{"name":"nodejs","version":"6.15.0","date":"2018-11-27","lts":"Boron","security":true},{"name":"nodejs","version":"6.16.0","date":"2018-12-26","lts":"Boron","security":false},{"name":"nodejs","version":"6.17.0","date":"2019-02-28","lts":"Boron","security":true},{"name":"nodejs","version":"7.0.0","date":"2016-10-25","lts":false,"security":false},{"name":"nodejs","version":"7.1.0","date":"2016-11-08","lts":false,"security":false},{"name":"nodejs","version":"7.2.0","date":"2016-11-22","lts":false,"security":false},{"name":"nodejs","version":"7.3.0","date":"2016-12-20","lts":false,"security":false},{"name":"nodejs","version":"7.4.0","date":"2017-01-04","lts":false,"security":false},{"name":"nodejs","version":"7.5.0","date":"2017-01-31","lts":false,"security":false},{"name":"nodejs","version":"7.6.0","date":"2017-02-21","lts":false,"security":false},{"name":"nodejs","version":"7.7.0","date":"2017-02-28","lts":false,"security":false},{"name":"nodejs","version":"7.8.0","date":"2017-03-29","lts":false,"security":false},{"name":"nodejs","version":"7.9.0","date":"2017-04-11","lts":false,"security":false},{"name":"nodejs","version":"7.10.0","date":"2017-05-02","lts":false,"security":false},{"name":"nodejs","version":"8.0.0","date":"2017-05-30","lts":false,"security":false},{"name":"nodejs","version":"8.1.0","date":"2017-06-08","lts":false,"security":false},{"name":"nodejs","version":"8.2.0","date":"2017-07-19","lts":false,"security":false},{"name":"nodejs","version":"8.3.0","date":"2017-08-08","lts":false,"security":false},{"name":"nodejs","version":"8.4.0","date":"2017-08-15","lts":false,"security":false},{"name":"nodejs","version":"8.5.0","date":"2017-09-12","lts":false,"security":false},{"name":"nodejs","version":"8.6.0","date":"2017-09-26","lts":false,"security":false},{"name":"nodejs","version":"8.7.0","date":"2017-10-11","lts":false,"security":false},{"name":"nodejs","version":"8.8.0","date":"2017-10-24","lts":false,"security":false},{"name":"nodejs","version":"8.9.0","date":"2017-10-31","lts":"Carbon","security":false},{"name":"nodejs","version":"8.10.0","date":"2018-03-06","lts":"Carbon","security":false},{"name":"nodejs","version":"8.11.0","date":"2018-03-28","lts":"Carbon","security":true},{"name":"nodejs","version":"8.12.0","date":"2018-09-10","lts":"Carbon","security":false},{"name":"nodejs","version":"8.13.0","date":"2018-11-20","lts":"Carbon","security":false},{"name":"nodejs","version":"8.14.0","date":"2018-11-27","lts":"Carbon","security":true},{"name":"nodejs","version":"8.15.0","date":"2018-12-26","lts":"Carbon","security":false},{"name":"nodejs","version":"8.16.0","date":"2019-04-16","lts":"Carbon","security":false},{"name":"nodejs","version":"8.17.0","date":"2019-12-17","lts":"Carbon","security":true},{"name":"nodejs","version":"9.0.0","date":"2017-10-31","lts":false,"security":false},{"name":"nodejs","version":"9.1.0","date":"2017-11-07","lts":false,"security":false},{"name":"nodejs","version":"9.2.0","date":"2017-11-14","lts":false,"security":false},{"name":"nodejs","version":"9.3.0","date":"2017-12-12","lts":false,"security":false},{"name":"nodejs","version":"9.4.0","date":"2018-01-10","lts":false,"security":false},{"name":"nodejs","version":"9.5.0","date":"2018-01-31","lts":false,"security":false},{"name":"nodejs","version":"9.6.0","date":"2018-02-21","lts":false,"security":false},{"name":"nodejs","version":"9.7.0","date":"2018-03-01","lts":false,"security":false},{"name":"nodejs","version":"9.8.0","date":"2018-03-07","lts":false,"security":false},{"name":"nodejs","version":"9.9.0","date":"2018-03-21","lts":false,"security":false},{"name":"nodejs","version":"9.10.0","date":"2018-03-28","lts":false,"security":true},{"name":"nodejs","version":"9.11.0","date":"2018-04-04","lts":false,"security":false},{"name":"nodejs","version":"10.0.0","date":"2018-04-24","lts":false,"security":false},{"name":"nodejs","version":"10.1.0","date":"2018-05-08","lts":false,"security":false},{"name":"nodejs","version":"10.2.0","date":"2018-05-23","lts":false,"security":false},{"name":"nodejs","version":"10.3.0","date":"2018-05-29","lts":false,"security":false},{"name":"nodejs","version":"10.4.0","date":"2018-06-06","lts":false,"security":false},{"name":"nodejs","version":"10.5.0","date":"2018-06-20","lts":false,"security":false},{"name":"nodejs","version":"10.6.0","date":"2018-07-04","lts":false,"security":false},{"name":"nodejs","version":"10.7.0","date":"2018-07-18","lts":false,"security":false},{"name":"nodejs","version":"10.8.0","date":"2018-08-01","lts":false,"security":false},{"name":"nodejs","version":"10.9.0","date":"2018-08-15","lts":false,"security":false},{"name":"nodejs","version":"10.10.0","date":"2018-09-06","lts":false,"security":false},{"name":"nodejs","version":"10.11.0","date":"2018-09-19","lts":false,"security":false},{"name":"nodejs","version":"10.12.0","date":"2018-10-10","lts":false,"security":false},{"name":"nodejs","version":"10.13.0","date":"2018-10-30","lts":"Dubnium","security":false},{"name":"nodejs","version":"10.14.0","date":"2018-11-27","lts":"Dubnium","security":true},{"name":"nodejs","version":"10.15.0","date":"2018-12-26","lts":"Dubnium","security":false},{"name":"nodejs","version":"10.16.0","date":"2019-05-28","lts":"Dubnium","security":false},{"name":"nodejs","version":"10.17.0","date":"2019-10-22","lts":"Dubnium","security":false},{"name":"nodejs","version":"10.18.0","date":"2019-12-17","lts":"Dubnium","security":true},{"name":"nodejs","version":"10.19.0","date":"2020-02-05","lts":"Dubnium","security":true},{"name":"nodejs","version":"10.20.0","date":"2020-03-26","lts":"Dubnium","security":false},{"name":"nodejs","version":"10.21.0","date":"2020-06-02","lts":"Dubnium","security":true},{"name":"nodejs","version":"10.22.0","date":"2020-07-21","lts":"Dubnium","security":false},{"name":"nodejs","version":"10.23.0","date":"2020-10-27","lts":"Dubnium","security":false},{"name":"nodejs","version":"10.24.0","date":"2021-02-23","lts":"Dubnium","security":true},{"name":"nodejs","version":"11.0.0","date":"2018-10-23","lts":false,"security":false},{"name":"nodejs","version":"11.1.0","date":"2018-10-30","lts":false,"security":false},{"name":"nodejs","version":"11.2.0","date":"2018-11-15","lts":false,"security":false},{"name":"nodejs","version":"11.3.0","date":"2018-11-27","lts":false,"security":true},{"name":"nodejs","version":"11.4.0","date":"2018-12-07","lts":false,"security":false},{"name":"nodejs","version":"11.5.0","date":"2018-12-18","lts":false,"security":false},{"name":"nodejs","version":"11.6.0","date":"2018-12-26","lts":false,"security":false},{"name":"nodejs","version":"11.7.0","date":"2019-01-17","lts":false,"security":false},{"name":"nodejs","version":"11.8.0","date":"2019-01-24","lts":false,"security":false},{"name":"nodejs","version":"11.9.0","date":"2019-01-30","lts":false,"security":false},{"name":"nodejs","version":"11.10.0","date":"2019-02-14","lts":false,"security":false},{"name":"nodejs","version":"11.11.0","date":"2019-03-05","lts":false,"security":false},{"name":"nodejs","version":"11.12.0","date":"2019-03-14","lts":false,"security":false},{"name":"nodejs","version":"11.13.0","date":"2019-03-28","lts":false,"security":false},{"name":"nodejs","version":"11.14.0","date":"2019-04-10","lts":false,"security":false},{"name":"nodejs","version":"11.15.0","date":"2019-04-30","lts":false,"security":false},{"name":"nodejs","version":"12.0.0","date":"2019-04-23","lts":false,"security":false},{"name":"nodejs","version":"12.1.0","date":"2019-04-29","lts":false,"security":false},{"name":"nodejs","version":"12.2.0","date":"2019-05-07","lts":false,"security":false},{"name":"nodejs","version":"12.3.0","date":"2019-05-21","lts":false,"security":false},{"name":"nodejs","version":"12.4.0","date":"2019-06-04","lts":false,"security":false},{"name":"nodejs","version":"12.5.0","date":"2019-06-26","lts":false,"security":false},{"name":"nodejs","version":"12.6.0","date":"2019-07-03","lts":false,"security":false},{"name":"nodejs","version":"12.7.0","date":"2019-07-23","lts":false,"security":false},{"name":"nodejs","version":"12.8.0","date":"2019-08-06","lts":false,"security":false},{"name":"nodejs","version":"12.9.0","date":"2019-08-20","lts":false,"security":false},{"name":"nodejs","version":"12.10.0","date":"2019-09-04","lts":false,"security":false},{"name":"nodejs","version":"12.11.0","date":"2019-09-25","lts":false,"security":false},{"name":"nodejs","version":"12.12.0","date":"2019-10-11","lts":false,"security":false},{"name":"nodejs","version":"12.13.0","date":"2019-10-21","lts":"Erbium","security":false},{"name":"nodejs","version":"12.14.0","date":"2019-12-17","lts":"Erbium","security":true},{"name":"nodejs","version":"12.15.0","date":"2020-02-05","lts":"Erbium","security":true},{"name":"nodejs","version":"12.16.0","date":"2020-02-11","lts":"Erbium","security":false},{"name":"nodejs","version":"12.17.0","date":"2020-05-26","lts":"Erbium","security":false},{"name":"nodejs","version":"12.18.0","date":"2020-06-02","lts":"Erbium","security":true},{"name":"nodejs","version":"12.19.0","date":"2020-10-06","lts":"Erbium","security":false},{"name":"nodejs","version":"12.20.0","date":"2020-11-24","lts":"Erbium","security":false},{"name":"nodejs","version":"12.21.0","date":"2021-02-23","lts":"Erbium","security":true},{"name":"nodejs","version":"12.22.0","date":"2021-03-30","lts":"Erbium","security":false},{"name":"nodejs","version":"13.0.0","date":"2019-10-22","lts":false,"security":false},{"name":"nodejs","version":"13.1.0","date":"2019-11-05","lts":false,"security":false},{"name":"nodejs","version":"13.2.0","date":"2019-11-21","lts":false,"security":false},{"name":"nodejs","version":"13.3.0","date":"2019-12-03","lts":false,"security":false},{"name":"nodejs","version":"13.4.0","date":"2019-12-17","lts":false,"security":true},{"name":"nodejs","version":"13.5.0","date":"2019-12-18","lts":false,"security":false},{"name":"nodejs","version":"13.6.0","date":"2020-01-07","lts":false,"security":false},{"name":"nodejs","version":"13.7.0","date":"2020-01-21","lts":false,"security":false},{"name":"nodejs","version":"13.8.0","date":"2020-02-05","lts":false,"security":true},{"name":"nodejs","version":"13.9.0","date":"2020-02-18","lts":false,"security":false},{"name":"nodejs","version":"13.10.0","date":"2020-03-04","lts":false,"security":false},{"name":"nodejs","version":"13.11.0","date":"2020-03-12","lts":false,"security":false},{"name":"nodejs","version":"13.12.0","date":"2020-03-26","lts":false,"security":false},{"name":"nodejs","version":"13.13.0","date":"2020-04-14","lts":false,"security":false},{"name":"nodejs","version":"13.14.0","date":"2020-04-29","lts":false,"security":false},{"name":"nodejs","version":"14.0.0","date":"2020-04-21","lts":false,"security":false},{"name":"nodejs","version":"14.1.0","date":"2020-04-29","lts":false,"security":false},{"name":"nodejs","version":"14.2.0","date":"2020-05-05","lts":false,"security":false},{"name":"nodejs","version":"14.3.0","date":"2020-05-19","lts":false,"security":false},{"name":"nodejs","version":"14.4.0","date":"2020-06-02","lts":false,"security":true},{"name":"nodejs","version":"14.5.0","date":"2020-06-30","lts":false,"security":false},{"name":"nodejs","version":"14.6.0","date":"2020-07-20","lts":false,"security":false},{"name":"nodejs","version":"14.7.0","date":"2020-07-29","lts":false,"security":false},{"name":"nodejs","version":"14.8.0","date":"2020-08-11","lts":false,"security":false},{"name":"nodejs","version":"14.9.0","date":"2020-08-27","lts":false,"security":false},{"name":"nodejs","version":"14.10.0","date":"2020-09-08","lts":false,"security":false},{"name":"nodejs","version":"14.11.0","date":"2020-09-15","lts":false,"security":true},{"name":"nodejs","version":"14.12.0","date":"2020-09-22","lts":false,"security":false},{"name":"nodejs","version":"14.13.0","date":"2020-09-29","lts":false,"security":false},{"name":"nodejs","version":"14.14.0","date":"2020-10-15","lts":false,"security":false},{"name":"nodejs","version":"14.15.0","date":"2020-10-27","lts":"Fermium","security":false},{"name":"nodejs","version":"14.16.0","date":"2021-02-23","lts":"Fermium","security":true},{"name":"nodejs","version":"14.17.0","date":"2021-05-11","lts":"Fermium","security":false},{"name":"nodejs","version":"14.18.0","date":"2021-09-28","lts":"Fermium","security":false},{"name":"nodejs","version":"14.19.0","date":"2022-02-01","lts":"Fermium","security":false},{"name":"nodejs","version":"14.20.0","date":"2022-07-07","lts":"Fermium","security":true},{"name":"nodejs","version":"15.0.0","date":"2020-10-20","lts":false,"security":false},{"name":"nodejs","version":"15.1.0","date":"2020-11-04","lts":false,"security":false},{"name":"nodejs","version":"15.2.0","date":"2020-11-10","lts":false,"security":false},{"name":"nodejs","version":"15.3.0","date":"2020-11-24","lts":false,"security":false},{"name":"nodejs","version":"15.4.0","date":"2020-12-09","lts":false,"security":false},{"name":"nodejs","version":"15.5.0","date":"2020-12-22","lts":false,"security":false},{"name":"nodejs","version":"15.6.0","date":"2021-01-14","lts":false,"security":false},{"name":"nodejs","version":"15.7.0","date":"2021-01-25","lts":false,"security":false},{"name":"nodejs","version":"15.8.0","date":"2021-02-02","lts":false,"security":false},{"name":"nodejs","version":"15.9.0","date":"2021-02-18","lts":false,"security":false},{"name":"nodejs","version":"15.10.0","date":"2021-02-23","lts":false,"security":true},{"name":"nodejs","version":"15.11.0","date":"2021-03-03","lts":false,"security":false},{"name":"nodejs","version":"15.12.0","date":"2021-03-17","lts":false,"security":false},{"name":"nodejs","version":"15.13.0","date":"2021-03-31","lts":false,"security":false},{"name":"nodejs","version":"15.14.0","date":"2021-04-06","lts":false,"security":false},{"name":"nodejs","version":"16.0.0","date":"2021-04-20","lts":false,"security":false},{"name":"nodejs","version":"16.1.0","date":"2021-05-04","lts":false,"security":false},{"name":"nodejs","version":"16.2.0","date":"2021-05-19","lts":false,"security":false},{"name":"nodejs","version":"16.3.0","date":"2021-06-03","lts":false,"security":false},{"name":"nodejs","version":"16.4.0","date":"2021-06-23","lts":false,"security":false},{"name":"nodejs","version":"16.5.0","date":"2021-07-14","lts":false,"security":false},{"name":"nodejs","version":"16.6.0","date":"2021-07-29","lts":false,"security":true},{"name":"nodejs","version":"16.7.0","date":"2021-08-18","lts":false,"security":false},{"name":"nodejs","version":"16.8.0","date":"2021-08-25","lts":false,"security":false},{"name":"nodejs","version":"16.9.0","date":"2021-09-07","lts":false,"security":false},{"name":"nodejs","version":"16.10.0","date":"2021-09-22","lts":false,"security":false},{"name":"nodejs","version":"16.11.0","date":"2021-10-08","lts":false,"security":false},{"name":"nodejs","version":"16.12.0","date":"2021-10-20","lts":false,"security":false},{"name":"nodejs","version":"16.13.0","date":"2021-10-26","lts":"Gallium","security":false},{"name":"nodejs","version":"16.14.0","date":"2022-02-08","lts":"Gallium","security":false},{"name":"nodejs","version":"16.15.0","date":"2022-04-26","lts":"Gallium","security":false},{"name":"nodejs","version":"16.16.0","date":"2022-07-07","lts":"Gallium","security":true},{"name":"nodejs","version":"17.0.0","date":"2021-10-19","lts":false,"security":false},{"name":"nodejs","version":"17.1.0","date":"2021-11-09","lts":false,"security":false},{"name":"nodejs","version":"17.2.0","date":"2021-11-30","lts":false,"security":false},{"name":"nodejs","version":"17.3.0","date":"2021-12-17","lts":false,"security":false},{"name":"nodejs","version":"17.4.0","date":"2022-01-18","lts":false,"security":false},{"name":"nodejs","version":"17.5.0","date":"2022-02-10","lts":false,"security":false},{"name":"nodejs","version":"17.6.0","date":"2022-02-22","lts":false,"security":false},{"name":"nodejs","version":"17.7.0","date":"2022-03-09","lts":false,"security":false},{"name":"nodejs","version":"17.8.0","date":"2022-03-22","lts":false,"security":false},{"name":"nodejs","version":"17.9.0","date":"2022-04-07","lts":false,"security":false},{"name":"nodejs","version":"18.0.0","date":"2022-04-18","lts":false,"security":false},{"name":"nodejs","version":"18.1.0","date":"2022-05-03","lts":false,"security":false},{"name":"nodejs","version":"18.2.0","date":"2022-05-17","lts":false,"security":false},{"name":"nodejs","version":"18.3.0","date":"2022-06-02","lts":false,"security":false},{"name":"nodejs","version":"18.4.0","date":"2022-06-16","lts":false,"security":false},{"name":"nodejs","version":"18.5.0","date":"2022-07-06","lts":false,"security":true}]')},40:e=>{"use strict";e.exports=JSON.parse('{"v0.8":{"start":"2012-06-25","end":"2014-07-31"},"v0.10":{"start":"2013-03-11","end":"2016-10-31"},"v0.12":{"start":"2015-02-06","end":"2016-12-31"},"v4":{"start":"2015-09-08","lts":"2015-10-12","maintenance":"2017-04-01","end":"2018-04-30","codename":"Argon"},"v5":{"start":"2015-10-29","maintenance":"2016-04-30","end":"2016-06-30"},"v6":{"start":"2016-04-26","lts":"2016-10-18","maintenance":"2018-04-30","end":"2019-04-30","codename":"Boron"},"v7":{"start":"2016-10-25","maintenance":"2017-04-30","end":"2017-06-30"},"v8":{"start":"2017-05-30","lts":"2017-10-31","maintenance":"2019-01-01","end":"2019-12-31","codename":"Carbon"},"v9":{"start":"2017-10-01","maintenance":"2018-04-01","end":"2018-06-30"},"v10":{"start":"2018-04-24","lts":"2018-10-30","maintenance":"2020-05-19","end":"2021-04-30","codename":"Dubnium"},"v11":{"start":"2018-10-23","maintenance":"2019-04-22","end":"2019-06-01"},"v12":{"start":"2019-04-23","lts":"2019-10-21","maintenance":"2020-11-30","end":"2022-04-30","codename":"Erbium"},"v13":{"start":"2019-10-22","maintenance":"2020-04-01","end":"2020-06-01"},"v14":{"start":"2020-04-21","lts":"2020-10-27","maintenance":"2021-10-19","end":"2023-04-30","codename":"Fermium"},"v15":{"start":"2020-10-20","maintenance":"2021-04-01","end":"2021-06-01"},"v16":{"start":"2021-04-20","lts":"2021-10-26","maintenance":"2022-10-18","end":"2023-09-11","codename":"Gallium"},"v17":{"start":"2021-10-19","maintenance":"2022-04-01","end":"2022-06-01"},"v18":{"start":"2022-04-19","lts":"2022-10-25","maintenance":"2023-10-18","end":"2025-04-30","codename":""},"v19":{"start":"2022-10-18","maintenance":"2023-04-01","end":"2023-06-01"},"v20":{"start":"2023-04-18","lts":"2023-10-24","maintenance":"2024-10-22","end":"2026-04-30","codename":""}}')}};var s={};function __nccwpck_require__(r){var n=s[r];if(n!==undefined){return n.exports}var t=s[r]={exports:{}};var a=true;try{e[r](t,t.exports,__nccwpck_require__);a=false}finally{if(a)delete s[r]}return t.exports}(()=>{__nccwpck_require__.o=(e,s)=>Object.prototype.hasOwnProperty.call(e,s)})();if(typeof __nccwpck_require__!=="undefined")__nccwpck_require__.ab=__dirname+"/";var r=__nccwpck_require__(751);module.exports=r})();