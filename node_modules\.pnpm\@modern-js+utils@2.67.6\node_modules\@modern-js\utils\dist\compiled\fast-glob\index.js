(()=>{var t={6491:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.createFileSystemAdapter=e.FILE_SYSTEM_ADAPTER=void 0;const n=r(7147);e.FILE_SYSTEM_ADAPTER={lstat:n.lstat,stat:n.stat,lstatSync:n.lstatSync,statSync:n.statSync,readdir:n.readdir,readdirSync:n.readdirSync};function createFileSystemAdapter(t){if(t===undefined){return e.FILE_SYSTEM_ADAPTER}return Object.assign(Object.assign({},e.FILE_SYSTEM_ADAPTER),t)}e.createFileSystemAdapter=createFileSystemAdapter},6325:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.IS_SUPPORT_READDIR_WITH_FILE_TYPES=void 0;const r=process.versions.node.split(".");if(r[0]===undefined||r[1]===undefined){throw new Error(`Unexpected behavior. The 'process.versions.node' variable has invalid value: ${process.versions.node}`)}const n=Number.parseInt(r[0],10);const s=Number.parseInt(r[1],10);const i=10;const o=10;const a=n>i;const u=n===i&&s>=o;e.IS_SUPPORT_READDIR_WITH_FILE_TYPES=a||u},1659:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.Settings=e.scandirSync=e.scandir=void 0;const n=r(4715);const s=r(8907);const i=r(3486);e.Settings=i.default;function scandir(t,e,r){if(typeof e==="function"){n.read(t,getSettings(),e);return}n.read(t,getSettings(e),r)}e.scandir=scandir;function scandirSync(t,e){const r=getSettings(e);return s.read(t,r)}e.scandirSync=scandirSync;function getSettings(t={}){if(t instanceof i.default){return t}return new i.default(t)}},4715:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.readdir=e.readdirWithFileTypes=e.read=void 0;const n=r(3645);const s=r(5341);const i=r(6325);const o=r(946);const a=r(3253);function read(t,e,r){if(!e.stats&&i.IS_SUPPORT_READDIR_WITH_FILE_TYPES){readdirWithFileTypes(t,e,r);return}readdir(t,e,r)}e.read=read;function readdirWithFileTypes(t,e,r){e.fs.readdir(t,{withFileTypes:true},((n,i)=>{if(n!==null){callFailureCallback(r,n);return}const o=i.map((r=>({dirent:r,name:r.name,path:a.joinPathSegments(t,r.name,e.pathSegmentSeparator)})));if(!e.followSymbolicLinks){callSuccessCallback(r,o);return}const u=o.map((t=>makeRplTaskEntry(t,e)));s(u,((t,e)=>{if(t!==null){callFailureCallback(r,t);return}callSuccessCallback(r,e)}))}))}e.readdirWithFileTypes=readdirWithFileTypes;function makeRplTaskEntry(t,e){return r=>{if(!t.dirent.isSymbolicLink()){r(null,t);return}e.fs.stat(t.path,((n,s)=>{if(n!==null){if(e.throwErrorOnBrokenSymbolicLink){r(n);return}r(null,t);return}t.dirent=o.fs.createDirentFromStats(t.name,s);r(null,t)}))}}function readdir(t,e,r){e.fs.readdir(t,((i,u)=>{if(i!==null){callFailureCallback(r,i);return}const c=u.map((r=>{const s=a.joinPathSegments(t,r,e.pathSegmentSeparator);return t=>{n.stat(s,e.fsStatSettings,((n,i)=>{if(n!==null){t(n);return}const a={name:r,path:s,dirent:o.fs.createDirentFromStats(r,i)};if(e.stats){a.stats=i}t(null,a)}))}}));s(c,((t,e)=>{if(t!==null){callFailureCallback(r,t);return}callSuccessCallback(r,e)}))}))}e.readdir=readdir;function callFailureCallback(t,e){t(e)}function callSuccessCallback(t,e){t(null,e)}},3253:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.joinPathSegments=void 0;function joinPathSegments(t,e,r){if(t.endsWith(r)){return t+e}return t+r+e}e.joinPathSegments=joinPathSegments},8907:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.readdir=e.readdirWithFileTypes=e.read=void 0;const n=r(3645);const s=r(6325);const i=r(946);const o=r(3253);function read(t,e){if(!e.stats&&s.IS_SUPPORT_READDIR_WITH_FILE_TYPES){return readdirWithFileTypes(t,e)}return readdir(t,e)}e.read=read;function readdirWithFileTypes(t,e){const r=e.fs.readdirSync(t,{withFileTypes:true});return r.map((r=>{const n={dirent:r,name:r.name,path:o.joinPathSegments(t,r.name,e.pathSegmentSeparator)};if(n.dirent.isSymbolicLink()&&e.followSymbolicLinks){try{const t=e.fs.statSync(n.path);n.dirent=i.fs.createDirentFromStats(n.name,t)}catch(t){if(e.throwErrorOnBrokenSymbolicLink){throw t}}}return n}))}e.readdirWithFileTypes=readdirWithFileTypes;function readdir(t,e){const r=e.fs.readdirSync(t);return r.map((r=>{const s=o.joinPathSegments(t,r,e.pathSegmentSeparator);const a=n.statSync(s,e.fsStatSettings);const u={name:r,path:s,dirent:i.fs.createDirentFromStats(r,a)};if(e.stats){u.stats=a}return u}))}e.readdir=readdir},3486:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});const n=r(1017);const s=r(3645);const i=r(6491);class Settings{constructor(t={}){this._options=t;this.followSymbolicLinks=this._getValue(this._options.followSymbolicLinks,false);this.fs=i.createFileSystemAdapter(this._options.fs);this.pathSegmentSeparator=this._getValue(this._options.pathSegmentSeparator,n.sep);this.stats=this._getValue(this._options.stats,false);this.throwErrorOnBrokenSymbolicLink=this._getValue(this._options.throwErrorOnBrokenSymbolicLink,true);this.fsStatSettings=new s.Settings({followSymbolicLink:this.followSymbolicLinks,fs:this.fs,throwErrorOnBrokenSymbolicLink:this.throwErrorOnBrokenSymbolicLink})}_getValue(t,e){return t!==null&&t!==void 0?t:e}}e["default"]=Settings},2926:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.createDirentFromStats=void 0;class DirentFromStats{constructor(t,e){this.name=t;this.isBlockDevice=e.isBlockDevice.bind(e);this.isCharacterDevice=e.isCharacterDevice.bind(e);this.isDirectory=e.isDirectory.bind(e);this.isFIFO=e.isFIFO.bind(e);this.isFile=e.isFile.bind(e);this.isSocket=e.isSocket.bind(e);this.isSymbolicLink=e.isSymbolicLink.bind(e)}}function createDirentFromStats(t,e){return new DirentFromStats(t,e)}e.createDirentFromStats=createDirentFromStats},946:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.fs=void 0;const n=r(2926);e.fs=n},2243:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.createFileSystemAdapter=e.FILE_SYSTEM_ADAPTER=void 0;const n=r(7147);e.FILE_SYSTEM_ADAPTER={lstat:n.lstat,stat:n.stat,lstatSync:n.lstatSync,statSync:n.statSync};function createFileSystemAdapter(t){if(t===undefined){return e.FILE_SYSTEM_ADAPTER}return Object.assign(Object.assign({},e.FILE_SYSTEM_ADAPTER),t)}e.createFileSystemAdapter=createFileSystemAdapter},3645:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.statSync=e.stat=e.Settings=void 0;const n=r(6948);const s=r(6750);const i=r(1917);e.Settings=i.default;function stat(t,e,r){if(typeof e==="function"){n.read(t,getSettings(),e);return}n.read(t,getSettings(e),r)}e.stat=stat;function statSync(t,e){const r=getSettings(e);return s.read(t,r)}e.statSync=statSync;function getSettings(t={}){if(t instanceof i.default){return t}return new i.default(t)}},6948:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.read=void 0;function read(t,e,r){e.fs.lstat(t,((n,s)=>{if(n!==null){callFailureCallback(r,n);return}if(!s.isSymbolicLink()||!e.followSymbolicLink){callSuccessCallback(r,s);return}e.fs.stat(t,((t,n)=>{if(t!==null){if(e.throwErrorOnBrokenSymbolicLink){callFailureCallback(r,t);return}callSuccessCallback(r,s);return}if(e.markSymbolicLink){n.isSymbolicLink=()=>true}callSuccessCallback(r,n)}))}))}e.read=read;function callFailureCallback(t,e){t(e)}function callSuccessCallback(t,e){t(null,e)}},6750:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.read=void 0;function read(t,e){const r=e.fs.lstatSync(t);if(!r.isSymbolicLink()||!e.followSymbolicLink){return r}try{const r=e.fs.statSync(t);if(e.markSymbolicLink){r.isSymbolicLink=()=>true}return r}catch(t){if(!e.throwErrorOnBrokenSymbolicLink){return r}throw t}}e.read=read},1917:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});const n=r(2243);class Settings{constructor(t={}){this._options=t;this.followSymbolicLink=this._getValue(this._options.followSymbolicLink,true);this.fs=n.createFileSystemAdapter(this._options.fs);this.markSymbolicLink=this._getValue(this._options.markSymbolicLink,false);this.throwErrorOnBrokenSymbolicLink=this._getValue(this._options.throwErrorOnBrokenSymbolicLink,true)}_getValue(t,e){return t!==null&&t!==void 0?t:e}}e["default"]=Settings},4328:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.Settings=e.walkStream=e.walkSync=e.walk=void 0;const n=r(4306);const s=r(2686);const i=r(8918);const o=r(8831);e.Settings=o.default;function walk(t,e,r){if(typeof e==="function"){new n.default(t,getSettings()).read(e);return}new n.default(t,getSettings(e)).read(r)}e.walk=walk;function walkSync(t,e){const r=getSettings(e);const n=new i.default(t,r);return n.read()}e.walkSync=walkSync;function walkStream(t,e){const r=getSettings(e);const n=new s.default(t,r);return n.read()}e.walkStream=walkStream;function getSettings(t={}){if(t instanceof o.default){return t}return new o.default(t)}},4306:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});const n=r(1293);class AsyncProvider{constructor(t,e){this._root=t;this._settings=e;this._reader=new n.default(this._root,this._settings);this._storage=[]}read(t){this._reader.onError((e=>{callFailureCallback(t,e)}));this._reader.onEntry((t=>{this._storage.push(t)}));this._reader.onEnd((()=>{callSuccessCallback(t,this._storage)}));this._reader.read()}}e["default"]=AsyncProvider;function callFailureCallback(t,e){t(e)}function callSuccessCallback(t,e){t(null,e)}},2686:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});const n=r(2781);const s=r(1293);class StreamProvider{constructor(t,e){this._root=t;this._settings=e;this._reader=new s.default(this._root,this._settings);this._stream=new n.Readable({objectMode:true,read:()=>{},destroy:()=>{if(!this._reader.isDestroyed){this._reader.destroy()}}})}read(){this._reader.onError((t=>{this._stream.emit("error",t)}));this._reader.onEntry((t=>{this._stream.push(t)}));this._reader.onEnd((()=>{this._stream.push(null)}));this._reader.read();return this._stream}}e["default"]=StreamProvider},8918:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});const n=r(9476);class SyncProvider{constructor(t,e){this._root=t;this._settings=e;this._reader=new n.default(this._root,this._settings)}read(){return this._reader.read()}}e["default"]=SyncProvider},1293:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});const n=r(2361);const s=r(1659);const i=r(4241);const o=r(1652);const a=r(8376);class AsyncReader extends a.default{constructor(t,e){super(t,e);this._settings=e;this._scandir=s.scandir;this._emitter=new n.EventEmitter;this._queue=i(this._worker.bind(this),this._settings.concurrency);this._isFatalError=false;this._isDestroyed=false;this._queue.drain=()=>{if(!this._isFatalError){this._emitter.emit("end")}}}read(){this._isFatalError=false;this._isDestroyed=false;setImmediate((()=>{this._pushToQueue(this._root,this._settings.basePath)}));return this._emitter}get isDestroyed(){return this._isDestroyed}destroy(){if(this._isDestroyed){throw new Error("The reader is already destroyed")}this._isDestroyed=true;this._queue.killAndDrain()}onEntry(t){this._emitter.on("entry",t)}onError(t){this._emitter.once("error",t)}onEnd(t){this._emitter.once("end",t)}_pushToQueue(t,e){const r={directory:t,base:e};this._queue.push(r,(t=>{if(t!==null){this._handleError(t)}}))}_worker(t,e){this._scandir(t.directory,this._settings.fsScandirSettings,((r,n)=>{if(r!==null){e(r,undefined);return}for(const e of n){this._handleEntry(e,t.base)}e(null,undefined)}))}_handleError(t){if(this._isDestroyed||!o.isFatalError(this._settings,t)){return}this._isFatalError=true;this._isDestroyed=true;this._emitter.emit("error",t)}_handleEntry(t,e){if(this._isDestroyed||this._isFatalError){return}const r=t.path;if(e!==undefined){t.path=o.joinPathSegments(e,t.name,this._settings.pathSegmentSeparator)}if(o.isAppliedFilter(this._settings.entryFilter,t)){this._emitEntry(t)}if(t.dirent.isDirectory()&&o.isAppliedFilter(this._settings.deepFilter,t)){this._pushToQueue(r,e===undefined?undefined:t.path)}}_emitEntry(t){this._emitter.emit("entry",t)}}e["default"]=AsyncReader},1652:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.joinPathSegments=e.replacePathSegmentSeparator=e.isAppliedFilter=e.isFatalError=void 0;function isFatalError(t,e){if(t.errorFilter===null){return true}return!t.errorFilter(e)}e.isFatalError=isFatalError;function isAppliedFilter(t,e){return t===null||t(e)}e.isAppliedFilter=isAppliedFilter;function replacePathSegmentSeparator(t,e){return t.split(/[/\\]/).join(e)}e.replacePathSegmentSeparator=replacePathSegmentSeparator;function joinPathSegments(t,e,r){if(t===""){return e}if(t.endsWith(r)){return t+e}return t+r+e}e.joinPathSegments=joinPathSegments},8376:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});const n=r(1652);class Reader{constructor(t,e){this._root=t;this._settings=e;this._root=n.replacePathSegmentSeparator(t,e.pathSegmentSeparator)}}e["default"]=Reader},9476:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});const n=r(1659);const s=r(1652);const i=r(8376);class SyncReader extends i.default{constructor(){super(...arguments);this._scandir=n.scandirSync;this._storage=[];this._queue=new Set}read(){this._pushToQueue(this._root,this._settings.basePath);this._handleQueue();return this._storage}_pushToQueue(t,e){this._queue.add({directory:t,base:e})}_handleQueue(){for(const t of this._queue.values()){this._handleDirectory(t.directory,t.base)}}_handleDirectory(t,e){try{const r=this._scandir(t,this._settings.fsScandirSettings);for(const t of r){this._handleEntry(t,e)}}catch(t){this._handleError(t)}}_handleError(t){if(!s.isFatalError(this._settings,t)){return}throw t}_handleEntry(t,e){const r=t.path;if(e!==undefined){t.path=s.joinPathSegments(e,t.name,this._settings.pathSegmentSeparator)}if(s.isAppliedFilter(this._settings.entryFilter,t)){this._pushToStorage(t)}if(t.dirent.isDirectory()&&s.isAppliedFilter(this._settings.deepFilter,t)){this._pushToQueue(r,e===undefined?undefined:t.path)}}_pushToStorage(t){this._storage.push(t)}}e["default"]=SyncReader},8831:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});const n=r(1017);const s=r(1659);class Settings{constructor(t={}){this._options=t;this.basePath=this._getValue(this._options.basePath,undefined);this.concurrency=this._getValue(this._options.concurrency,Number.POSITIVE_INFINITY);this.deepFilter=this._getValue(this._options.deepFilter,null);this.entryFilter=this._getValue(this._options.entryFilter,null);this.errorFilter=this._getValue(this._options.errorFilter,null);this.pathSegmentSeparator=this._getValue(this._options.pathSegmentSeparator,n.sep);this.fsScandirSettings=new s.Settings({followSymbolicLinks:this._options.followSymbolicLinks,fs:this._options.fs,pathSegmentSeparator:this._options.pathSegmentSeparator,stats:this._options.stats,throwErrorOnBrokenSymbolicLink:this._options.throwErrorOnBrokenSymbolicLink})}_getValue(t,e){return t!==null&&t!==void 0?t:e}}e["default"]=Settings},1570:(t,e,r)=>{"use strict";const n=r(3625);const s=r(4666);const i=r(6183);const o=r(2467);const braces=(t,e={})=>{let r=[];if(Array.isArray(t)){for(let n of t){let t=braces.create(n,e);if(Array.isArray(t)){r.push(...t)}else{r.push(t)}}}else{r=[].concat(braces.create(t,e))}if(e&&e.expand===true&&e.nodupes===true){r=[...new Set(r)]}return r};braces.parse=(t,e={})=>o(t,e);braces.stringify=(t,e={})=>{if(typeof t==="string"){return n(braces.parse(t,e),e)}return n(t,e)};braces.compile=(t,e={})=>{if(typeof t==="string"){t=braces.parse(t,e)}return s(t,e)};braces.expand=(t,e={})=>{if(typeof t==="string"){t=braces.parse(t,e)}let r=i(t,e);if(e.noempty===true){r=r.filter(Boolean)}if(e.nodupes===true){r=[...new Set(r)]}return r};braces.create=(t,e={})=>{if(t===""||t.length<3){return[t]}return e.expand!==true?braces.compile(t,e):braces.expand(t,e)};t.exports=braces},4666:(t,e,r)=>{"use strict";const n=r(442);const s=r(2456);const compile=(t,e={})=>{let walk=(t,r={})=>{let i=s.isInvalidBrace(r);let o=t.invalid===true&&e.escapeInvalid===true;let a=i===true||o===true;let u=e.escapeInvalid===true?"\\":"";let c="";if(t.isOpen===true){return u+t.value}if(t.isClose===true){return u+t.value}if(t.type==="open"){return a?u+t.value:"("}if(t.type==="close"){return a?u+t.value:")"}if(t.type==="comma"){return t.prev.type==="comma"?"":a?t.value:"|"}if(t.value){return t.value}if(t.nodes&&t.ranges>0){let r=s.reduce(t.nodes);let i=n(...r,{...e,wrap:false,toRegex:true});if(i.length!==0){return r.length>1&&i.length>1?`(${i})`:i}}if(t.nodes){for(let e of t.nodes){c+=walk(e,t)}}return c};return walk(t)};t.exports=compile},487:t=>{"use strict";t.exports={MAX_LENGTH:1024*64,CHAR_0:"0",CHAR_9:"9",CHAR_UPPERCASE_A:"A",CHAR_LOWERCASE_A:"a",CHAR_UPPERCASE_Z:"Z",CHAR_LOWERCASE_Z:"z",CHAR_LEFT_PARENTHESES:"(",CHAR_RIGHT_PARENTHESES:")",CHAR_ASTERISK:"*",CHAR_AMPERSAND:"&",CHAR_AT:"@",CHAR_BACKSLASH:"\\",CHAR_BACKTICK:"`",CHAR_CARRIAGE_RETURN:"\r",CHAR_CIRCUMFLEX_ACCENT:"^",CHAR_COLON:":",CHAR_COMMA:",",CHAR_DOLLAR:"$",CHAR_DOT:".",CHAR_DOUBLE_QUOTE:'"',CHAR_EQUAL:"=",CHAR_EXCLAMATION_MARK:"!",CHAR_FORM_FEED:"\f",CHAR_FORWARD_SLASH:"/",CHAR_HASH:"#",CHAR_HYPHEN_MINUS:"-",CHAR_LEFT_ANGLE_BRACKET:"<",CHAR_LEFT_CURLY_BRACE:"{",CHAR_LEFT_SQUARE_BRACKET:"[",CHAR_LINE_FEED:"\n",CHAR_NO_BREAK_SPACE:" ",CHAR_PERCENT:"%",CHAR_PLUS:"+",CHAR_QUESTION_MARK:"?",CHAR_RIGHT_ANGLE_BRACKET:">",CHAR_RIGHT_CURLY_BRACE:"}",CHAR_RIGHT_SQUARE_BRACKET:"]",CHAR_SEMICOLON:";",CHAR_SINGLE_QUOTE:"'",CHAR_SPACE:" ",CHAR_TAB:"\t",CHAR_UNDERSCORE:"_",CHAR_VERTICAL_LINE:"|",CHAR_ZERO_WIDTH_NOBREAK_SPACE:"\ufeff"}},6183:(t,e,r)=>{"use strict";const n=r(442);const s=r(3625);const i=r(2456);const append=(t="",e="",r=false)=>{let n=[];t=[].concat(t);e=[].concat(e);if(!e.length)return t;if(!t.length){return r?i.flatten(e).map((t=>`{${t}}`)):e}for(let s of t){if(Array.isArray(s)){for(let t of s){n.push(append(t,e,r))}}else{for(let t of e){if(r===true&&typeof t==="string")t=`{${t}}`;n.push(Array.isArray(t)?append(s,t,r):s+t)}}}return i.flatten(n)};const expand=(t,e={})=>{let r=e.rangeLimit===void 0?1e3:e.rangeLimit;let walk=(t,o={})=>{t.queue=[];let a=o;let u=o.queue;while(a.type!=="brace"&&a.type!=="root"&&a.parent){a=a.parent;u=a.queue}if(t.invalid||t.dollar){u.push(append(u.pop(),s(t,e)));return}if(t.type==="brace"&&t.invalid!==true&&t.nodes.length===2){u.push(append(u.pop(),["{}"]));return}if(t.nodes&&t.ranges>0){let o=i.reduce(t.nodes);if(i.exceedsLimit(...o,e.step,r)){throw new RangeError("expanded array length exceeds range limit. Use options.rangeLimit to increase or disable the limit.")}let a=n(...o,e);if(a.length===0){a=s(t,e)}u.push(append(u.pop(),a));t.nodes=[];return}let c=i.encloseBrace(t);let l=t.queue;let f=t;while(f.type!=="brace"&&f.type!=="root"&&f.parent){f=f.parent;l=f.queue}for(let e=0;e<t.nodes.length;e++){let r=t.nodes[e];if(r.type==="comma"&&t.type==="brace"){if(e===1)l.push("");l.push("");continue}if(r.type==="close"){u.push(append(u.pop(),l,c));continue}if(r.value&&r.type!=="open"){l.push(append(l.pop(),r.value));continue}if(r.nodes){walk(r,t)}}return l};return i.flatten(walk(t))};t.exports=expand},2467:(t,e,r)=>{"use strict";const n=r(3625);const{MAX_LENGTH:s,CHAR_BACKSLASH:i,CHAR_BACKTICK:o,CHAR_COMMA:a,CHAR_DOT:u,CHAR_LEFT_PARENTHESES:c,CHAR_RIGHT_PARENTHESES:l,CHAR_LEFT_CURLY_BRACE:f,CHAR_RIGHT_CURLY_BRACE:p,CHAR_LEFT_SQUARE_BRACKET:h,CHAR_RIGHT_SQUARE_BRACKET:d,CHAR_DOUBLE_QUOTE:_,CHAR_SINGLE_QUOTE:g,CHAR_NO_BREAK_SPACE:y,CHAR_ZERO_WIDTH_NOBREAK_SPACE:S}=r(487);const parse=(t,e={})=>{if(typeof t!=="string"){throw new TypeError("Expected a string")}let r=e||{};let m=typeof r.maxLength==="number"?Math.min(s,r.maxLength):s;if(t.length>m){throw new SyntaxError(`Input length (${t.length}), exceeds max characters (${m})`)}let v={type:"root",input:t,nodes:[]};let E=[v];let A=v;let b=v;let R=0;let P=t.length;let x=0;let k=0;let T;let C={};const advance=()=>t[x++];const push=t=>{if(t.type==="text"&&b.type==="dot"){b.type="text"}if(b&&b.type==="text"&&t.type==="text"){b.value+=t.value;return}A.nodes.push(t);t.parent=A;t.prev=b;b=t;return t};push({type:"bos"});while(x<P){A=E[E.length-1];T=advance();if(T===S||T===y){continue}if(T===i){push({type:"text",value:(e.keepEscaping?T:"")+advance()});continue}if(T===d){push({type:"text",value:"\\"+T});continue}if(T===h){R++;let t=true;let e;while(x<P&&(e=advance())){T+=e;if(e===h){R++;continue}if(e===i){T+=advance();continue}if(e===d){R--;if(R===0){break}}}push({type:"text",value:T});continue}if(T===c){A=push({type:"paren",nodes:[]});E.push(A);push({type:"text",value:T});continue}if(T===l){if(A.type!=="paren"){push({type:"text",value:T});continue}A=E.pop();push({type:"text",value:T});A=E[E.length-1];continue}if(T===_||T===g||T===o){let t=T;let r;if(e.keepQuotes!==true){T=""}while(x<P&&(r=advance())){if(r===i){T+=r+advance();continue}if(r===t){if(e.keepQuotes===true)T+=r;break}T+=r}push({type:"text",value:T});continue}if(T===f){k++;let t=b.value&&b.value.slice(-1)==="$"||A.dollar===true;let e={type:"brace",open:true,close:false,dollar:t,depth:k,commas:0,ranges:0,nodes:[]};A=push(e);E.push(A);push({type:"open",value:T});continue}if(T===p){if(A.type!=="brace"){push({type:"text",value:T});continue}let t="close";A=E.pop();A.close=true;push({type:t,value:T});k--;A=E[E.length-1];continue}if(T===a&&k>0){if(A.ranges>0){A.ranges=0;let t=A.nodes.shift();A.nodes=[t,{type:"text",value:n(A)}]}push({type:"comma",value:T});A.commas++;continue}if(T===u&&k>0&&A.commas===0){let t=A.nodes;if(k===0||t.length===0){push({type:"text",value:T});continue}if(b.type==="dot"){A.range=[];b.value+=T;b.type="range";if(A.nodes.length!==3&&A.nodes.length!==5){A.invalid=true;A.ranges=0;b.type="text";continue}A.ranges++;A.args=[];continue}if(b.type==="range"){t.pop();let e=t[t.length-1];e.value+=b.value+T;b=e;A.ranges--;continue}push({type:"dot",value:T});continue}push({type:"text",value:T})}do{A=E.pop();if(A.type!=="root"){A.nodes.forEach((t=>{if(!t.nodes){if(t.type==="open")t.isOpen=true;if(t.type==="close")t.isClose=true;if(!t.nodes)t.type="text";t.invalid=true}}));let t=E[E.length-1];let e=t.nodes.indexOf(A);t.nodes.splice(e,1,...A.nodes)}}while(E.length>0);push({type:"eos"});return v};t.exports=parse},3625:(t,e,r)=>{"use strict";const n=r(2456);t.exports=(t,e={})=>{let stringify=(t,r={})=>{let s=e.escapeInvalid&&n.isInvalidBrace(r);let i=t.invalid===true&&e.escapeInvalid===true;let o="";if(t.value){if((s||i)&&n.isOpenOrClose(t)){return"\\"+t.value}return t.value}if(t.value){return t.value}if(t.nodes){for(let e of t.nodes){o+=stringify(e)}}return o};return stringify(t)}},2456:(t,e)=>{"use strict";e.isInteger=t=>{if(typeof t==="number"){return Number.isInteger(t)}if(typeof t==="string"&&t.trim()!==""){return Number.isInteger(Number(t))}return false};e.find=(t,e)=>t.nodes.find((t=>t.type===e));e.exceedsLimit=(t,r,n=1,s)=>{if(s===false)return false;if(!e.isInteger(t)||!e.isInteger(r))return false;return(Number(r)-Number(t))/Number(n)>=s};e.escapeNode=(t,e=0,r)=>{let n=t.nodes[e];if(!n)return;if(r&&n.type===r||n.type==="open"||n.type==="close"){if(n.escaped!==true){n.value="\\"+n.value;n.escaped=true}}};e.encloseBrace=t=>{if(t.type!=="brace")return false;if(t.commas>>0+t.ranges>>0===0){t.invalid=true;return true}return false};e.isInvalidBrace=t=>{if(t.type!=="brace")return false;if(t.invalid===true||t.dollar)return true;if(t.commas>>0+t.ranges>>0===0){t.invalid=true;return true}if(t.open!==true||t.close!==true){t.invalid=true;return true}return false};e.isOpenOrClose=t=>{if(t.type==="open"||t.type==="close"){return true}return t.open===true||t.close===true};e.reduce=t=>t.reduce(((t,e)=>{if(e.type==="text")t.push(e.value);if(e.type==="range")e.type="text";return t}),[]);e.flatten=(...t)=>{const e=[];const flat=t=>{for(let r=0;r<t.length;r++){let n=t[r];Array.isArray(n)?flat(n,e):n!==void 0&&e.push(n)}return e};flat(t);return e}},3924:(t,e,r)=>{"use strict";const n=r(2291);const s=r(8620);const i=r(7900);const o=r(5407);const a=r(1066);const u=r(7190);const c=r(3828);async function FastGlob(t,e){assertPatternsInput(t);const r=getWorks(t,i.default,e);const n=await Promise.all(r);return c.array.flatten(n)}(function(t){function sync(t,e){assertPatternsInput(t);const r=getWorks(t,a.default,e);return c.array.flatten(r)}t.sync=sync;function stream(t,e){assertPatternsInput(t);const r=getWorks(t,o.default,e);return c.stream.merge(r)}t.stream=stream;function generateTasks(t,e){assertPatternsInput(t);const r=s.transform([].concat(t));const i=new u.default(e);return n.generate(r,i)}t.generateTasks=generateTasks;function isDynamicPattern(t,e){assertPatternsInput(t);const r=new u.default(e);return c.pattern.isDynamicPattern(t,r)}t.isDynamicPattern=isDynamicPattern;function escapePath(t){assertPatternsInput(t);return c.path.escape(t)}t.escapePath=escapePath})(FastGlob||(FastGlob={}));function getWorks(t,e,r){const i=s.transform([].concat(t));const o=new u.default(r);const a=n.generate(i,o);const c=new e(o);return a.map(c.read,c)}function assertPatternsInput(t){const e=[].concat(t);const r=e.every((t=>c.string.isString(t)&&!c.string.isEmpty(t)));if(!r){throw new TypeError("Patterns must be a string (non empty) or an array of strings")}}t.exports=FastGlob},8620:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.removeDuplicateSlashes=e.transform=void 0;const r=/(?!^)\/{2,}/g;function transform(t){return t.map((t=>removeDuplicateSlashes(t)))}e.transform=transform;function removeDuplicateSlashes(t){return t.replace(r,"/")}e.removeDuplicateSlashes=removeDuplicateSlashes},2291:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.convertPatternGroupToTask=e.convertPatternGroupsToTasks=e.groupPatternsByBaseDirectory=e.getNegativePatternsAsPositive=e.getPositivePatterns=e.convertPatternsToTasks=e.generate=void 0;const n=r(3828);function generate(t,e){const r=getPositivePatterns(t);const s=getNegativePatternsAsPositive(t,e.ignore);const i=r.filter((t=>n.pattern.isStaticPattern(t,e)));const o=r.filter((t=>n.pattern.isDynamicPattern(t,e)));const a=convertPatternsToTasks(i,s,false);const u=convertPatternsToTasks(o,s,true);return a.concat(u)}e.generate=generate;function convertPatternsToTasks(t,e,r){const s=[];const i=n.pattern.getPatternsOutsideCurrentDirectory(t);const o=n.pattern.getPatternsInsideCurrentDirectory(t);const a=groupPatternsByBaseDirectory(i);const u=groupPatternsByBaseDirectory(o);s.push(...convertPatternGroupsToTasks(a,e,r));if("."in u){s.push(convertPatternGroupToTask(".",o,e,r))}else{s.push(...convertPatternGroupsToTasks(u,e,r))}return s}e.convertPatternsToTasks=convertPatternsToTasks;function getPositivePatterns(t){return n.pattern.getPositivePatterns(t)}e.getPositivePatterns=getPositivePatterns;function getNegativePatternsAsPositive(t,e){const r=n.pattern.getNegativePatterns(t).concat(e);const s=r.map(n.pattern.convertToPositivePattern);return s}e.getNegativePatternsAsPositive=getNegativePatternsAsPositive;function groupPatternsByBaseDirectory(t){const e={};return t.reduce(((t,e)=>{const r=n.pattern.getBaseDirectory(e);if(r in t){t[r].push(e)}else{t[r]=[e]}return t}),e)}e.groupPatternsByBaseDirectory=groupPatternsByBaseDirectory;function convertPatternGroupsToTasks(t,e,r){return Object.keys(t).map((n=>convertPatternGroupToTask(n,t[n],e,r)))}e.convertPatternGroupsToTasks=convertPatternGroupsToTasks;function convertPatternGroupToTask(t,e,r,s){return{dynamic:s,positive:e,negative:r,base:t,patterns:[].concat(e,r.map(n.pattern.convertToNegativePattern))}}e.convertPatternGroupToTask=convertPatternGroupToTask},7900:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});const n=r(8075);const s=r(8845);class ProviderAsync extends s.default{constructor(){super(...arguments);this._reader=new n.default(this._settings)}read(t){const e=this._getRootDirectory(t);const r=this._getReaderOptions(t);const n=[];return new Promise(((s,i)=>{const o=this.api(e,t,r);o.once("error",i);o.on("data",(t=>n.push(r.transform(t))));o.once("end",(()=>s(n)))}))}api(t,e,r){if(e.dynamic){return this._reader.dynamic(t,r)}return this._reader.static(e.patterns,r)}}e["default"]=ProviderAsync},4292:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});const n=r(3828);const s=r(5995);class DeepFilter{constructor(t,e){this._settings=t;this._micromatchOptions=e}getFilter(t,e,r){const n=this._getMatcher(e);const s=this._getNegativePatternsRe(r);return e=>this._filter(t,e,n,s)}_getMatcher(t){return new s.default(t,this._settings,this._micromatchOptions)}_getNegativePatternsRe(t){const e=t.filter(n.pattern.isAffectDepthOfReadingPattern);return n.pattern.convertPatternsToRe(e,this._micromatchOptions)}_filter(t,e,r,s){if(this._isSkippedByDeep(t,e.path)){return false}if(this._isSkippedSymbolicLink(e)){return false}const i=n.path.removeLeadingDotSegment(e.path);if(this._isSkippedByPositivePatterns(i,r)){return false}return this._isSkippedByNegativePatterns(i,s)}_isSkippedByDeep(t,e){if(this._settings.deep===Infinity){return false}return this._getEntryLevel(t,e)>=this._settings.deep}_getEntryLevel(t,e){const r=e.split("/").length;if(t===""){return r}const n=t.split("/").length;return r-n}_isSkippedSymbolicLink(t){return!this._settings.followSymbolicLinks&&t.dirent.isSymbolicLink()}_isSkippedByPositivePatterns(t,e){return!this._settings.baseNameMatch&&!e.match(t)}_isSkippedByNegativePatterns(t,e){return!n.pattern.matchAny(t,e)}}e["default"]=DeepFilter},659:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});const n=r(3828);class EntryFilter{constructor(t,e){this._settings=t;this._micromatchOptions=e;this.index=new Map}getFilter(t,e){const r=n.pattern.convertPatternsToRe(t,this._micromatchOptions);const s=n.pattern.convertPatternsToRe(e,this._micromatchOptions);return t=>this._filter(t,r,s)}_filter(t,e,r){if(this._settings.unique&&this._isDuplicateEntry(t)){return false}if(this._onlyFileFilter(t)||this._onlyDirectoryFilter(t)){return false}if(this._isSkippedByAbsoluteNegativePatterns(t.path,r)){return false}const n=this._settings.baseNameMatch?t.name:t.path;const s=this._isMatchToPatterns(n,e)&&!this._isMatchToPatterns(t.path,r);if(this._settings.unique&&s){this._createIndexRecord(t)}return s}_isDuplicateEntry(t){return this.index.has(t.path)}_createIndexRecord(t){this.index.set(t.path,undefined)}_onlyFileFilter(t){return this._settings.onlyFiles&&!t.dirent.isFile()}_onlyDirectoryFilter(t){return this._settings.onlyDirectories&&!t.dirent.isDirectory()}_isSkippedByAbsoluteNegativePatterns(t,e){if(!this._settings.absolute){return false}const r=n.path.makeAbsolute(this._settings.cwd,t);return n.pattern.matchAny(r,e)}_isMatchToPatterns(t,e){const r=n.path.removeLeadingDotSegment(t);return n.pattern.matchAny(r,e)||n.pattern.matchAny(r+"/",e)}}e["default"]=EntryFilter},9558:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});const n=r(3828);class ErrorFilter{constructor(t){this._settings=t}getFilter(){return t=>this._isNonFatalError(t)}_isNonFatalError(t){return n.errno.isEnoentCodeError(t)||this._settings.suppressErrors}}e["default"]=ErrorFilter},8302:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});const n=r(3828);class Matcher{constructor(t,e,r){this._patterns=t;this._settings=e;this._micromatchOptions=r;this._storage=[];this._fillStorage()}_fillStorage(){const t=n.pattern.expandPatternsWithBraceExpansion(this._patterns);for(const e of t){const t=this._getPatternSegments(e);const r=this._splitSegmentsIntoSections(t);this._storage.push({complete:r.length<=1,pattern:e,segments:t,sections:r})}}_getPatternSegments(t){const e=n.pattern.getPatternParts(t,this._micromatchOptions);return e.map((t=>{const e=n.pattern.isDynamicPattern(t,this._settings);if(!e){return{dynamic:false,pattern:t}}return{dynamic:true,pattern:t,patternRe:n.pattern.makeRe(t,this._micromatchOptions)}}))}_splitSegmentsIntoSections(t){return n.array.splitWhen(t,(t=>t.dynamic&&n.pattern.hasGlobStar(t.pattern)))}}e["default"]=Matcher},5995:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});const n=r(8302);class PartialMatcher extends n.default{match(t){const e=t.split("/");const r=e.length;const n=this._storage.filter((t=>!t.complete||t.segments.length>r));for(const t of n){const n=t.sections[0];if(!t.complete&&r>n.length){return true}const s=e.every(((e,r)=>{const n=t.segments[r];if(n.dynamic&&n.patternRe.test(e)){return true}if(!n.dynamic&&n.pattern===e){return true}return false}));if(s){return true}}return false}}e["default"]=PartialMatcher},8845:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});const n=r(1017);const s=r(4292);const i=r(659);const o=r(9558);const a=r(8460);class Provider{constructor(t){this._settings=t;this.errorFilter=new o.default(this._settings);this.entryFilter=new i.default(this._settings,this._getMicromatchOptions());this.deepFilter=new s.default(this._settings,this._getMicromatchOptions());this.entryTransformer=new a.default(this._settings)}_getRootDirectory(t){return n.resolve(this._settings.cwd,t.base)}_getReaderOptions(t){const e=t.base==="."?"":t.base;return{basePath:e,pathSegmentSeparator:"/",concurrency:this._settings.concurrency,deepFilter:this.deepFilter.getFilter(e,t.positive,t.negative),entryFilter:this.entryFilter.getFilter(t.positive,t.negative),errorFilter:this.errorFilter.getFilter(),followSymbolicLinks:this._settings.followSymbolicLinks,fs:this._settings.fs,stats:this._settings.stats,throwErrorOnBrokenSymbolicLink:this._settings.throwErrorOnBrokenSymbolicLink,transform:this.entryTransformer.getTransformer()}}_getMicromatchOptions(){return{dot:this._settings.dot,matchBase:this._settings.baseNameMatch,nobrace:!this._settings.braceExpansion,nocase:!this._settings.caseSensitiveMatch,noext:!this._settings.extglob,noglobstar:!this._settings.globstar,posix:true,strictSlashes:false}}}e["default"]=Provider},5407:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});const n=r(2781);const s=r(8075);const i=r(8845);class ProviderStream extends i.default{constructor(){super(...arguments);this._reader=new s.default(this._settings)}read(t){const e=this._getRootDirectory(t);const r=this._getReaderOptions(t);const s=this.api(e,t,r);const i=new n.Readable({objectMode:true,read:()=>{}});s.once("error",(t=>i.emit("error",t))).on("data",(t=>i.emit("data",r.transform(t)))).once("end",(()=>i.emit("end")));i.once("close",(()=>s.destroy()));return i}api(t,e,r){if(e.dynamic){return this._reader.dynamic(t,r)}return this._reader.static(e.patterns,r)}}e["default"]=ProviderStream},1066:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});const n=r(8110);const s=r(8845);class ProviderSync extends s.default{constructor(){super(...arguments);this._reader=new n.default(this._settings)}read(t){const e=this._getRootDirectory(t);const r=this._getReaderOptions(t);const n=this.api(e,t,r);return n.map(r.transform)}api(t,e,r){if(e.dynamic){return this._reader.dynamic(t,r)}return this._reader.static(e.patterns,r)}}e["default"]=ProviderSync},8460:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});const n=r(3828);class EntryTransformer{constructor(t){this._settings=t}getTransformer(){return t=>this._transform(t)}_transform(t){let e=t.path;if(this._settings.absolute){e=n.path.makeAbsolute(this._settings.cwd,e);e=n.path.unixify(e)}if(this._settings.markDirectories&&t.dirent.isDirectory()){e+="/"}if(!this._settings.objectMode){return e}return Object.assign(Object.assign({},t),{path:e})}}e["default"]=EntryTransformer},5906:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});const n=r(1017);const s=r(3645);const i=r(3828);class Reader{constructor(t){this._settings=t;this._fsStatSettings=new s.Settings({followSymbolicLink:this._settings.followSymbolicLinks,fs:this._settings.fs,throwErrorOnBrokenSymbolicLink:this._settings.followSymbolicLinks})}_getFullEntryPath(t){return n.resolve(this._settings.cwd,t)}_makeEntry(t,e){const r={name:e,path:e,dirent:i.fs.createDirentFromStats(e,t)};if(this._settings.stats){r.stats=t}return r}_isFatalError(t){return!i.errno.isEnoentCodeError(t)&&!this._settings.suppressErrors}}e["default"]=Reader},8075:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});const n=r(2781);const s=r(3645);const i=r(4328);const o=r(5906);class ReaderStream extends o.default{constructor(){super(...arguments);this._walkStream=i.walkStream;this._stat=s.stat}dynamic(t,e){return this._walkStream(t,e)}static(t,e){const r=t.map(this._getFullEntryPath,this);const s=new n.PassThrough({objectMode:true});s._write=(n,i,o)=>this._getEntry(r[n],t[n],e).then((t=>{if(t!==null&&e.entryFilter(t)){s.push(t)}if(n===r.length-1){s.end()}o()})).catch(o);for(let t=0;t<r.length;t++){s.write(t)}return s}_getEntry(t,e,r){return this._getStat(t).then((t=>this._makeEntry(t,e))).catch((t=>{if(r.errorFilter(t)){return null}throw t}))}_getStat(t){return new Promise(((e,r)=>{this._stat(t,this._fsStatSettings,((t,n)=>t===null?e(n):r(t)))}))}}e["default"]=ReaderStream},8110:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});const n=r(3645);const s=r(4328);const i=r(5906);class ReaderSync extends i.default{constructor(){super(...arguments);this._walkSync=s.walkSync;this._statSync=n.statSync}dynamic(t,e){return this._walkSync(t,e)}static(t,e){const r=[];for(const n of t){const t=this._getFullEntryPath(n);const s=this._getEntry(t,n,e);if(s===null||!e.entryFilter(s)){continue}r.push(s)}return r}_getEntry(t,e,r){try{const r=this._getStat(t);return this._makeEntry(r,e)}catch(t){if(r.errorFilter(t)){return null}throw t}}_getStat(t){return this._statSync(t,this._fsStatSettings)}}e["default"]=ReaderSync},7190:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.DEFAULT_FILE_SYSTEM_ADAPTER=void 0;const n=r(7147);const s=r(2037);const i=Math.max(s.cpus().length,1);e.DEFAULT_FILE_SYSTEM_ADAPTER={lstat:n.lstat,lstatSync:n.lstatSync,stat:n.stat,statSync:n.statSync,readdir:n.readdir,readdirSync:n.readdirSync};class Settings{constructor(t={}){this._options=t;this.absolute=this._getValue(this._options.absolute,false);this.baseNameMatch=this._getValue(this._options.baseNameMatch,false);this.braceExpansion=this._getValue(this._options.braceExpansion,true);this.caseSensitiveMatch=this._getValue(this._options.caseSensitiveMatch,true);this.concurrency=this._getValue(this._options.concurrency,i);this.cwd=this._getValue(this._options.cwd,process.cwd());this.deep=this._getValue(this._options.deep,Infinity);this.dot=this._getValue(this._options.dot,false);this.extglob=this._getValue(this._options.extglob,true);this.followSymbolicLinks=this._getValue(this._options.followSymbolicLinks,true);this.fs=this._getFileSystemMethods(this._options.fs);this.globstar=this._getValue(this._options.globstar,true);this.ignore=this._getValue(this._options.ignore,[]);this.markDirectories=this._getValue(this._options.markDirectories,false);this.objectMode=this._getValue(this._options.objectMode,false);this.onlyDirectories=this._getValue(this._options.onlyDirectories,false);this.onlyFiles=this._getValue(this._options.onlyFiles,true);this.stats=this._getValue(this._options.stats,false);this.suppressErrors=this._getValue(this._options.suppressErrors,false);this.throwErrorOnBrokenSymbolicLink=this._getValue(this._options.throwErrorOnBrokenSymbolicLink,false);this.unique=this._getValue(this._options.unique,true);if(this.onlyDirectories){this.onlyFiles=false}if(this.stats){this.objectMode=true}}_getValue(t,e){return t===undefined?e:t}_getFileSystemMethods(t={}){return Object.assign(Object.assign({},e.DEFAULT_FILE_SYSTEM_ADAPTER),t)}}e["default"]=Settings},5789:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.splitWhen=e.flatten=void 0;function flatten(t){return t.reduce(((t,e)=>[].concat(t,e)),[])}e.flatten=flatten;function splitWhen(t,e){const r=[[]];let n=0;for(const s of t){if(e(s)){n++;r[n]=[]}else{r[n].push(s)}}return r}e.splitWhen=splitWhen},886:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.isEnoentCodeError=void 0;function isEnoentCodeError(t){return t.code==="ENOENT"}e.isEnoentCodeError=isEnoentCodeError},9102:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.createDirentFromStats=void 0;class DirentFromStats{constructor(t,e){this.name=t;this.isBlockDevice=e.isBlockDevice.bind(e);this.isCharacterDevice=e.isCharacterDevice.bind(e);this.isDirectory=e.isDirectory.bind(e);this.isFIFO=e.isFIFO.bind(e);this.isFile=e.isFile.bind(e);this.isSocket=e.isSocket.bind(e);this.isSymbolicLink=e.isSymbolicLink.bind(e)}}function createDirentFromStats(t,e){return new DirentFromStats(t,e)}e.createDirentFromStats=createDirentFromStats},3828:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.string=e.stream=e.pattern=e.path=e.fs=e.errno=e.array=void 0;const n=r(5789);e.array=n;const s=r(886);e.errno=s;const i=r(9102);e.fs=i;const o=r(6462);e.path=o;const a=r(2256);e.pattern=a;const u=r(9941);e.stream=u;const c=r(5121);e.string=c},6462:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.removeLeadingDotSegment=e.escape=e.makeAbsolute=e.unixify=void 0;const n=r(1017);const s=2;const i=/(\\?)([()*?[\]{|}]|^!|[!+@](?=\())/g;function unixify(t){return t.replace(/\\/g,"/")}e.unixify=unixify;function makeAbsolute(t,e){return n.resolve(t,e)}e.makeAbsolute=makeAbsolute;function escape(t){return t.replace(i,"\\$2")}e.escape=escape;function removeLeadingDotSegment(t){if(t.charAt(0)==="."){const e=t.charAt(1);if(e==="/"||e==="\\"){return t.slice(s)}}return t}e.removeLeadingDotSegment=removeLeadingDotSegment},2256:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.matchAny=e.convertPatternsToRe=e.makeRe=e.getPatternParts=e.expandBraceExpansion=e.expandPatternsWithBraceExpansion=e.isAffectDepthOfReadingPattern=e.endsWithSlashGlobStar=e.hasGlobStar=e.getBaseDirectory=e.isPatternRelatedToParentDirectory=e.getPatternsOutsideCurrentDirectory=e.getPatternsInsideCurrentDirectory=e.getPositivePatterns=e.getNegativePatterns=e.isPositivePattern=e.isNegativePattern=e.convertToNegativePattern=e.convertToPositivePattern=e.isDynamicPattern=e.isStaticPattern=void 0;const n=r(1017);const s=r(5976);const i=r(8627);const o="**";const a="\\";const u=/[*?]|^!/;const c=/\[[^[]*]/;const l=/(?:^|[^!*+?@])\([^(]*\|[^|]*\)/;const f=/[!*+?@]\([^(]*\)/;const p=/,|\.\./;function isStaticPattern(t,e={}){return!isDynamicPattern(t,e)}e.isStaticPattern=isStaticPattern;function isDynamicPattern(t,e={}){if(t===""){return false}if(e.caseSensitiveMatch===false||t.includes(a)){return true}if(u.test(t)||c.test(t)||l.test(t)){return true}if(e.extglob!==false&&f.test(t)){return true}if(e.braceExpansion!==false&&hasBraceExpansion(t)){return true}return false}e.isDynamicPattern=isDynamicPattern;function hasBraceExpansion(t){const e=t.indexOf("{");if(e===-1){return false}const r=t.indexOf("}",e+1);if(r===-1){return false}const n=t.slice(e,r);return p.test(n)}function convertToPositivePattern(t){return isNegativePattern(t)?t.slice(1):t}e.convertToPositivePattern=convertToPositivePattern;function convertToNegativePattern(t){return"!"+t}e.convertToNegativePattern=convertToNegativePattern;function isNegativePattern(t){return t.startsWith("!")&&t[1]!=="("}e.isNegativePattern=isNegativePattern;function isPositivePattern(t){return!isNegativePattern(t)}e.isPositivePattern=isPositivePattern;function getNegativePatterns(t){return t.filter(isNegativePattern)}e.getNegativePatterns=getNegativePatterns;function getPositivePatterns(t){return t.filter(isPositivePattern)}e.getPositivePatterns=getPositivePatterns;function getPatternsInsideCurrentDirectory(t){return t.filter((t=>!isPatternRelatedToParentDirectory(t)))}e.getPatternsInsideCurrentDirectory=getPatternsInsideCurrentDirectory;function getPatternsOutsideCurrentDirectory(t){return t.filter(isPatternRelatedToParentDirectory)}e.getPatternsOutsideCurrentDirectory=getPatternsOutsideCurrentDirectory;function isPatternRelatedToParentDirectory(t){return t.startsWith("..")||t.startsWith("./..")}e.isPatternRelatedToParentDirectory=isPatternRelatedToParentDirectory;function getBaseDirectory(t){return s(t,{flipBackslashes:false})}e.getBaseDirectory=getBaseDirectory;function hasGlobStar(t){return t.includes(o)}e.hasGlobStar=hasGlobStar;function endsWithSlashGlobStar(t){return t.endsWith("/"+o)}e.endsWithSlashGlobStar=endsWithSlashGlobStar;function isAffectDepthOfReadingPattern(t){const e=n.basename(t);return endsWithSlashGlobStar(t)||isStaticPattern(e)}e.isAffectDepthOfReadingPattern=isAffectDepthOfReadingPattern;function expandPatternsWithBraceExpansion(t){return t.reduce(((t,e)=>t.concat(expandBraceExpansion(e))),[])}e.expandPatternsWithBraceExpansion=expandPatternsWithBraceExpansion;function expandBraceExpansion(t){return i.braces(t,{expand:true,nodupes:true})}e.expandBraceExpansion=expandBraceExpansion;function getPatternParts(t,e){let{parts:r}=i.scan(t,Object.assign(Object.assign({},e),{parts:true}));if(r.length===0){r=[t]}if(r[0].startsWith("/")){r[0]=r[0].slice(1);r.unshift("")}return r}e.getPatternParts=getPatternParts;function makeRe(t,e){return i.makeRe(t,e)}e.makeRe=makeRe;function convertPatternsToRe(t,e){return t.map((t=>makeRe(t,e)))}e.convertPatternsToRe=convertPatternsToRe;function matchAny(t,e){return e.some((e=>e.test(t)))}e.matchAny=matchAny},9941:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.merge=void 0;const n=r(1382);function merge(t){const e=n(t);t.forEach((t=>{t.once("error",(t=>e.emit("error",t)))}));e.once("close",(()=>propagateCloseEventToSources(t)));e.once("end",(()=>propagateCloseEventToSources(t)));return e}e.merge=merge;function propagateCloseEventToSources(t){t.forEach((t=>t.emit("close")))}},5121:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:true});e.isEmpty=e.isString=void 0;function isString(t){return typeof t==="string"}e.isString=isString;function isEmpty(t){return t===""}e.isEmpty=isEmpty},4241:(t,e,r)=>{"use strict";var n=r(1816);function fastqueue(t,e,r){if(typeof t==="function"){r=e;e=t;t=null}if(r<1){throw new Error("fastqueue concurrency must be greater than 1")}var s=n(Task);var i=null;var o=null;var a=0;var u=null;var c={push:push,drain:noop,saturated:noop,pause:pause,paused:false,concurrency:r,running:running,resume:resume,idle:idle,length:length,getQueue:getQueue,unshift:unshift,empty:noop,kill:kill,killAndDrain:killAndDrain,error:error};return c;function running(){return a}function pause(){c.paused=true}function length(){var t=i;var e=0;while(t){t=t.next;e++}return e}function getQueue(){var t=i;var e=[];while(t){e.push(t.value);t=t.next}return e}function resume(){if(!c.paused)return;c.paused=false;for(var t=0;t<c.concurrency;t++){a++;release()}}function idle(){return a===0&&c.length()===0}function push(r,n){var l=s.get();l.context=t;l.release=release;l.value=r;l.callback=n||noop;l.errorHandler=u;if(a===c.concurrency||c.paused){if(o){o.next=l;o=l}else{i=l;o=l;c.saturated()}}else{a++;e.call(t,l.value,l.worked)}}function unshift(r,n){var u=s.get();u.context=t;u.release=release;u.value=r;u.callback=n||noop;if(a===c.concurrency||c.paused){if(i){u.next=i;i=u}else{i=u;o=u;c.saturated()}}else{a++;e.call(t,u.value,u.worked)}}function release(r){if(r){s.release(r)}var n=i;if(n){if(!c.paused){if(o===i){o=null}i=n.next;n.next=null;e.call(t,n.value,n.worked);if(o===null){c.empty()}}else{a--}}else if(--a===0){c.drain()}}function kill(){i=null;o=null;c.drain=noop}function killAndDrain(){i=null;o=null;c.drain();c.drain=noop}function error(t){u=t}}function noop(){}function Task(){this.value=null;this.callback=noop;this.next=null;this.release=noop;this.context=null;this.errorHandler=null;var t=this;this.worked=function worked(e,r){var n=t.callback;var s=t.errorHandler;var i=t.value;t.value=null;t.callback=noop;if(t.errorHandler){s(e,i)}n.call(t.context,e,r);t.release(t)}}function queueAsPromised(t,e,r){if(typeof t==="function"){r=e;e=t;t=null}function asyncWrapper(t,r){e.call(this,t).then((function(t){r(null,t)}),r)}var n=fastqueue(t,asyncWrapper,r);var s=n.push;var i=n.unshift;n.push=push;n.unshift=unshift;n.drained=drained;return n;function push(t){var e=new Promise((function(e,r){s(t,(function(t,n){if(t){r(t);return}e(n)}))}));e.catch(noop);return e}function unshift(t){var e=new Promise((function(e,r){i(t,(function(t,n){if(t){r(t);return}e(n)}))}));e.catch(noop);return e}function drained(){var t=n.drain;var e=new Promise((function(e){n.drain=function(){t();e()}}));return e}}t.exports=fastqueue;t.exports.promise=queueAsPromised},442:(t,e,r)=>{"use strict";
/*!
 * fill-range <https://github.com/jonschlinkert/fill-range>
 *
 * Copyright (c) 2014-present, Jon Schlinkert.
 * Licensed under the MIT License.
 */const n=r(3837);const s=r(211);const isObject=t=>t!==null&&typeof t==="object"&&!Array.isArray(t);const transform=t=>e=>t===true?Number(e):String(e);const isValidValue=t=>typeof t==="number"||typeof t==="string"&&t!=="";const isNumber=t=>Number.isInteger(+t);const zeros=t=>{let e=`${t}`;let r=-1;if(e[0]==="-")e=e.slice(1);if(e==="0")return false;while(e[++r]==="0");return r>0};const stringify=(t,e,r)=>{if(typeof t==="string"||typeof e==="string"){return true}return r.stringify===true};const pad=(t,e,r)=>{if(e>0){let r=t[0]==="-"?"-":"";if(r)t=t.slice(1);t=r+t.padStart(r?e-1:e,"0")}if(r===false){return String(t)}return t};const toMaxLen=(t,e)=>{let r=t[0]==="-"?"-":"";if(r){t=t.slice(1);e--}while(t.length<e)t="0"+t;return r?"-"+t:t};const toSequence=(t,e)=>{t.negatives.sort(((t,e)=>t<e?-1:t>e?1:0));t.positives.sort(((t,e)=>t<e?-1:t>e?1:0));let r=e.capture?"":"?:";let n="";let s="";let i;if(t.positives.length){n=t.positives.join("|")}if(t.negatives.length){s=`-(${r}${t.negatives.join("|")})`}if(n&&s){i=`${n}|${s}`}else{i=n||s}if(e.wrap){return`(${r}${i})`}return i};const toRange=(t,e,r,n)=>{if(r){return s(t,e,{wrap:false,...n})}let i=String.fromCharCode(t);if(t===e)return i;let o=String.fromCharCode(e);return`[${i}-${o}]`};const toRegex=(t,e,r)=>{if(Array.isArray(t)){let e=r.wrap===true;let n=r.capture?"":"?:";return e?`(${n}${t.join("|")})`:t.join("|")}return s(t,e,r)};const rangeError=(...t)=>new RangeError("Invalid range arguments: "+n.inspect(...t));const invalidRange=(t,e,r)=>{if(r.strictRanges===true)throw rangeError([t,e]);return[]};const invalidStep=(t,e)=>{if(e.strictRanges===true){throw new TypeError(`Expected step "${t}" to be a number`)}return[]};const fillNumbers=(t,e,r=1,n={})=>{let s=Number(t);let i=Number(e);if(!Number.isInteger(s)||!Number.isInteger(i)){if(n.strictRanges===true)throw rangeError([t,e]);return[]}if(s===0)s=0;if(i===0)i=0;let o=s>i;let a=String(t);let u=String(e);let c=String(r);r=Math.max(Math.abs(r),1);let l=zeros(a)||zeros(u)||zeros(c);let f=l?Math.max(a.length,u.length,c.length):0;let p=l===false&&stringify(t,e,n)===false;let h=n.transform||transform(p);if(n.toRegex&&r===1){return toRange(toMaxLen(t,f),toMaxLen(e,f),true,n)}let d={negatives:[],positives:[]};let push=t=>d[t<0?"negatives":"positives"].push(Math.abs(t));let _=[];let g=0;while(o?s>=i:s<=i){if(n.toRegex===true&&r>1){push(s)}else{_.push(pad(h(s,g),f,p))}s=o?s-r:s+r;g++}if(n.toRegex===true){return r>1?toSequence(d,n):toRegex(_,null,{wrap:false,...n})}return _};const fillLetters=(t,e,r=1,n={})=>{if(!isNumber(t)&&t.length>1||!isNumber(e)&&e.length>1){return invalidRange(t,e,n)}let s=n.transform||(t=>String.fromCharCode(t));let i=`${t}`.charCodeAt(0);let o=`${e}`.charCodeAt(0);let a=i>o;let u=Math.min(i,o);let c=Math.max(i,o);if(n.toRegex&&r===1){return toRange(u,c,false,n)}let l=[];let f=0;while(a?i>=o:i<=o){l.push(s(i,f));i=a?i-r:i+r;f++}if(n.toRegex===true){return toRegex(l,null,{wrap:false,options:n})}return l};const fill=(t,e,r,n={})=>{if(e==null&&isValidValue(t)){return[t]}if(!isValidValue(t)||!isValidValue(e)){return invalidRange(t,e,n)}if(typeof r==="function"){return fill(t,e,1,{transform:r})}if(isObject(r)){return fill(t,e,0,r)}let s={...n};if(s.capture===true)s.wrap=true;r=r||s.step||1;if(!isNumber(r)){if(r!=null&&!isObject(r))return invalidStep(r,s);return fill(t,e,1,r)}if(isNumber(t)&&isNumber(e)){return fillNumbers(t,e,r,s)}return fillLetters(t,e,Math.max(Math.abs(r),1),s)};t.exports=fill},5976:(t,e,r)=>{"use strict";var n=r(6654);var s=r(1017).posix.dirname;var i=r(2037).platform()==="win32";var o="/";var a=/\\/g;var u=/[\{\[].*[\}\]]$/;var c=/(^|[^\\])([\{\[]|\([^\)]+$)/;var l=/\\([\!\*\?\|\[\]\(\)\{\}])/g;t.exports=function globParent(t,e){var r=Object.assign({flipBackslashes:true},e);if(r.flipBackslashes&&i&&t.indexOf(o)<0){t=t.replace(a,o)}if(u.test(t)){t+=o}t+="a";do{t=s(t)}while(n(t)||c.test(t));return t.replace(l,"$1")}},3071:t=>{
/*!
 * is-extglob <https://github.com/jonschlinkert/is-extglob>
 *
 * Copyright (c) 2014-2016, Jon Schlinkert.
 * Licensed under the MIT License.
 */
t.exports=function isExtglob(t){if(typeof t!=="string"||t===""){return false}var e;while(e=/(\\).|([@?!+*]\(.*\))/g.exec(t)){if(e[2])return true;t=t.slice(e.index+e[0].length)}return false}},6654:(t,e,r)=>{
/*!
 * is-glob <https://github.com/jonschlinkert/is-glob>
 *
 * Copyright (c) 2014-2017, Jon Schlinkert.
 * Released under the MIT License.
 */
var n=r(3071);var s={"{":"}","(":")","[":"]"};var strictCheck=function(t){if(t[0]==="!"){return true}var e=0;var r=-2;var n=-2;var i=-2;var o=-2;var a=-2;while(e<t.length){if(t[e]==="*"){return true}if(t[e+1]==="?"&&/[\].+)]/.test(t[e])){return true}if(n!==-1&&t[e]==="["&&t[e+1]!=="]"){if(n<e){n=t.indexOf("]",e)}if(n>e){if(a===-1||a>n){return true}a=t.indexOf("\\",e);if(a===-1||a>n){return true}}}if(i!==-1&&t[e]==="{"&&t[e+1]!=="}"){i=t.indexOf("}",e);if(i>e){a=t.indexOf("\\",e);if(a===-1||a>i){return true}}}if(o!==-1&&t[e]==="("&&t[e+1]==="?"&&/[:!=]/.test(t[e+2])&&t[e+3]!==")"){o=t.indexOf(")",e);if(o>e){a=t.indexOf("\\",e);if(a===-1||a>o){return true}}}if(r!==-1&&t[e]==="("&&t[e+1]!=="|"){if(r<e){r=t.indexOf("|",e)}if(r!==-1&&t[r+1]!==")"){o=t.indexOf(")",r);if(o>r){a=t.indexOf("\\",r);if(a===-1||a>o){return true}}}}if(t[e]==="\\"){var u=t[e+1];e+=2;var c=s[u];if(c){var l=t.indexOf(c,e);if(l!==-1){e=l+1}}if(t[e]==="!"){return true}}else{e++}}return false};var relaxedCheck=function(t){if(t[0]==="!"){return true}var e=0;while(e<t.length){if(/[*?{}()[\]]/.test(t[e])){return true}if(t[e]==="\\"){var r=t[e+1];e+=2;var n=s[r];if(n){var i=t.indexOf(n,e);if(i!==-1){e=i+1}}if(t[e]==="!"){return true}}else{e++}}return false};t.exports=function isGlob(t,e){if(typeof t!=="string"||t===""){return false}if(n(t)){return true}var r=strictCheck;if(e&&e.strict===false){r=relaxedCheck}return r(t)}},8387:t=>{"use strict";
/*!
 * is-number <https://github.com/jonschlinkert/is-number>
 *
 * Copyright (c) 2014-present, Jon Schlinkert.
 * Released under the MIT License.
 */t.exports=function(t){if(typeof t==="number"){return t-t===0}if(typeof t==="string"&&t.trim()!==""){return Number.isFinite?Number.isFinite(+t):isFinite(+t)}return false}},1382:(t,e,r)=>{"use strict";const n=r(2781);const s=n.PassThrough;const i=Array.prototype.slice;t.exports=merge2;function merge2(){const t=[];const e=i.call(arguments);let r=false;let n=e[e.length-1];if(n&&!Array.isArray(n)&&n.pipe==null){e.pop()}else{n={}}const o=n.end!==false;const a=n.pipeError===true;if(n.objectMode==null){n.objectMode=true}if(n.highWaterMark==null){n.highWaterMark=64*1024}const u=s(n);function addStream(){for(let e=0,r=arguments.length;e<r;e++){t.push(pauseStreams(arguments[e],n))}mergeStream();return this}function mergeStream(){if(r){return}r=true;let e=t.shift();if(!e){process.nextTick(endStream);return}if(!Array.isArray(e)){e=[e]}let n=e.length+1;function next(){if(--n>0){return}r=false;mergeStream()}function pipe(t){function onend(){t.removeListener("merge2UnpipeEnd",onend);t.removeListener("end",onend);if(a){t.removeListener("error",onerror)}next()}function onerror(t){u.emit("error",t)}if(t._readableState.endEmitted){return next()}t.on("merge2UnpipeEnd",onend);t.on("end",onend);if(a){t.on("error",onerror)}t.pipe(u,{end:false});t.resume()}for(let t=0;t<e.length;t++){pipe(e[t])}next()}function endStream(){r=false;u.emit("queueDrain");if(o){u.end()}}u.setMaxListeners(0);u.add=addStream;u.on("unpipe",(function(t){t.emit("merge2UnpipeEnd")}));if(e.length){addStream.apply(null,e)}return u}function pauseStreams(t,e){if(!Array.isArray(t)){if(!t._readableState&&t.pipe){t=t.pipe(s(e))}if(!t._readableState||!t.pause||!t.pipe){throw new Error("Only readable stream can be merged.")}t.pause()}else{for(let r=0,n=t.length;r<n;r++){t[r]=pauseStreams(t[r],e)}}return t}},8627:(t,e,r)=>{"use strict";const n=r(3837);const s=r(1570);const i=r(376);const o=r(8658);const isEmptyString=t=>t===""||t==="./";const micromatch=(t,e,r)=>{e=[].concat(e);t=[].concat(t);let n=new Set;let s=new Set;let o=new Set;let a=0;let onResult=t=>{o.add(t.output);if(r&&r.onResult){r.onResult(t)}};for(let o=0;o<e.length;o++){let u=i(String(e[o]),{...r,onResult:onResult},true);let c=u.state.negated||u.state.negatedExtglob;if(c)a++;for(let e of t){let t=u(e,true);let r=c?!t.isMatch:t.isMatch;if(!r)continue;if(c){n.add(t.output)}else{n.delete(t.output);s.add(t.output)}}}let u=a===e.length?[...o]:[...s];let c=u.filter((t=>!n.has(t)));if(r&&c.length===0){if(r.failglob===true){throw new Error(`No matches found for "${e.join(", ")}"`)}if(r.nonull===true||r.nullglob===true){return r.unescape?e.map((t=>t.replace(/\\/g,""))):e}}return c};micromatch.match=micromatch;micromatch.matcher=(t,e)=>i(t,e);micromatch.isMatch=(t,e,r)=>i(e,r)(t);micromatch.any=micromatch.isMatch;micromatch.not=(t,e,r={})=>{e=[].concat(e).map(String);let n=new Set;let s=[];let onResult=t=>{if(r.onResult)r.onResult(t);s.push(t.output)};let i=new Set(micromatch(t,e,{...r,onResult:onResult}));for(let t of s){if(!i.has(t)){n.add(t)}}return[...n]};micromatch.contains=(t,e,r)=>{if(typeof t!=="string"){throw new TypeError(`Expected a string: "${n.inspect(t)}"`)}if(Array.isArray(e)){return e.some((e=>micromatch.contains(t,e,r)))}if(typeof e==="string"){if(isEmptyString(t)||isEmptyString(e)){return false}if(t.includes(e)||t.startsWith("./")&&t.slice(2).includes(e)){return true}}return micromatch.isMatch(t,e,{...r,contains:true})};micromatch.matchKeys=(t,e,r)=>{if(!o.isObject(t)){throw new TypeError("Expected the first argument to be an object")}let n=micromatch(Object.keys(t),e,r);let s={};for(let e of n)s[e]=t[e];return s};micromatch.some=(t,e,r)=>{let n=[].concat(t);for(let t of[].concat(e)){let e=i(String(t),r);if(n.some((t=>e(t)))){return true}}return false};micromatch.every=(t,e,r)=>{let n=[].concat(t);for(let t of[].concat(e)){let e=i(String(t),r);if(!n.every((t=>e(t)))){return false}}return true};micromatch.all=(t,e,r)=>{if(typeof t!=="string"){throw new TypeError(`Expected a string: "${n.inspect(t)}"`)}return[].concat(e).every((e=>i(e,r)(t)))};micromatch.capture=(t,e,r)=>{let n=o.isWindows(r);let s=i.makeRe(String(t),{...r,capture:true});let a=s.exec(n?o.toPosixSlashes(e):e);if(a){return a.slice(1).map((t=>t===void 0?"":t))}};micromatch.makeRe=(...t)=>i.makeRe(...t);micromatch.scan=(...t)=>i.scan(...t);micromatch.parse=(t,e)=>{let r=[];for(let n of[].concat(t||[])){for(let t of s(String(n),e)){r.push(i.parse(t,e))}}return r};micromatch.braces=(t,e)=>{if(typeof t!=="string")throw new TypeError("Expected a string");if(e&&e.nobrace===true||!/\{.*\}/.test(t)){return[t]}return s(t,e)};micromatch.braceExpand=(t,e)=>{if(typeof t!=="string")throw new TypeError("Expected a string");return micromatch.braces(t,{...e,expand:true})};t.exports=micromatch},376:(t,e,r)=>{"use strict";t.exports=r(7631)},7820:(t,e,r)=>{"use strict";const n=r(1017);const s="\\\\/";const i=`[^${s}]`;const o="\\.";const a="\\+";const u="\\?";const c="\\/";const l="(?=.)";const f="[^/]";const p=`(?:${c}|$)`;const h=`(?:^|${c})`;const d=`${o}{1,2}${p}`;const _=`(?!${o})`;const g=`(?!${h}${d})`;const y=`(?!${o}{0,1}${p})`;const S=`(?!${d})`;const m=`[^.${c}]`;const v=`${f}*?`;const E={DOT_LITERAL:o,PLUS_LITERAL:a,QMARK_LITERAL:u,SLASH_LITERAL:c,ONE_CHAR:l,QMARK:f,END_ANCHOR:p,DOTS_SLASH:d,NO_DOT:_,NO_DOTS:g,NO_DOT_SLASH:y,NO_DOTS_SLASH:S,QMARK_NO_DOT:m,STAR:v,START_ANCHOR:h};const A={...E,SLASH_LITERAL:`[${s}]`,QMARK:i,STAR:`${i}*?`,DOTS_SLASH:`${o}{1,2}(?:[${s}]|$)`,NO_DOT:`(?!${o})`,NO_DOTS:`(?!(?:^|[${s}])${o}{1,2}(?:[${s}]|$))`,NO_DOT_SLASH:`(?!${o}{0,1}(?:[${s}]|$))`,NO_DOTS_SLASH:`(?!${o}{1,2}(?:[${s}]|$))`,QMARK_NO_DOT:`[^.${s}]`,START_ANCHOR:`(?:^|[${s}])`,END_ANCHOR:`(?:[${s}]|$)`};const b={alnum:"a-zA-Z0-9",alpha:"a-zA-Z",ascii:"\\x00-\\x7F",blank:" \\t",cntrl:"\\x00-\\x1F\\x7F",digit:"0-9",graph:"\\x21-\\x7E",lower:"a-z",print:"\\x20-\\x7E ",punct:"\\-!\"#$%&'()\\*+,./:;<=>?@[\\]^_`{|}~",space:" \\t\\r\\n\\v\\f",upper:"A-Z",word:"A-Za-z0-9_",xdigit:"A-Fa-f0-9"};t.exports={MAX_LENGTH:1024*64,POSIX_REGEX_SOURCE:b,REGEX_BACKSLASH:/\\(?![*+?^${}(|)[\]])/g,REGEX_NON_SPECIAL_CHARS:/^[^@![\].,$*+?^{}()|\\/]+/,REGEX_SPECIAL_CHARS:/[-*+?.^${}(|)[\]]/,REGEX_SPECIAL_CHARS_BACKREF:/(\\?)((\W)(\3*))/g,REGEX_SPECIAL_CHARS_GLOBAL:/([-*+?.^${}(|)[\]])/g,REGEX_REMOVE_BACKSLASH:/(?:\[.*?[^\\]\]|\\(?=.))/g,REPLACEMENTS:{"***":"*","**/**":"**","**/**/**":"**"},CHAR_0:48,CHAR_9:57,CHAR_UPPERCASE_A:65,CHAR_LOWERCASE_A:97,CHAR_UPPERCASE_Z:90,CHAR_LOWERCASE_Z:122,CHAR_LEFT_PARENTHESES:40,CHAR_RIGHT_PARENTHESES:41,CHAR_ASTERISK:42,CHAR_AMPERSAND:38,CHAR_AT:64,CHAR_BACKWARD_SLASH:92,CHAR_CARRIAGE_RETURN:13,CHAR_CIRCUMFLEX_ACCENT:94,CHAR_COLON:58,CHAR_COMMA:44,CHAR_DOT:46,CHAR_DOUBLE_QUOTE:34,CHAR_EQUAL:61,CHAR_EXCLAMATION_MARK:33,CHAR_FORM_FEED:12,CHAR_FORWARD_SLASH:47,CHAR_GRAVE_ACCENT:96,CHAR_HASH:35,CHAR_HYPHEN_MINUS:45,CHAR_LEFT_ANGLE_BRACKET:60,CHAR_LEFT_CURLY_BRACE:123,CHAR_LEFT_SQUARE_BRACKET:91,CHAR_LINE_FEED:10,CHAR_NO_BREAK_SPACE:160,CHAR_PERCENT:37,CHAR_PLUS:43,CHAR_QUESTION_MARK:63,CHAR_RIGHT_ANGLE_BRACKET:62,CHAR_RIGHT_CURLY_BRACE:125,CHAR_RIGHT_SQUARE_BRACKET:93,CHAR_SEMICOLON:59,CHAR_SINGLE_QUOTE:39,CHAR_SPACE:32,CHAR_TAB:9,CHAR_UNDERSCORE:95,CHAR_VERTICAL_LINE:124,CHAR_ZERO_WIDTH_NOBREAK_SPACE:65279,SEP:n.sep,extglobChars(t){return{"!":{type:"negate",open:"(?:(?!(?:",close:`))${t.STAR})`},"?":{type:"qmark",open:"(?:",close:")?"},"+":{type:"plus",open:"(?:",close:")+"},"*":{type:"star",open:"(?:",close:")*"},"@":{type:"at",open:"(?:",close:")"}}},globChars(t){return t===true?A:E}}},6986:(t,e,r)=>{"use strict";const n=r(7820);const s=r(8658);const{MAX_LENGTH:i,POSIX_REGEX_SOURCE:o,REGEX_NON_SPECIAL_CHARS:a,REGEX_SPECIAL_CHARS_BACKREF:u,REPLACEMENTS:c}=n;const expandRange=(t,e)=>{if(typeof e.expandRange==="function"){return e.expandRange(...t,e)}t.sort();const r=`[${t.join("-")}]`;try{new RegExp(r)}catch(e){return t.map((t=>s.escapeRegex(t))).join("..")}return r};const syntaxError=(t,e)=>`Missing ${t}: "${e}" - use "\\\\${e}" to match literal characters`;const parse=(t,e)=>{if(typeof t!=="string"){throw new TypeError("Expected a string")}t=c[t]||t;const r={...e};const l=typeof r.maxLength==="number"?Math.min(i,r.maxLength):i;let f=t.length;if(f>l){throw new SyntaxError(`Input length: ${f}, exceeds maximum allowed length: ${l}`)}const p={type:"bos",value:"",output:r.prepend||""};const h=[p];const d=r.capture?"":"?:";const _=s.isWindows(e);const g=n.globChars(_);const y=n.extglobChars(g);const{DOT_LITERAL:S,PLUS_LITERAL:m,SLASH_LITERAL:v,ONE_CHAR:E,DOTS_SLASH:A,NO_DOT:b,NO_DOT_SLASH:R,NO_DOTS_SLASH:P,QMARK:x,QMARK_NO_DOT:k,STAR:T,START_ANCHOR:C}=g;const globstar=t=>`(${d}(?:(?!${C}${t.dot?A:S}).)*?)`;const w=r.dot?"":b;const O=r.dot?x:k;let L=r.bash===true?globstar(r):T;if(r.capture){L=`(${L})`}if(typeof r.noext==="boolean"){r.noextglob=r.noext}const H={input:t,index:-1,start:0,dot:r.dot===true,consumed:"",output:"",prefix:"",backtrack:false,negated:false,brackets:0,braces:0,parens:0,quotes:0,globstar:false,tokens:h};t=s.removePrefix(t,H);f=t.length;const D=[];const F=[];const $=[];let M=p;let N;const eos=()=>H.index===f-1;const I=H.peek=(e=1)=>t[H.index+e];const B=H.advance=()=>t[++H.index]||"";const remaining=()=>t.slice(H.index+1);const consume=(t="",e=0)=>{H.consumed+=t;H.index+=e};const append=t=>{H.output+=t.output!=null?t.output:t.value;consume(t.value)};const negate=()=>{let t=1;while(I()==="!"&&(I(2)!=="("||I(3)==="?")){B();H.start++;t++}if(t%2===0){return false}H.negated=true;H.start++;return true};const increment=t=>{H[t]++;$.push(t)};const decrement=t=>{H[t]--;$.pop()};const push=t=>{if(M.type==="globstar"){const e=H.braces>0&&(t.type==="comma"||t.type==="brace");const r=t.extglob===true||D.length&&(t.type==="pipe"||t.type==="paren");if(t.type!=="slash"&&t.type!=="paren"&&!e&&!r){H.output=H.output.slice(0,-M.output.length);M.type="star";M.value="*";M.output=L;H.output+=M.output}}if(D.length&&t.type!=="paren"){D[D.length-1].inner+=t.value}if(t.value||t.output)append(t);if(M&&M.type==="text"&&t.type==="text"){M.value+=t.value;M.output=(M.output||"")+t.value;return}t.prev=M;h.push(t);M=t};const extglobOpen=(t,e)=>{const n={...y[e],conditions:1,inner:""};n.prev=M;n.parens=H.parens;n.output=H.output;const s=(r.capture?"(":"")+n.open;increment("parens");push({type:t,value:e,output:H.output?"":E});push({type:"paren",extglob:true,value:B(),output:s});D.push(n)};const extglobClose=t=>{let n=t.close+(r.capture?")":"");let s;if(t.type==="negate"){let i=L;if(t.inner&&t.inner.length>1&&t.inner.includes("/")){i=globstar(r)}if(i!==L||eos()||/^\)+$/.test(remaining())){n=t.close=`)$))${i}`}if(t.inner.includes("*")&&(s=remaining())&&/^\.[^\\/.]+$/.test(s)){const r=parse(s,{...e,fastpaths:false}).output;n=t.close=`)${r})${i})`}if(t.prev.type==="bos"){H.negatedExtglob=true}}push({type:"paren",extglob:true,value:N,output:n});decrement("parens")};if(r.fastpaths!==false&&!/(^[*!]|[/()[\]{}"])/.test(t)){let n=false;let i=t.replace(u,((t,e,r,s,i,o)=>{if(s==="\\"){n=true;return t}if(s==="?"){if(e){return e+s+(i?x.repeat(i.length):"")}if(o===0){return O+(i?x.repeat(i.length):"")}return x.repeat(r.length)}if(s==="."){return S.repeat(r.length)}if(s==="*"){if(e){return e+s+(i?L:"")}return L}return e?t:`\\${t}`}));if(n===true){if(r.unescape===true){i=i.replace(/\\/g,"")}else{i=i.replace(/\\+/g,(t=>t.length%2===0?"\\\\":t?"\\":""))}}if(i===t&&r.contains===true){H.output=t;return H}H.output=s.wrapOutput(i,H,e);return H}while(!eos()){N=B();if(N==="\0"){continue}if(N==="\\"){const t=I();if(t==="/"&&r.bash!==true){continue}if(t==="."||t===";"){continue}if(!t){N+="\\";push({type:"text",value:N});continue}const e=/^\\+/.exec(remaining());let n=0;if(e&&e[0].length>2){n=e[0].length;H.index+=n;if(n%2!==0){N+="\\"}}if(r.unescape===true){N=B()}else{N+=B()}if(H.brackets===0){push({type:"text",value:N});continue}}if(H.brackets>0&&(N!=="]"||M.value==="["||M.value==="[^")){if(r.posix!==false&&N===":"){const t=M.value.slice(1);if(t.includes("[")){M.posix=true;if(t.includes(":")){const t=M.value.lastIndexOf("[");const e=M.value.slice(0,t);const r=M.value.slice(t+2);const n=o[r];if(n){M.value=e+n;H.backtrack=true;B();if(!p.output&&h.indexOf(M)===1){p.output=E}continue}}}}if(N==="["&&I()!==":"||N==="-"&&I()==="]"){N=`\\${N}`}if(N==="]"&&(M.value==="["||M.value==="[^")){N=`\\${N}`}if(r.posix===true&&N==="!"&&M.value==="["){N="^"}M.value+=N;append({value:N});continue}if(H.quotes===1&&N!=='"'){N=s.escapeRegex(N);M.value+=N;append({value:N});continue}if(N==='"'){H.quotes=H.quotes===1?0:1;if(r.keepQuotes===true){push({type:"text",value:N})}continue}if(N==="("){increment("parens");push({type:"paren",value:N});continue}if(N===")"){if(H.parens===0&&r.strictBrackets===true){throw new SyntaxError(syntaxError("opening","("))}const t=D[D.length-1];if(t&&H.parens===t.parens+1){extglobClose(D.pop());continue}push({type:"paren",value:N,output:H.parens?")":"\\)"});decrement("parens");continue}if(N==="["){if(r.nobracket===true||!remaining().includes("]")){if(r.nobracket!==true&&r.strictBrackets===true){throw new SyntaxError(syntaxError("closing","]"))}N=`\\${N}`}else{increment("brackets")}push({type:"bracket",value:N});continue}if(N==="]"){if(r.nobracket===true||M&&M.type==="bracket"&&M.value.length===1){push({type:"text",value:N,output:`\\${N}`});continue}if(H.brackets===0){if(r.strictBrackets===true){throw new SyntaxError(syntaxError("opening","["))}push({type:"text",value:N,output:`\\${N}`});continue}decrement("brackets");const t=M.value.slice(1);if(M.posix!==true&&t[0]==="^"&&!t.includes("/")){N=`/${N}`}M.value+=N;append({value:N});if(r.literalBrackets===false||s.hasRegexChars(t)){continue}const e=s.escapeRegex(M.value);H.output=H.output.slice(0,-M.value.length);if(r.literalBrackets===true){H.output+=e;M.value=e;continue}M.value=`(${d}${e}|${M.value})`;H.output+=M.value;continue}if(N==="{"&&r.nobrace!==true){increment("braces");const t={type:"brace",value:N,output:"(",outputIndex:H.output.length,tokensIndex:H.tokens.length};F.push(t);push(t);continue}if(N==="}"){const t=F[F.length-1];if(r.nobrace===true||!t){push({type:"text",value:N,output:N});continue}let e=")";if(t.dots===true){const t=h.slice();const n=[];for(let e=t.length-1;e>=0;e--){h.pop();if(t[e].type==="brace"){break}if(t[e].type!=="dots"){n.unshift(t[e].value)}}e=expandRange(n,r);H.backtrack=true}if(t.comma!==true&&t.dots!==true){const r=H.output.slice(0,t.outputIndex);const n=H.tokens.slice(t.tokensIndex);t.value=t.output="\\{";N=e="\\}";H.output=r;for(const t of n){H.output+=t.output||t.value}}push({type:"brace",value:N,output:e});decrement("braces");F.pop();continue}if(N==="|"){if(D.length>0){D[D.length-1].conditions++}push({type:"text",value:N});continue}if(N===","){let t=N;const e=F[F.length-1];if(e&&$[$.length-1]==="braces"){e.comma=true;t="|"}push({type:"comma",value:N,output:t});continue}if(N==="/"){if(M.type==="dot"&&H.index===H.start+1){H.start=H.index+1;H.consumed="";H.output="";h.pop();M=p;continue}push({type:"slash",value:N,output:v});continue}if(N==="."){if(H.braces>0&&M.type==="dot"){if(M.value===".")M.output=S;const t=F[F.length-1];M.type="dots";M.output+=N;M.value+=N;t.dots=true;continue}if(H.braces+H.parens===0&&M.type!=="bos"&&M.type!=="slash"){push({type:"text",value:N,output:S});continue}push({type:"dot",value:N,output:S});continue}if(N==="?"){const t=M&&M.value==="(";if(!t&&r.noextglob!==true&&I()==="("&&I(2)!=="?"){extglobOpen("qmark",N);continue}if(M&&M.type==="paren"){const t=I();let e=N;if(t==="<"&&!s.supportsLookbehinds()){throw new Error("Node.js v10 or higher is required for regex lookbehinds")}if(M.value==="("&&!/[!=<:]/.test(t)||t==="<"&&!/<([!=]|\w+>)/.test(remaining())){e=`\\${N}`}push({type:"text",value:N,output:e});continue}if(r.dot!==true&&(M.type==="slash"||M.type==="bos")){push({type:"qmark",value:N,output:k});continue}push({type:"qmark",value:N,output:x});continue}if(N==="!"){if(r.noextglob!==true&&I()==="("){if(I(2)!=="?"||!/[!=<:]/.test(I(3))){extglobOpen("negate",N);continue}}if(r.nonegate!==true&&H.index===0){negate();continue}}if(N==="+"){if(r.noextglob!==true&&I()==="("&&I(2)!=="?"){extglobOpen("plus",N);continue}if(M&&M.value==="("||r.regex===false){push({type:"plus",value:N,output:m});continue}if(M&&(M.type==="bracket"||M.type==="paren"||M.type==="brace")||H.parens>0){push({type:"plus",value:N});continue}push({type:"plus",value:m});continue}if(N==="@"){if(r.noextglob!==true&&I()==="("&&I(2)!=="?"){push({type:"at",extglob:true,value:N,output:""});continue}push({type:"text",value:N});continue}if(N!=="*"){if(N==="$"||N==="^"){N=`\\${N}`}const t=a.exec(remaining());if(t){N+=t[0];H.index+=t[0].length}push({type:"text",value:N});continue}if(M&&(M.type==="globstar"||M.star===true)){M.type="star";M.star=true;M.value+=N;M.output=L;H.backtrack=true;H.globstar=true;consume(N);continue}let e=remaining();if(r.noextglob!==true&&/^\([^?]/.test(e)){extglobOpen("star",N);continue}if(M.type==="star"){if(r.noglobstar===true){consume(N);continue}const n=M.prev;const s=n.prev;const i=n.type==="slash"||n.type==="bos";const o=s&&(s.type==="star"||s.type==="globstar");if(r.bash===true&&(!i||e[0]&&e[0]!=="/")){push({type:"star",value:N,output:""});continue}const a=H.braces>0&&(n.type==="comma"||n.type==="brace");const u=D.length&&(n.type==="pipe"||n.type==="paren");if(!i&&n.type!=="paren"&&!a&&!u){push({type:"star",value:N,output:""});continue}while(e.slice(0,3)==="/**"){const r=t[H.index+4];if(r&&r!=="/"){break}e=e.slice(3);consume("/**",3)}if(n.type==="bos"&&eos()){M.type="globstar";M.value+=N;M.output=globstar(r);H.output=M.output;H.globstar=true;consume(N);continue}if(n.type==="slash"&&n.prev.type!=="bos"&&!o&&eos()){H.output=H.output.slice(0,-(n.output+M.output).length);n.output=`(?:${n.output}`;M.type="globstar";M.output=globstar(r)+(r.strictSlashes?")":"|$)");M.value+=N;H.globstar=true;H.output+=n.output+M.output;consume(N);continue}if(n.type==="slash"&&n.prev.type!=="bos"&&e[0]==="/"){const t=e[1]!==void 0?"|$":"";H.output=H.output.slice(0,-(n.output+M.output).length);n.output=`(?:${n.output}`;M.type="globstar";M.output=`${globstar(r)}${v}|${v}${t})`;M.value+=N;H.output+=n.output+M.output;H.globstar=true;consume(N+B());push({type:"slash",value:"/",output:""});continue}if(n.type==="bos"&&e[0]==="/"){M.type="globstar";M.value+=N;M.output=`(?:^|${v}|${globstar(r)}${v})`;H.output=M.output;H.globstar=true;consume(N+B());push({type:"slash",value:"/",output:""});continue}H.output=H.output.slice(0,-M.output.length);M.type="globstar";M.output=globstar(r);M.value+=N;H.output+=M.output;H.globstar=true;consume(N);continue}const n={type:"star",value:N,output:L};if(r.bash===true){n.output=".*?";if(M.type==="bos"||M.type==="slash"){n.output=w+n.output}push(n);continue}if(M&&(M.type==="bracket"||M.type==="paren")&&r.regex===true){n.output=N;push(n);continue}if(H.index===H.start||M.type==="slash"||M.type==="dot"){if(M.type==="dot"){H.output+=R;M.output+=R}else if(r.dot===true){H.output+=P;M.output+=P}else{H.output+=w;M.output+=w}if(I()!=="*"){H.output+=E;M.output+=E}}push(n)}while(H.brackets>0){if(r.strictBrackets===true)throw new SyntaxError(syntaxError("closing","]"));H.output=s.escapeLast(H.output,"[");decrement("brackets")}while(H.parens>0){if(r.strictBrackets===true)throw new SyntaxError(syntaxError("closing",")"));H.output=s.escapeLast(H.output,"(");decrement("parens")}while(H.braces>0){if(r.strictBrackets===true)throw new SyntaxError(syntaxError("closing","}"));H.output=s.escapeLast(H.output,"{");decrement("braces")}if(r.strictSlashes!==true&&(M.type==="star"||M.type==="bracket")){push({type:"maybe_slash",value:"",output:`${v}?`})}if(H.backtrack===true){H.output="";for(const t of H.tokens){H.output+=t.output!=null?t.output:t.value;if(t.suffix){H.output+=t.suffix}}}return H};parse.fastpaths=(t,e)=>{const r={...e};const o=typeof r.maxLength==="number"?Math.min(i,r.maxLength):i;const a=t.length;if(a>o){throw new SyntaxError(`Input length: ${a}, exceeds maximum allowed length: ${o}`)}t=c[t]||t;const u=s.isWindows(e);const{DOT_LITERAL:l,SLASH_LITERAL:f,ONE_CHAR:p,DOTS_SLASH:h,NO_DOT:d,NO_DOTS:_,NO_DOTS_SLASH:g,STAR:y,START_ANCHOR:S}=n.globChars(u);const m=r.dot?_:d;const v=r.dot?g:d;const E=r.capture?"":"?:";const A={negated:false,prefix:""};let b=r.bash===true?".*?":y;if(r.capture){b=`(${b})`}const globstar=t=>{if(t.noglobstar===true)return b;return`(${E}(?:(?!${S}${t.dot?h:l}).)*?)`};const create=t=>{switch(t){case"*":return`${m}${p}${b}`;case".*":return`${l}${p}${b}`;case"*.*":return`${m}${b}${l}${p}${b}`;case"*/*":return`${m}${b}${f}${p}${v}${b}`;case"**":return m+globstar(r);case"**/*":return`(?:${m}${globstar(r)}${f})?${v}${p}${b}`;case"**/*.*":return`(?:${m}${globstar(r)}${f})?${v}${b}${l}${p}${b}`;case"**/.*":return`(?:${m}${globstar(r)}${f})?${l}${p}${b}`;default:{const e=/^(.*?)\.(\w+)$/.exec(t);if(!e)return;const r=create(e[1]);if(!r)return;return r+l+e[2]}}};const R=s.removePrefix(t,A);let P=create(R);if(P&&r.strictSlashes!==true){P+=`${f}?`}return P};t.exports=parse},7631:(t,e,r)=>{"use strict";const n=r(1017);const s=r(8640);const i=r(6986);const o=r(8658);const a=r(7820);const isObject=t=>t&&typeof t==="object"&&!Array.isArray(t);const picomatch=(t,e,r=false)=>{if(Array.isArray(t)){const n=t.map((t=>picomatch(t,e,r)));const arrayMatcher=t=>{for(const e of n){const r=e(t);if(r)return r}return false};return arrayMatcher}const n=isObject(t)&&t.tokens&&t.input;if(t===""||typeof t!=="string"&&!n){throw new TypeError("Expected pattern to be a non-empty string")}const s=e||{};const i=o.isWindows(e);const a=n?picomatch.compileRe(t,e):picomatch.makeRe(t,e,false,true);const u=a.state;delete a.state;let isIgnored=()=>false;if(s.ignore){const t={...e,ignore:null,onMatch:null,onResult:null};isIgnored=picomatch(s.ignore,t,r)}const matcher=(r,n=false)=>{const{isMatch:o,match:c,output:l}=picomatch.test(r,a,e,{glob:t,posix:i});const f={glob:t,state:u,regex:a,posix:i,input:r,output:l,match:c,isMatch:o};if(typeof s.onResult==="function"){s.onResult(f)}if(o===false){f.isMatch=false;return n?f:false}if(isIgnored(r)){if(typeof s.onIgnore==="function"){s.onIgnore(f)}f.isMatch=false;return n?f:false}if(typeof s.onMatch==="function"){s.onMatch(f)}return n?f:true};if(r){matcher.state=u}return matcher};picomatch.test=(t,e,r,{glob:n,posix:s}={})=>{if(typeof t!=="string"){throw new TypeError("Expected input to be a string")}if(t===""){return{isMatch:false,output:""}}const i=r||{};const a=i.format||(s?o.toPosixSlashes:null);let u=t===n;let c=u&&a?a(t):t;if(u===false){c=a?a(t):t;u=c===n}if(u===false||i.capture===true){if(i.matchBase===true||i.basename===true){u=picomatch.matchBase(t,e,r,s)}else{u=e.exec(c)}}return{isMatch:Boolean(u),match:u,output:c}};picomatch.matchBase=(t,e,r,s=o.isWindows(r))=>{const i=e instanceof RegExp?e:picomatch.makeRe(e,r);return i.test(n.basename(t))};picomatch.isMatch=(t,e,r)=>picomatch(e,r)(t);picomatch.parse=(t,e)=>{if(Array.isArray(t))return t.map((t=>picomatch.parse(t,e)));return i(t,{...e,fastpaths:false})};picomatch.scan=(t,e)=>s(t,e);picomatch.compileRe=(t,e,r=false,n=false)=>{if(r===true){return t.output}const s=e||{};const i=s.contains?"":"^";const o=s.contains?"":"$";let a=`${i}(?:${t.output})${o}`;if(t&&t.negated===true){a=`^(?!${a}).*$`}const u=picomatch.toRegex(a,e);if(n===true){u.state=t}return u};picomatch.makeRe=(t,e={},r=false,n=false)=>{if(!t||typeof t!=="string"){throw new TypeError("Expected a non-empty string")}let s={negated:false,fastpaths:true};if(e.fastpaths!==false&&(t[0]==="."||t[0]==="*")){s.output=i.fastpaths(t,e)}if(!s.output){s=i(t,e)}return picomatch.compileRe(s,e,r,n)};picomatch.toRegex=(t,e)=>{try{const r=e||{};return new RegExp(t,r.flags||(r.nocase?"i":""))}catch(t){if(e&&e.debug===true)throw t;return/$^/}};picomatch.constants=a;t.exports=picomatch},8640:(t,e,r)=>{"use strict";const n=r(8658);const{CHAR_ASTERISK:s,CHAR_AT:i,CHAR_BACKWARD_SLASH:o,CHAR_COMMA:a,CHAR_DOT:u,CHAR_EXCLAMATION_MARK:c,CHAR_FORWARD_SLASH:l,CHAR_LEFT_CURLY_BRACE:f,CHAR_LEFT_PARENTHESES:p,CHAR_LEFT_SQUARE_BRACKET:h,CHAR_PLUS:d,CHAR_QUESTION_MARK:_,CHAR_RIGHT_CURLY_BRACE:g,CHAR_RIGHT_PARENTHESES:y,CHAR_RIGHT_SQUARE_BRACKET:S}=r(7820);const isPathSeparator=t=>t===l||t===o;const depth=t=>{if(t.isPrefix!==true){t.depth=t.isGlobstar?Infinity:1}};const scan=(t,e)=>{const r=e||{};const m=t.length-1;const v=r.parts===true||r.scanToEnd===true;const E=[];const A=[];const b=[];let R=t;let P=-1;let x=0;let k=0;let T=false;let C=false;let w=false;let O=false;let L=false;let H=false;let D=false;let F=false;let $=false;let M=false;let N=0;let I;let B;let j={value:"",depth:0,isGlob:false};const eos=()=>P>=m;const peek=()=>R.charCodeAt(P+1);const advance=()=>{I=B;return R.charCodeAt(++P)};while(P<m){B=advance();let t;if(B===o){D=j.backslashes=true;B=advance();if(B===f){H=true}continue}if(H===true||B===f){N++;while(eos()!==true&&(B=advance())){if(B===o){D=j.backslashes=true;advance();continue}if(B===f){N++;continue}if(H!==true&&B===u&&(B=advance())===u){T=j.isBrace=true;w=j.isGlob=true;M=true;if(v===true){continue}break}if(H!==true&&B===a){T=j.isBrace=true;w=j.isGlob=true;M=true;if(v===true){continue}break}if(B===g){N--;if(N===0){H=false;T=j.isBrace=true;M=true;break}}}if(v===true){continue}break}if(B===l){E.push(P);A.push(j);j={value:"",depth:0,isGlob:false};if(M===true)continue;if(I===u&&P===x+1){x+=2;continue}k=P+1;continue}if(r.noext!==true){const t=B===d||B===i||B===s||B===_||B===c;if(t===true&&peek()===p){w=j.isGlob=true;O=j.isExtglob=true;M=true;if(B===c&&P===x){$=true}if(v===true){while(eos()!==true&&(B=advance())){if(B===o){D=j.backslashes=true;B=advance();continue}if(B===y){w=j.isGlob=true;M=true;break}}continue}break}}if(B===s){if(I===s)L=j.isGlobstar=true;w=j.isGlob=true;M=true;if(v===true){continue}break}if(B===_){w=j.isGlob=true;M=true;if(v===true){continue}break}if(B===h){while(eos()!==true&&(t=advance())){if(t===o){D=j.backslashes=true;advance();continue}if(t===S){C=j.isBracket=true;w=j.isGlob=true;M=true;break}}if(v===true){continue}break}if(r.nonegate!==true&&B===c&&P===x){F=j.negated=true;x++;continue}if(r.noparen!==true&&B===p){w=j.isGlob=true;if(v===true){while(eos()!==true&&(B=advance())){if(B===p){D=j.backslashes=true;B=advance();continue}if(B===y){M=true;break}}continue}break}if(w===true){M=true;if(v===true){continue}break}}if(r.noext===true){O=false;w=false}let G=R;let W="";let U="";if(x>0){W=R.slice(0,x);R=R.slice(x);k-=x}if(G&&w===true&&k>0){G=R.slice(0,k);U=R.slice(k)}else if(w===true){G="";U=R}else{G=R}if(G&&G!==""&&G!=="/"&&G!==R){if(isPathSeparator(G.charCodeAt(G.length-1))){G=G.slice(0,-1)}}if(r.unescape===true){if(U)U=n.removeBackslashes(U);if(G&&D===true){G=n.removeBackslashes(G)}}const K={prefix:W,input:t,start:x,base:G,glob:U,isBrace:T,isBracket:C,isGlob:w,isExtglob:O,isGlobstar:L,negated:F,negatedExtglob:$};if(r.tokens===true){K.maxDepth=0;if(!isPathSeparator(B)){A.push(j)}K.tokens=A}if(r.parts===true||r.tokens===true){let e;for(let n=0;n<E.length;n++){const s=e?e+1:x;const i=E[n];const o=t.slice(s,i);if(r.tokens){if(n===0&&x!==0){A[n].isPrefix=true;A[n].value=W}else{A[n].value=o}depth(A[n]);K.maxDepth+=A[n].depth}if(n!==0||o!==""){b.push(o)}e=i}if(e&&e+1<t.length){const n=t.slice(e+1);b.push(n);if(r.tokens){A[A.length-1].value=n;depth(A[A.length-1]);K.maxDepth+=A[A.length-1].depth}}K.slashes=E;K.parts=b}return K};t.exports=scan},8658:(t,e,r)=>{"use strict";const n=r(1017);const s=process.platform==="win32";const{REGEX_BACKSLASH:i,REGEX_REMOVE_BACKSLASH:o,REGEX_SPECIAL_CHARS:a,REGEX_SPECIAL_CHARS_GLOBAL:u}=r(7820);e.isObject=t=>t!==null&&typeof t==="object"&&!Array.isArray(t);e.hasRegexChars=t=>a.test(t);e.isRegexChar=t=>t.length===1&&e.hasRegexChars(t);e.escapeRegex=t=>t.replace(u,"\\$1");e.toPosixSlashes=t=>t.replace(i,"/");e.removeBackslashes=t=>t.replace(o,(t=>t==="\\"?"":t));e.supportsLookbehinds=()=>{const t=process.version.slice(1).split(".").map(Number);if(t.length===3&&t[0]>=9||t[0]===8&&t[1]>=10){return true}return false};e.isWindows=t=>{if(t&&typeof t.windows==="boolean"){return t.windows}return s===true||n.sep==="\\"};e.escapeLast=(t,r,n)=>{const s=t.lastIndexOf(r,n);if(s===-1)return t;if(t[s-1]==="\\")return e.escapeLast(t,r,s-1);return`${t.slice(0,s)}\\${t.slice(s)}`};e.removePrefix=(t,e={})=>{let r=t;if(r.startsWith("./")){r=r.slice(2);e.prefix="./"}return r};e.wrapOutput=(t,e={},r={})=>{const n=r.contains?"":"^";const s=r.contains?"":"$";let i=`${n}(?:${t})${s}`;if(e.negated===true){i=`(?:^(?!${i}).*$)`}return i}},8453:t=>{
/*! queue-microtask. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */
let e;t.exports=typeof queueMicrotask==="function"?queueMicrotask.bind(typeof window!=="undefined"?window:global):t=>(e||(e=Promise.resolve())).then(t).catch((t=>setTimeout((()=>{throw t}),0)))},1816:t=>{"use strict";function reusify(t){var e=new t;var r=e;function get(){var n=e;if(n.next){e=n.next}else{e=new t;r=e}n.next=null;return n}function release(t){r.next=t;r=t}return{get:get,release:release}}t.exports=reusify},5341:(t,e,r)=>{
/*! run-parallel. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */
t.exports=runParallel;const n=r(8453);function runParallel(t,e){let r,s,i;let o=true;if(Array.isArray(t)){r=[];s=t.length}else{i=Object.keys(t);r={};s=i.length}function done(t){function end(){if(e)e(t,r);e=null}if(o)n(end);else end()}function each(t,e,n){r[t]=n;if(--s===0||e){done(e)}}if(!s){done(null)}else if(i){i.forEach((function(e){t[e]((function(t,r){each(e,t,r)}))}))}else{t.forEach((function(t,e){t((function(t,r){each(e,t,r)}))}))}o=false}},211:(t,e,r)=>{"use strict";
/*!
 * to-regex-range <https://github.com/micromatch/to-regex-range>
 *
 * Copyright (c) 2015-present, Jon Schlinkert.
 * Released under the MIT License.
 */const n=r(8387);const toRegexRange=(t,e,r)=>{if(n(t)===false){throw new TypeError("toRegexRange: expected the first argument to be a number")}if(e===void 0||t===e){return String(t)}if(n(e)===false){throw new TypeError("toRegexRange: expected the second argument to be a number.")}let s={relaxZeros:true,...r};if(typeof s.strictZeros==="boolean"){s.relaxZeros=s.strictZeros===false}let i=String(s.relaxZeros);let o=String(s.shorthand);let a=String(s.capture);let u=String(s.wrap);let c=t+":"+e+"="+i+o+a+u;if(toRegexRange.cache.hasOwnProperty(c)){return toRegexRange.cache[c].result}let l=Math.min(t,e);let f=Math.max(t,e);if(Math.abs(l-f)===1){let r=t+"|"+e;if(s.capture){return`(${r})`}if(s.wrap===false){return r}return`(?:${r})`}let p=hasPadding(t)||hasPadding(e);let h={min:t,max:e,a:l,b:f};let d=[];let _=[];if(p){h.isPadded=p;h.maxLen=String(h.max).length}if(l<0){let t=f<0?Math.abs(f):1;_=splitToPatterns(t,Math.abs(l),h,s);l=h.a=0}if(f>=0){d=splitToPatterns(l,f,h,s)}h.negatives=_;h.positives=d;h.result=collatePatterns(_,d,s);if(s.capture===true){h.result=`(${h.result})`}else if(s.wrap!==false&&d.length+_.length>1){h.result=`(?:${h.result})`}toRegexRange.cache[c]=h;return h.result};function collatePatterns(t,e,r){let n=filterPatterns(t,e,"-",false,r)||[];let s=filterPatterns(e,t,"",false,r)||[];let i=filterPatterns(t,e,"-?",true,r)||[];let o=n.concat(i).concat(s);return o.join("|")}function splitToRanges(t,e){let r=1;let n=1;let s=countNines(t,r);let i=new Set([e]);while(t<=s&&s<=e){i.add(s);r+=1;s=countNines(t,r)}s=countZeros(e+1,n)-1;while(t<s&&s<=e){i.add(s);n+=1;s=countZeros(e+1,n)-1}i=[...i];i.sort(compare);return i}function rangeToPattern(t,e,r){if(t===e){return{pattern:t,count:[],digits:0}}let n=zip(t,e);let s=n.length;let i="";let o=0;for(let t=0;t<s;t++){let[e,s]=n[t];if(e===s){i+=e}else if(e!=="0"||s!=="9"){i+=toCharacterClass(e,s,r)}else{o++}}if(o){i+=r.shorthand===true?"\\d":"[0-9]"}return{pattern:i,count:[o],digits:s}}function splitToPatterns(t,e,r,n){let s=splitToRanges(t,e);let i=[];let o=t;let a;for(let t=0;t<s.length;t++){let e=s[t];let u=rangeToPattern(String(o),String(e),n);let c="";if(!r.isPadded&&a&&a.pattern===u.pattern){if(a.count.length>1){a.count.pop()}a.count.push(u.count[0]);a.string=a.pattern+toQuantifier(a.count);o=e+1;continue}if(r.isPadded){c=padZeros(e,r,n)}u.string=c+u.pattern+toQuantifier(u.count);i.push(u);o=e+1;a=u}return i}function filterPatterns(t,e,r,n,s){let i=[];for(let s of t){let{string:t}=s;if(!n&&!contains(e,"string",t)){i.push(r+t)}if(n&&contains(e,"string",t)){i.push(r+t)}}return i}function zip(t,e){let r=[];for(let n=0;n<t.length;n++)r.push([t[n],e[n]]);return r}function compare(t,e){return t>e?1:e>t?-1:0}function contains(t,e,r){return t.some((t=>t[e]===r))}function countNines(t,e){return Number(String(t).slice(0,-e)+"9".repeat(e))}function countZeros(t,e){return t-t%Math.pow(10,e)}function toQuantifier(t){let[e=0,r=""]=t;if(r||e>1){return`{${e+(r?","+r:"")}}`}return""}function toCharacterClass(t,e,r){return`[${t}${e-t===1?"":"-"}${e}]`}function hasPadding(t){return/^-?(0+)\d/.test(t)}function padZeros(t,e,r){if(!e.isPadded){return t}let n=Math.abs(e.maxLen-String(t).length);let s=r.relaxZeros!==false;switch(n){case 0:return"";case 1:return s?"0?":"0";case 2:return s?"0{0,2}":"00";default:{return s?`0{0,${n}}`:`0{${n}}`}}}toRegexRange.cache={};toRegexRange.clearCache=()=>toRegexRange.cache={};t.exports=toRegexRange},2361:t=>{"use strict";t.exports=require("events")},7147:t=>{"use strict";t.exports=require("fs")},2037:t=>{"use strict";t.exports=require("os")},1017:t=>{"use strict";t.exports=require("path")},2781:t=>{"use strict";t.exports=require("stream")},3837:t=>{"use strict";t.exports=require("util")}};var e={};function __nccwpck_require__(r){var n=e[r];if(n!==undefined){return n.exports}var s=e[r]={exports:{}};var i=true;try{t[r](s,s.exports,__nccwpck_require__);i=false}finally{if(i)delete e[r]}return s.exports}if(typeof __nccwpck_require__!=="undefined")__nccwpck_require__.ab=__dirname+"/";var r=__nccwpck_require__(3924);module.exports=r})();