"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var watch_exports = {};
__export(watch_exports, {
  WatchChangeType: () => WatchChangeType,
  watch: () => watch
});
module.exports = __toCommonJS(watch_exports);
var import_path = __toESM(require("path"));
var import_compiled = require("../compiled");
const WatchChangeType = {
  ADD: "add",
  UNLINK: "unlink",
  CHANGE: "change"
};
const watch = (watchDir, runTask, ignored = []) => {
  let ready = false;
  const watcher = import_compiled.chokidar.watch(watchDir, {
    ignored
  });
  watcher.on("ready", () => ready = true);
  watcher.on("change", async (filePath) => {
    const changedFilePath = import_path.default.resolve(filePath);
    await runTask({
      changedFilePath,
      changeType: WatchChangeType.CHANGE
    });
  });
  watcher.on("add", async (filePath) => {
    const changedFilePath = import_path.default.resolve(filePath);
    if (ready) {
      await runTask({
        changedFilePath,
        changeType: WatchChangeType.ADD
      });
    }
  });
  watcher.on("unlink", async (filePath) => {
    const changedFilePath = import_path.default.resolve(filePath);
    await runTask({
      changedFilePath,
      changeType: WatchChangeType.UNLINK
    });
  });
  watcher.on("error", (err) => {
    throw err;
  });
  return watcher;
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  WatchChangeType,
  watch
});
