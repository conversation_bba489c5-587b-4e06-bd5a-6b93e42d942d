// Type definitions for debug 4.1
// Project: https://github.com/visionmedia/debug
// Definitions by: <PERSON><PERSON>-<PERSON><PERSON> <https://github.com/swook>
//                 Gal Talmor <https://github.com/galtalmor>
//                 <PERSON> <https://github.com/zamb3zi>
//                 <PERSON><PERSON><PERSON> <https://github.com/brasten>
//                 <PERSON> <https://github.com/npenin>
//                 <PERSON><PERSON> <https://github.com/kristianmitk>
//                 <PERSON> <https://github.com/calebgregory>
// Definitions: https://github.com/DefinitelyTyped/DefinitelyTyped

declare var debug: debug.Debug & { debug: debug.Debug; default: debug.Debug };

export = debug;
export as namespace debug;

declare namespace debug {
    interface Debug {
        (namespace: string): Debugger;
        coerce: (val: any) => any;
        disable: () => string;
        enable: (namespaces: string) => void;
        enabled: (namespaces: string) => boolean;
        formatArgs: (this: Debugger, args: any[]) => void;
        log: (...args: any[]) => any;
        selectColor: (namespace: string) => string | number;
        humanize: typeof import('./ms');

        names: RegExp[];
        skips: RegExp[];

        formatters: Formatters;
    }

    type IDebug = Debug;

    interface Formatters {
        [formatter: string]: (v: any) => string;
    }

    type IDebugger = Debugger;

    interface Debugger {
        (formatter: any, ...args: any[]): void;

        color: string;
        diff: number;
        enabled: boolean;
        log: (...args: any[]) => any;
        namespace: string;
        destroy: () => boolean;
        extend: (namespace: string, delimiter?: string) => Debugger;
    }
}
