(()=>{var e={608:(e,r,n)=>{const t=n(147);const o=n(17);const s=n(37);function log(e){console.log(`[dotenv][DEBUG] ${e}`)}const i="\n";const c=/^\s*([\w.-]+)\s*=\s*(.*)?\s*$/;const l=/\\n/g;const a=/\r\n|\n|\r/;function parse(e,r){const n=Boolean(r&&r.debug);const t={};e.toString().split(a).forEach((function(e,r){const o=e.match(c);if(o!=null){const e=o[1];let r=o[2]||"";const n=r.length-1;const s=r[0]==='"'&&r[n]==='"';const c=r[0]==="'"&&r[n]==="'";if(c||s){r=r.substring(1,n);if(s){r=r.replace(l,i)}}else{r=r.trim()}t[e]=r}else if(n){log(`did not match key and value when parsing line ${r+1}: ${e}`)}}));return t}function resolveHome(e){return e[0]==="~"?o.join(s.homedir(),e.slice(1)):e}function config(e){let r=o.resolve(process.cwd(),".env");let n="utf8";let s=false;if(e){if(e.path!=null){r=resolveHome(e.path)}if(e.encoding!=null){n=e.encoding}if(e.debug!=null){s=true}}try{const e=parse(t.readFileSync(r,{encoding:n}),{debug:s});Object.keys(e).forEach((function(r){if(!Object.prototype.hasOwnProperty.call(process.env,r)){process.env[r]=e[r]}else if(s){log(`"${r}" is already defined in \`process.env\` and will not be overwritten`)}}));return{parsed:e}}catch(e){return{error:e}}}e.exports.config=config;e.exports.parse=parse},147:e=>{"use strict";e.exports=require("fs")},37:e=>{"use strict";e.exports=require("os")},17:e=>{"use strict";e.exports=require("path")}};var r={};function __nccwpck_require__(n){var t=r[n];if(t!==undefined){return t.exports}var o=r[n]={exports:{}};var s=true;try{e[n](o,o.exports,__nccwpck_require__);s=false}finally{if(s)delete r[n]}return o.exports}if(typeof __nccwpck_require__!=="undefined")__nccwpck_require__.ab=__dirname+"/";var n=__nccwpck_require__(608);module.exports=n})();