(()=>{"use strict";var e={42:e=>{const lazy=(e,r,t)=>e===undefined?r(t):e;e.exports=e=>r=>{let t;const _={get:(_,n)=>{t=lazy(t,e,r);return Reflect.get(t,n)},apply:(_,n,u)=>{t=lazy(t,e,r);return Reflect.apply(t,n,u)},construct:(_,n)=>{t=lazy(t,e,r);return Reflect.construct(t,n)}};return new Proxy((function(){}),_)}}};var r={};function __nccwpck_require__(t){var _=r[t];if(_!==undefined){return _.exports}var n=r[t]={exports:{}};var u=true;try{e[t](n,n.exports,__nccwpck_require__);u=false}finally{if(u)delete r[t]}return n.exports}if(typeof __nccwpck_require__!=="undefined")__nccwpck_require__.ab=__dirname+"/";var t=__nccwpck_require__(42);module.exports=t})();