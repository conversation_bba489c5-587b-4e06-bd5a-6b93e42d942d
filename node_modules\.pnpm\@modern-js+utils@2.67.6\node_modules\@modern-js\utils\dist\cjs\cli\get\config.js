"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var config_exports = {};
__export(config_exports, {
  getEntryOptions: () => getEntryOptions
});
module.exports = __toCommonJS(config_exports);
var import_is = require("../is");
const getEntryOptions = (name, isMainEntry, baseOptions, optionsByEntries, packageName) => {
  if (optionsByEntries) {
    let optionsByEntry = getOptionsByEntryName(name, optionsByEntries);
    if (optionsByEntry === void 0 && isMainEntry && packageName) {
      optionsByEntry = getOptionsByEntryName(packageName, optionsByEntries);
    }
    return optionsByEntry !== void 0 ? (0, import_is.isPlainObject)(optionsByEntry) && (0, import_is.isPlainObject)(baseOptions) ? {
      ...baseOptions,
      ...optionsByEntry
    } : optionsByEntry : baseOptions;
  } else {
    return baseOptions;
  }
};
const getOptionsByEntryName = (name, optionsByEntries) => optionsByEntries.hasOwnProperty(name) ? optionsByEntries[name] : void 0;
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  getEntryOptions
});
