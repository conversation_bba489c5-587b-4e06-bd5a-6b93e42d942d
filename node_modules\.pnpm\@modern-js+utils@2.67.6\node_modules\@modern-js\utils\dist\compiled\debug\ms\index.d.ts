// Type definitions for ms v0.7.1
// Project: https://github.com/zeit/ms
// Definitions by: <PERSON><PERSON><PERSON> <https://github.com/danny8002>
// Definitions: https://github.com/DefinitelyTyped/DefinitelyTyped



/**
* Short/Long format for `value`.
*
* @param {Number} value
* @param {{long: boolean}} options
* @return {String}
*/
declare function ms(value: number, options?: { long: boolean }): string;

/**
* Parse the given `value` and return milliseconds.
*
* @param {String} value
* @return {Number}
*/
declare function ms(value: string): number;

export = ms;
